#!/usr/bin/env python3
"""
LTESocks端口重置定时任务安装脚本
"""

import os
import sys
import shutil
import subprocess
import platform

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必需的模块
    required_modules = ['requests', 'pydantic']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 已安装")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module} 未安装")
    
    if missing_modules:
        print(f"\n📦 安装缺失的模块: {', '.join(missing_modules)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_modules)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False
    
    return True

def check_files():
    """检查必需文件"""
    print("\n📁 检查必需文件...")
    
    required_files = [
        'ltesocks_client.py',
        'ltesocks_port_reset_scheduler.py',
        'start_port_reset_scheduler.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} 不存在")
            return False
    
    return True

def install_as_service():
    """安装为系统服务（Linux）"""
    if platform.system() != 'Linux':
        print("⚠️ 系统服务安装仅支持Linux")
        return False
    
    print("\n🔧 安装为系统服务...")
    
    try:
        # 创建安装目录
        install_dir = '/opt/ltesocks-scheduler'
        if not os.path.exists(install_dir):
            os.makedirs(install_dir)
            print(f"✅ 创建目录: {install_dir}")
        
        # 复制文件
        files_to_copy = [
            'ltesocks_client.py',
            'ltesocks_port_reset_scheduler.py',
            'start_port_reset_scheduler.py',
            'scheduler_config.json'
        ]
        
        for file in files_to_copy:
            if os.path.exists(file):
                shutil.copy2(file, install_dir)
                print(f"✅ 复制文件: {file}")
        
        # 复制服务文件
        service_file = '/etc/systemd/system/ltesocks-scheduler.service'
        if os.path.exists('ltesocks-scheduler.service'):
            shutil.copy2('ltesocks-scheduler.service', service_file)
            print(f"✅ 安装服务文件: {service_file}")
        
        # 重新加载systemd
        subprocess.check_call(['systemctl', 'daemon-reload'])
        print("✅ 重新加载systemd")
        
        # 启用服务
        subprocess.check_call(['systemctl', 'enable', 'ltesocks-scheduler'])
        print("✅ 启用服务")
        
        print(f"\n🎉 安装完成！")
        print(f"启动服务: sudo systemctl start ltesocks-scheduler")
        print(f"查看状态: sudo systemctl status ltesocks-scheduler")
        print(f"查看日志: sudo journalctl -u ltesocks-scheduler -f")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except PermissionError:
        print("❌ 权限不足，请使用sudo运行")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 LTESocks端口重置定时任务安装程序")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        sys.exit(1)
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败")
        sys.exit(1)
    
    print("\n✅ 所有检查通过！")
    
    # 询问是否安装为系统服务
    if platform.system() == 'Linux':
        choice = input("\n是否安装为系统服务？(y/N): ").lower()
        if choice == 'y':
            if install_as_service():
                print("\n🎉 系统服务安装成功！")
            else:
                print("\n❌ 系统服务安装失败")
                sys.exit(1)
        else:
            print("\n📝 手动运行说明:")
            print("python start_port_reset_scheduler.py")
    else:
        print("\n📝 运行说明:")
        if platform.system() == 'Windows':
            print("双击运行: start_scheduler.bat")
        print("或使用命令: python start_port_reset_scheduler.py")
    
    print("\n✅ 安装完成！")

if __name__ == "__main__":
    main()
