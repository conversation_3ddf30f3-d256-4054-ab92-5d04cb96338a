#!/usr/bin/env python3
"""
SeleniumBase优化版批量爬取器
基于控制台日志分析的优化版本
解决Cloudflare检测、重复数据、性能等问题
"""

import os
import sys
import time
import logging
import traceback
import re
import random
from datetime import datetime
from typing import List, Dict, Tuple, Set
import csv
from pathlib import Path

from seleniumbase import SB
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seleniumbase_optimized_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class OptimizedSeleniumBaseScraper:
    """优化版SeleniumBase爬取器"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data"):
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None,
            'cloudflare_blocks': 0,
            'button_not_found': 0,
            'relatives_extracted': 0
        }

        # 已处理记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()

        # CSV文件
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_initialized = False

        # 智能延迟控制
        self.last_request_time = 0
        self.min_delay = 1.5
        self.max_delay = 4.0
        self.adaptive_delay = 2.0
        self.success_count = 0
        self.fail_count = 0

        # 优化参数
        self.max_relatives_per_phone = 15  # 限制每个号码处理的亲属数量
        self.cloudflare_retry_limit = 2    # Cloudflare重试次数限制
        self.page_load_timeout = 8         # 页面加载超时
        
        logging.info(f"优化版SeleniumBase爬取器初始化完成")
        logging.info(f"最大亲属处理数量: {self.max_relatives_per_phone}")
        logging.info(f"Cloudflare重试限制: {self.cloudflare_retry_limit}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")

    def init_unified_csv(self):
        """初始化统一的CSV文件"""
        if not self.csv_initialized:
            try:
                if not self.unified_csv_file.exists():
                    with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                    logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                else:
                    logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
                self.csv_initialized = True
            except Exception as e:
                logging.error(f"初始化CSV文件失败: {e}")

    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """立即保存数据到CSV文件"""
        try:
            self.init_unified_csv()
            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    data_type,
                    info.get('姓名', ''),
                    info.get('年龄', ''),
                    info.get('手机', ''),
                    info.get('地址', ''),
                    info.get('关系', data_type),
                    info.get('链接', '')
                ])
            return True
        except Exception as e:
            logging.error(f"保存{data_type}信息失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """从文件中加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                        
                        # 验证电话号码格式（10位数字）
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def calculate_adaptive_delay(self, success: bool) -> float:
        """计算自适应延迟时间"""
        current_time = time.time()
        
        if success:
            self.success_count += 1
            self.fail_count = 0
            # 连续成功时逐渐减少延迟
            if self.success_count >= 2:
                self.adaptive_delay = max(self.min_delay, self.adaptive_delay * 0.95)
        else:
            self.fail_count += 1
            self.success_count = 0
            # 失败时增加延迟
            self.adaptive_delay = min(self.max_delay * 1.5, self.adaptive_delay * 1.3)
        
        # 确保最小间隔
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_delay:
            additional_delay = self.min_delay - time_since_last
        else:
            additional_delay = 0
        
        # 计算总延迟
        base_delay = random.uniform(self.adaptive_delay * 0.9, self.adaptive_delay * 1.1)
        total_delay = base_delay + additional_delay
        
        # 记录请求时间
        self.last_request_time = current_time + total_delay
        
        return total_delay

    def is_cloudflare_blocked(self, page_source: str) -> bool:
        """检测是否被Cloudflare阻止"""
        cloudflare_indicators = [
            "access denied",
            "just a moment",
            "checking your browser",
            "cloudflare",
            "please wait",
            "ray id"
        ]
        
        page_lower = page_source.lower()
        return any(indicator in page_lower for indicator in cloudflare_indicators)

    def find_background_report_button_optimized(self, sb) -> Tuple[bool, str]:
        """优化的背景报告按钮查找"""
        try:
            # 等待页面基本加载
            sb.wait_for_ready_state_complete(timeout=self.page_load_timeout)
            
            # 获取页面源码
            page_source = sb.get_page_source()
            
            # 检查Cloudflare阻止
            if self.is_cloudflare_blocked(page_source):
                logging.warning("⚠️ 检测到Cloudflare阻止，跳过此页面")
                self.stats['cloudflare_blocks'] += 1
                return False, ""
            
            # 使用BeautifulSoup解析
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 查找包含"Free Background Report"的链接
            target_texts = [
                "open free background report",
                "free background report",
                "background report",
                "view report"
            ]
            
            for link in soup.find_all('a', href=True):
                link_text = link.get_text().strip().lower()
                href = link.get('href', '')
                
                # 检查文本匹配
                if any(text in link_text for text in target_texts):
                    # 确保是people页面的链接
                    if '/people/' in href:
                        if href.startswith('/'):
                            base_url = sb.get_current_url().split('/')[0] + '//' + sb.get_current_url().split('/')[2]
                            href = base_url + href
                        logging.info(f"✅ 找到背景报告链接: {href}")
                        return True, href
            
            # 如果没找到，尝试查找任何people链接
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if '/people/' in href and len(href) > 20:  # 确保是有效的people链接
                    if href.startswith('/'):
                        base_url = sb.get_current_url().split('/')[0] + '//' + sb.get_current_url().split('/')[2]
                        href = base_url + href
                    logging.info(f"🔍 找到备选people链接: {href}")
                    return True, href
            
            logging.warning("❌ 未找到任何有效的背景报告链接")
            self.stats['button_not_found'] += 1
            return False, ""
            
        except Exception as e:
            logging.error(f"查找背景报告按钮失败: {e}")
            return False, ""

    def extract_person_info_optimized(self, soup) -> Dict:
        """优化的个人信息提取"""
        person_info = {}
        
        try:
            # 检查是否是Cloudflare页面
            page_text = soup.get_text()
            if self.is_cloudflare_blocked(page_text):
                logging.warning("⚠️ 个人信息页面被Cloudflare阻止")
                return {}
            
            # 查找姓名 - 更精确的选择器
            name_selectors = [
                'h1.name', 'h1[class*="name"]', '.person-name', '.full-name',
                'h1', 'h2.name', '.profile-name', '.person-header h1'
            ]
            
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem and name_elem.get_text().strip():
                    name_text = name_elem.get_text().strip()
                    # 过滤掉明显的错误文本
                    if len(name_text) < 100 and not any(word in name_text.lower() for word in ['access denied', 'cloudflare', 'just a moment']):
                        person_info['姓名'] = name_text
                        break
            
            # 查找年龄
            age_pattern = r'(?:age|年龄)[\s:]*(\d{1,3})'
            age_match = re.search(age_pattern, page_text, re.IGNORECASE)
            if age_match:
                person_info['年龄'] = age_match.group(1)
            
            # 查找手机号码
            phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
            phone_match = re.search(phone_pattern, page_text)
            if phone_match:
                person_info['手机'] = phone_match.group(0)
            
            # 查找地址
            address_selectors = [
                '.address', '[class*="address"]', '.location', '.city-state'
            ]
            
            for selector in address_selectors:
                addr_elem = soup.select_one(selector)
                if addr_elem and addr_elem.get_text().strip():
                    addr_text = addr_elem.get_text().strip()
                    if len(addr_text) < 200:  # 合理的地址长度
                        person_info['地址'] = addr_text
                        break
                        
        except Exception as e:
            logging.error(f"提取个人信息失败: {e}")
        
        return person_info

    def extract_relatives_info_optimized(self, soup, min_age: int = 40) -> List[Dict]:
        """优化的亲属信息提取 - 去重和限制数量"""
        relatives = []
        seen_names = set()  # 用于去重
        
        try:
            # 查找亲属相关的容器
            relative_containers = soup.find_all(['div', 'section'], class_=re.compile(r'relative|family|associate', re.I))
            
            for container in relative_containers:
                # 查找个人条目
                person_items = container.find_all(['div', 'li'], class_=re.compile(r'person|member|item', re.I))
                
                for item in person_items:
                    if len(relatives) >= self.max_relatives_per_phone:
                        logging.info(f"⚠️ 已达到最大亲属处理数量限制: {self.max_relatives_per_phone}")
                        break
                    
                    try:
                        item_text = item.get_text()
                        
                        # 提取姓名
                        name_elem = item.find(['a', 'span', 'div'], string=re.compile(r'^[A-Za-z\s]{2,30}$'))
                        if not name_elem:
                            name_elem = item.find('a')
                        
                        if name_elem:
                            name = name_elem.get_text().strip()
                            
                            # 去重检查
                            if name in seen_names or len(name) < 2:
                                continue
                            
                            # 提取年龄
                            age_match = re.search(r'(\d{1,3})', item_text)
                            if age_match:
                                age = int(age_match.group(1))
                                if age < min_age or age > 120:  # 合理的年龄范围
                                    continue
                                
                                # 提取链接
                                link_elem = item.find('a', href=True)
                                link_url = ""
                                if link_elem:
                                    link_url = link_elem.get('href', '')
                                    if link_url.startswith('/'):
                                        link_url = "https://www.smartbackgroundchecks.com" + link_url
                                
                                relative_info = {
                                    '姓名': name,
                                    '年龄': str(age),
                                    '关系': '亲属',
                                    '链接': link_url
                                }
                                
                                relatives.append(relative_info)
                                seen_names.add(name)
                                logging.debug(f"✅ 找到符合条件的亲属: {name}, {age}岁")
                    
                    except Exception as e:
                        logging.debug(f"处理亲属条目失败: {e}")
                        continue
                
                if len(relatives) >= self.max_relatives_per_phone:
                    break
                    
        except Exception as e:
            logging.error(f"提取亲属信息失败: {e}")
        
        logging.info(f"📊 共找到 {len(relatives)} 个符合条件的亲属（年龄>={min_age}，已去重）")
        return relatives

    def get_relative_details_optimized(self, sb, relative_name: str, relative_url: str) -> Dict:
        """优化的亲属详细信息获取"""
        details = {'手机': '', '地址': ''}

        if not relative_url:
            return details

        try:
            # 访问亲属页面
            sb.uc_open_with_reconnect(relative_url, reconnect_time=2)
            sb.wait_for_ready_state_complete(timeout=5)

            # 获取页面源码
            page_source = sb.get_page_source()

            # 检查Cloudflare阻止
            if self.is_cloudflare_blocked(page_source):
                logging.warning(f"⚠️ 亲属页面被Cloudflare阻止: {relative_name}")
                return details

            # 解析页面
            soup = BeautifulSoup(page_source, 'html.parser')
            person_info = self.extract_person_info_optimized(soup)

            details['手机'] = person_info.get('手机', '')
            details['地址'] = person_info.get('地址', '')

            if details['手机'] or details['地址']:
                logging.info(f"✅ 获取到亲属详细信息: {relative_name}")
            else:
                logging.debug(f"ℹ️ 未获取到亲属联系信息: {relative_name}")

        except Exception as e:
            logging.warning(f"获取亲属详细信息失败: {relative_name} - {e}")

        return details

    def scrape_phone_data_optimized(self, sb, phone: str) -> Tuple[bool, Dict]:
        """
        优化的电话号码数据爬取
        """
        start_time = time.time()
        logging.info("=" * 80)
        logging.info(f"🔍 开始处理电话号码: {phone}")

        try:
            # 智能延迟
            delay = self.calculate_adaptive_delay(True)
            logging.info(f"⏰ 智能延迟: {delay:.2f}秒 (自适应延迟: {self.adaptive_delay:.2f})")
            sb.sleep(delay)

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"目标URL: {target_url}")

            # 使用UC Mode打开页面
            sb.uc_open_with_reconnect(target_url, reconnect_time=3)

            # 查找背景报告按钮
            button_found, button_url = self.find_background_report_button_optimized(sb)

            if not button_found:
                logging.warning(f"❌ 未找到背景报告按钮: {phone}")
                self.calculate_adaptive_delay(False)
                return False, {}

            logging.info(f"✅ 找到背景报告链接: {button_url}")

            # 访问背景报告页面
            sb.uc_open_with_reconnect(button_url, reconnect_time=2)
            sb.wait_for_ready_state_complete(timeout=self.page_load_timeout)
            sb.sleep(1)  # 短暂等待内容加载

            # 获取页面源码并解析
            page_source = sb.get_page_source()

            # 检查Cloudflare阻止
            if self.is_cloudflare_blocked(page_source):
                logging.warning(f"⚠️ 背景报告页面被Cloudflare阻止: {phone}")
                self.stats['cloudflare_blocks'] += 1
                self.calculate_adaptive_delay(False)
                return False, {}

            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取个人信息
            person_info = self.extract_person_info_optimized(soup)
            if not person_info.get('姓名'):
                logging.warning(f"❌ 未提取到个人信息: {phone}")
                self.calculate_adaptive_delay(False)
                return False, {}

            # 保存个人信息
            self.save_to_csv(phone, '本人', person_info)
            logging.info(f"✅ 已保存个人信息: {person_info.get('姓名', 'N/A')}")

            # 提取亲属信息
            relatives_info = self.extract_relatives_info_optimized(soup, min_age=40)

            if not relatives_info:
                logging.info(f"ℹ️ 未找到符合条件的亲属: {phone}")
                # 即使没有亲属，个人信息成功也算成功
                self.calculate_adaptive_delay(True)
                return True, {
                    'phone': phone,
                    'person_info': person_info,
                    'relatives_count': 0,
                    'duration': time.time() - start_time
                }

            # 处理亲属信息（限制数量和时间）
            detailed_relatives_count = 0
            max_processing_time = 30  # 最大处理时间30秒

            for i, relative in enumerate(relatives_info):
                # 检查处理时间限制
                if time.time() - start_time > max_processing_time:
                    logging.warning(f"⚠️ 达到最大处理时间限制，停止处理亲属")
                    break

                relative_name = relative.get('姓名', '')
                relative_url = relative.get('链接', '')

                if relative_name:
                    try:
                        logging.info(f"处理亲属 {i+1}/{len(relatives_info)}: {relative_name}")

                        # 获取亲属详细信息（有超时控制）
                        relative_details = self.get_relative_details_optimized(sb, relative_name, relative_url)

                        combined_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': relative_details.get('手机', ''),
                            '地址': relative_details.get('地址', '')
                        }

                        # 保存亲属信息
                        if self.save_to_csv(phone, '亲属', combined_info):
                            detailed_relatives_count += 1
                            self.stats['relatives_extracted'] += 1

                        # 智能延迟
                        has_contact_info = combined_info.get('手机') or combined_info.get('地址')
                        if has_contact_info:
                            sb.sleep(random.uniform(0.3, 0.6))
                        else:
                            sb.sleep(random.uniform(0.1, 0.3))

                    except Exception as e:
                        logging.warning(f"处理亲属 {relative_name} 失败: {e}")
                        # 保存基本信息
                        basic_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': '',
                            '地址': ''
                        }
                        self.save_to_csv(phone, '亲属', basic_info)

            duration = time.time() - start_time
            logging.info(f"✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"成功保存亲属数量: {detailed_relatives_count}")

            # 更新成功状态
            self.calculate_adaptive_delay(True)

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"错误详情: {e}")

            # 更新失败状态
            self.calculate_adaptive_delay(False)

            return False, {}

    def run_optimized_batch_scraping(self, max_phones: int = None,
                                   base_delay: float = 1.5) -> Dict:
        """
        运行优化版批量爬取
        """
        self.stats['start_time'] = datetime.now()
        self.min_delay = base_delay
        self.max_delay = base_delay * 2.5
        self.adaptive_delay = base_delay

        logging.info("🚀 开始优化版SeleniumBase批量爬取任务")
        logging.info(f"基础延迟: {base_delay}秒")
        logging.info(f"最大亲属处理数量: {self.max_relatives_per_phone}")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 使用SeleniumBase UC Mode
        try:
            with SB(uc=True, incognito=True, test=True,
                   ad_block=True, block_images=True,
                   page_load_strategy="none") as sb:

                logging.info("✅ SeleniumBase UC Mode 初始化成功")

                # 处理每个电话号码
                for i, phone in enumerate(unprocessed_phones, 1):
                    self.stats['processed_phones'] = i

                    logging.info(f"\n📞 处理进度: {i}/{len(unprocessed_phones)} ({i/len(unprocessed_phones)*100:.1f}%)")

                    try:
                        success, result_data = self.scrape_phone_data_optimized(sb, phone)

                        if success:
                            self.stats['successful_scrapes'] += 1
                            self.save_processed_phone(phone)
                            logging.info(f"✅ 成功处理: {phone}")
                        else:
                            self.stats['failed_scrapes'] += 1
                            logging.error(f"❌ 处理失败: {phone}")

                    except Exception as e:
                        self.stats['failed_scrapes'] += 1
                        logging.error(f"❌ 处理电话号码 {phone} 时发生异常: {e}")

                    # 打印当前统计
                    self.print_stats()

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在优雅退出...")
        except Exception as e:
            logging.error(f"优化版批量处理过程中发生严重错误: {e}")

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 优化版SeleniumBase批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        logging.info(f"Cloudflare阻止: {self.stats['cloudflare_blocks']}")
        logging.info(f"按钮未找到: {self.stats['button_not_found']}")
        logging.info(f"提取亲属数量: {self.stats['relatives_extracted']}")
        if self.stats['processed_phones'] > 0:
            success_rate = self.stats['successful_scrapes']/self.stats['processed_phones']*100
            avg_time = duration / self.stats['processed_phones']
            logging.info(f"成功率: {success_rate:.1f}%")
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
        logging.info(f"最终自适应延迟: {self.adaptive_delay:.2f}秒")
        logging.info("=" * 80)

        return self.stats

    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"CF阻止: {self.stats['cloudflare_blocks']}, 亲属: {self.stats['relatives_extracted']}, "
                        f"自适应延迟: {self.adaptive_delay:.2f}s, 耗时: {elapsed:.0f}秒")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='优化版SeleniumBase批量电话号码爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=float, default=1.5, help='基础延迟时间（秒）')
    parser.add_argument('--max-relatives', type=int, default=15, help='每个号码最大处理亲属数量')

    args = parser.parse_args()

    try:
        # 创建优化版爬取器
        scraper = OptimizedSeleniumBaseScraper(args.phone_file, args.output_dir)

        # 设置最大亲属处理数量
        if args.max_relatives:
            scraper.max_relatives_per_phone = args.max_relatives

        # 运行优化版批量爬取
        stats = scraper.run_optimized_batch_scraping(args.max_phones, args.delay)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
