[Unit]
Description=LTESocks Port Reset Scheduler
Documentation=https://github.com/your-repo/ltesocks-scheduler
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=nobody
Group=nogroup
WorkingDirectory=/opt/ltesocks-scheduler
ExecStart=/usr/bin/python3 /opt/ltesocks-scheduler/start_port_reset_scheduler.py --interval 180
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ltesocks-scheduler

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/ltesocks-scheduler

# 环境变量
Environment=PYTHONPATH=/opt/ltesocks-scheduler
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
