#!/bin/bash

echo "========================================"
echo "LTESocks端口重置定时任务启动器"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查依赖文件
if [ ! -f "ltesocks_port_reset_scheduler.py" ]; then
    echo "错误: 未找到 ltesocks_port_reset_scheduler.py"
    exit 1
fi

if [ ! -f "ltesocks_client.py" ]; then
    echo "错误: 未找到 ltesocks_client.py"
    exit 1
fi

echo "正在启动LTESocks端口重置定时任务..."
echo "每3分钟自动重置一次端口"
echo "按 Ctrl+C 停止任务"
echo

# 启动定时任务
python3 start_port_reset_scheduler.py --interval 180

echo
echo "任务已停止"
