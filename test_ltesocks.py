"""
LTESocks API测试脚本
用于测试LTESocks客户端和API服务器的功能
"""

import requests
import json
import time
from ltesocks_client import LTESocksClient, LTESocksAPIError


def test_client():
    """测试LTESocks客户端"""
    print("=== 测试LTESocks客户端 ===\n")
    
    api_key = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
    client = LTESocksClient(api_key)
    
    try:
        # 测试1: 获取用户信息
        print("1. 测试获取用户信息...")
        user = client.get_user()
        print(f"   ✓ 用户: {user.login}")
        print(f"   ✓ 余额: ${user.balance/100:.2f}")
        print(f"   ✓ 端口: {user.portsCount}/{user.portsLimit}")
        
        # 测试2: 获取端口列表
        print("\n2. 测试获取端口列表...")
        ports_response = client.get_ports()
        print(f"   ✓ 总端口数: {ports_response.total}")
        print(f"   ✓ 当前页端口数: {len(ports_response.data)}")
        
        # 测试3: 获取活跃端口
        print("\n3. 测试获取活跃端口...")
        active_ports = client.get_active_ports()
        print(f"   ✓ 活跃端口数: {len(active_ports)}")
        
        # 测试4: 获取计划列表
        print("\n4. 测试获取计划列表...")
        plans = client.get_plans()
        print(f"   ✓ 可用计划数: {len(plans.plans)}")
        
        # 测试5: 获取服务器列表
        print("\n5. 测试获取服务器列表...")
        servers = client.get_servers()
        print(f"   ✓ VPN服务器数: {len(servers.vpnServers)}")
        print(f"   ✓ 代理服务器数: {len(servers.proxyServers)}")
        
        # 测试6: 如果有端口，测试端口操作
        if active_ports:
            port_id = active_ports[0].port
            print(f"\n6. 测试端口操作 (端口 {port_id})...")
            
            # 获取端口详情
            port_detail = client.get_port(port_id)
            print(f"   ✓ 端口详情: {port_detail.ip} - {port_detail.status}")
            
            # 获取代理信息
            proxy_info = client.get_port_proxy_info(port_id)
            print(f"   ✓ 代理信息: {proxy_info['host']}:{proxy_info['port']}")
            
            # 测试重置端口（谨慎操作）
            confirm = input(f"   是否重置端口 {port_id}? (y/N): ")
            if confirm.lower() == 'y':
                reset_port = client.reset_port(port_id)
                print(f"   ✓ 端口重置成功: {reset_port.status}")
            else:
                print("   - 跳过端口重置")
        
        print("\n✓ 客户端测试完成")
        return True
        
    except LTESocksAPIError as e:
        print(f"   ✗ API错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 未知错误: {e}")
        return False


def test_api_server(base_url="http://localhost:8001"):
    """测试API服务器"""
    print(f"\n=== 测试API服务器 ({base_url}) ===\n")
    
    try:
        # 测试1: 健康检查
        print("1. 测试健康检查...")
        response = requests.get(f"{base_url}/api/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✓ 服务状态: {health_data['status']}")
            print(f"   ✓ API连接: {health_data['api_connection']}")
        else:
            print(f"   ✗ 健康检查失败: {response.status_code}")
            return False
        
        # 测试2: 获取用户信息
        print("\n2. 测试获取用户信息...")
        response = requests.get(f"{base_url}/api/user", timeout=10)
        if response.status_code == 200:
            user_data = response.json()
            print(f"   ✓ 用户: {user_data['login']}")
            print(f"   ✓ 余额: ${user_data['balance']/100:.2f}")
        else:
            print(f"   ✗ 获取用户信息失败: {response.status_code}")
        
        # 测试3: 获取端口列表
        print("\n3. 测试获取端口列表...")
        response = requests.get(f"{base_url}/api/ports", timeout=10)
        if response.status_code == 200:
            ports_data = response.json()
            print(f"   ✓ 总端口数: {ports_data['total']}")
            print(f"   ✓ 当前页端口数: {len(ports_data['data'])}")
        else:
            print(f"   ✗ 获取端口列表失败: {response.status_code}")
        
        # 测试4: 获取活跃端口
        print("\n4. 测试获取活跃端口...")
        response = requests.get(f"{base_url}/api/ports/active", timeout=10)
        if response.status_code == 200:
            active_data = response.json()
            print(f"   ✓ 活跃端口数: {active_data['count']}")
        else:
            print(f"   ✗ 获取活跃端口失败: {response.status_code}")
        
        # 测试5: 获取概览统计
        print("\n5. 测试获取概览统计...")
        response = requests.get(f"{base_url}/api/stats/overview", timeout=10)
        if response.status_code == 200:
            stats_data = response.json()
            print(f"   ✓ 用户: {stats_data['user']['login']}")
            print(f"   ✓ 总端口: {stats_data['ports']['total']}")
            print(f"   ✓ 活跃端口: {stats_data['ports']['active']}")
        else:
            print(f"   ✗ 获取统计信息失败: {response.status_code}")
        
        # 测试6: 获取计划列表
        print("\n6. 测试获取计划列表...")
        response = requests.get(f"{base_url}/api/plans", timeout=10)
        if response.status_code == 200:
            plans_data = response.json()
            print(f"   ✓ 可用计划数: {len(plans_data['plans'])}")
        else:
            print(f"   ✗ 获取计划列表失败: {response.status_code}")
        
        # 测试7: 获取服务器列表
        print("\n7. 测试获取服务器列表...")
        response = requests.get(f"{base_url}/api/servers", timeout=10)
        if response.status_code == 200:
            servers_data = response.json()
            print(f"   ✓ VPN服务器数: {len(servers_data['vpnServers'])}")
            print(f"   ✓ 代理服务器数: {len(servers_data['proxyServers'])}")
        else:
            print(f"   ✗ 获取服务器列表失败: {response.status_code}")
        
        print("\n✓ API服务器测试完成")
        return True
        
    except requests.exceptions.ConnectionError:
        print(f"   ✗ 无法连接到API服务器 ({base_url})")
        print("   提示: 请确保API服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("   ✗ 请求超时")
        return False
    except Exception as e:
        print(f"   ✗ 未知错误: {e}")
        return False


def test_api_endpoints(base_url="http://localhost:8001"):
    """测试特定API端点"""
    print(f"\n=== 测试特定API端点 ===\n")
    
    endpoints = [
        ("/", "根路径"),
        ("/api/health", "健康检查"),
        ("/api/user", "用户信息"),
        ("/api/ports", "端口列表"),
        ("/api/ports/active", "活跃端口"),
        ("/api/plans", "计划列表"),
        ("/api/servers", "服务器列表"),
        ("/api/signatures", "可用签名"),
        ("/api/stats/overview", "概览统计"),
        ("/api/stats/countries", "国家统计"),
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"   ✓ {description} ({endpoint})")
                success_count += 1
            else:
                print(f"   ✗ {description} ({endpoint}) - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ✗ {description} ({endpoint}) - 错误: {e}")
    
    print(f"\n端点测试结果: {success_count}/{total_count} 成功")
    return success_count == total_count


def main():
    """主测试函数"""
    print("LTESocks API测试脚本")
    print("=" * 50)
    
    # 测试客户端
    client_success = test_client()
    
    # 询问是否测试API服务器
    if client_success:
        test_server = input("\n是否测试API服务器? 请确保服务器正在运行 (y/N): ")
        if test_server.lower() == 'y':
            # 测试API服务器
            server_success = test_api_server()
            
            if server_success:
                # 测试所有端点
                test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
