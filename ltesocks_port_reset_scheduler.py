#!/usr/bin/env python3
"""
LTESocks端口重置定时任务
每3分钟自动重置所有活跃的LTESocks端口
"""

import time
import logging
import threading
import signal
import sys
from datetime import datetime
from typing import List, Optional
import json
import os

from ltesocks_client import LTESocksClient, LTESocksAPIError, Port

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ltesocks_scheduler.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class LTESocksPortResetScheduler:
    """LTESocks端口重置定时任务调度器"""
    
    def __init__(self, api_key: str, reset_interval: int = 180):
        """
        初始化调度器
        
        Args:
            api_key: LTESocks API密钥
            reset_interval: 重置间隔（秒），默认180秒（3分钟）
        """
        self.api_key = api_key
        self.reset_interval = reset_interval
        self.client = LTESocksClient(api_key)
        self.running = False
        self.thread = None
        self.reset_count = 0
        self.last_reset_time = None
        self.failed_resets = 0
        
        # 统计信息
        self.stats = {
            'total_resets': 0,
            'successful_resets': 0,
            'failed_resets': 0,
            'ports_reset': 0,
            'start_time': None,
            'last_reset_time': None
        }
        
        logging.info(f"LTESocks端口重置调度器初始化完成")
        logging.info(f"重置间隔: {reset_interval}秒 ({reset_interval/60:.1f}分钟)")
    
    def get_active_ports(self) -> List[Port]:
        """获取所有活跃端口"""
        try:
            ports = self.client.get_active_ports()
            logging.info(f"获取到 {len(ports)} 个活跃端口")
            return ports
        except LTESocksAPIError as e:
            logging.error(f"获取活跃端口失败: {e}")
            return []
    
    def reset_single_port(self, port_id: str) -> bool:
        """重置单个端口"""
        try:
            reset_port = self.client.reset_port(port_id)
            logging.info(f"✅ 端口 {port_id} 重置成功 - 状态: {reset_port.status}")
            return True
        except LTESocksAPIError as e:
            logging.error(f"❌ 端口 {port_id} 重置失败: {e}")
            return False
    
    def reset_all_ports(self) -> dict:
        """重置所有活跃端口"""
        start_time = time.time()
        logging.info("=" * 60)
        logging.info("🔄 开始重置所有活跃端口...")
        
        # 获取活跃端口
        active_ports = self.get_active_ports()
        if not active_ports:
            logging.warning("⚠️ 没有找到活跃端口")
            return {
                'success': False,
                'message': '没有活跃端口',
                'ports_count': 0,
                'successful_count': 0,
                'failed_count': 0,
                'duration': 0
            }
        
        # 重置每个端口
        successful_count = 0
        failed_count = 0
        
        for i, port in enumerate(active_ports, 1):
            port_id = port.port
            logging.info(f"正在重置第 {i}/{len(active_ports)} 个端口: {port_id}")
            
            if self.reset_single_port(port_id):
                successful_count += 1
            else:
                failed_count += 1
            
            # 端口之间添加小延迟
            if i < len(active_ports):
                time.sleep(1)
        
        duration = time.time() - start_time
        
        # 更新统计信息
        self.stats['total_resets'] += 1
        self.stats['successful_resets'] += successful_count
        self.stats['failed_resets'] += failed_count
        self.stats['ports_reset'] += successful_count
        self.stats['last_reset_time'] = datetime.now().isoformat()
        
        result = {
            'success': successful_count > 0,
            'message': f'重置完成: 成功 {successful_count}, 失败 {failed_count}',
            'ports_count': len(active_ports),
            'successful_count': successful_count,
            'failed_count': failed_count,
            'duration': duration
        }
        
        logging.info(f"🎯 重置结果: {result['message']}")
        logging.info(f"⏱️ 耗时: {duration:.2f}秒")
        logging.info("=" * 60)
        
        return result
    
    def _scheduler_loop(self):
        """调度器主循环"""
        logging.info("🚀 定时任务开始运行...")
        self.stats['start_time'] = datetime.now().isoformat()
        
        while self.running:
            try:
                # 执行端口重置
                result = self.reset_all_ports()
                self.last_reset_time = datetime.now()
                
                if result['success']:
                    self.reset_count += 1
                    self.failed_resets = 0  # 重置失败计数
                else:
                    self.failed_resets += 1
                
                # 如果连续失败次数过多，记录警告
                if self.failed_resets >= 3:
                    logging.warning(f"⚠️ 连续 {self.failed_resets} 次重置失败，请检查网络连接和API密钥")
                
                # 等待下次执行
                logging.info(f"⏰ 下次重置时间: {datetime.now().strftime('%H:%M:%S')} + {self.reset_interval}秒")
                
                # 分段等待，以便能够响应停止信号
                wait_time = 0
                while wait_time < self.reset_interval and self.running:
                    time.sleep(1)
                    wait_time += 1
                
            except Exception as e:
                logging.error(f"❌ 调度器运行出错: {e}")
                self.failed_resets += 1
                time.sleep(10)  # 出错后等待10秒再继续
    
    def start(self):
        """启动定时任务"""
        if self.running:
            logging.warning("⚠️ 调度器已经在运行中")
            return
        
        logging.info("🎬 启动LTESocks端口重置定时任务...")
        self.running = True
        self.thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.thread.start()
        logging.info("✅ 定时任务启动成功")
    
    def stop(self):
        """停止定时任务"""
        if not self.running:
            logging.warning("⚠️ 调度器未在运行")
            return
        
        logging.info("🛑 正在停止定时任务...")
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        logging.info("✅ 定时任务已停止")
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            'running': self.running,
            'reset_interval': self.reset_interval,
            'reset_count': self.reset_count,
            'last_reset_time': self.last_reset_time.isoformat() if self.last_reset_time else None,
            'failed_resets': self.failed_resets,
            'stats': self.stats
        }
    
    def print_status(self):
        """打印状态信息"""
        status = self.get_status()
        print("\n" + "=" * 50)
        print("📊 LTESocks端口重置调度器状态")
        print("=" * 50)
        print(f"运行状态: {'🟢 运行中' if status['running'] else '🔴 已停止'}")
        print(f"重置间隔: {status['reset_interval']}秒 ({status['reset_interval']/60:.1f}分钟)")
        print(f"已执行重置: {status['reset_count']}次")
        print(f"连续失败: {status['failed_resets']}次")
        
        if status['last_reset_time']:
            print(f"上次重置: {status['last_reset_time']}")
        
        stats = status['stats']
        if stats['start_time']:
            print(f"\n📈 统计信息:")
            print(f"启动时间: {stats['start_time']}")
            print(f"总重置次数: {stats['total_resets']}")
            print(f"成功重置端口: {stats['ports_reset']}")
            print(f"失败次数: {stats['failed_resets']}")
        
        print("=" * 50)

def signal_handler(signum, frame):
    """信号处理器"""
    global scheduler
    logging.info(f"收到信号 {signum}，正在优雅关闭...")
    if scheduler:
        scheduler.stop()
    sys.exit(0)

def main():
    """主函数"""
    global scheduler
    
    # API密钥
    API_KEY = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
    
    # 重置间隔（秒）
    RESET_INTERVAL = 180  # 3分钟
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 创建调度器
        scheduler = LTESocksPortResetScheduler(API_KEY, RESET_INTERVAL)
        
        # 启动调度器
        scheduler.start()
        
        # 主循环 - 每30秒打印一次状态
        while True:
            time.sleep(30)
            scheduler.print_status()
            
    except KeyboardInterrupt:
        logging.info("收到键盘中断信号...")
    except Exception as e:
        logging.error(f"程序运行出错: {e}")
    finally:
        if scheduler:
            scheduler.stop()

if __name__ == "__main__":
    scheduler = None
    main()
