import time
import logging
import os
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cloudflare_bypass.log', mode='w', encoding='utf-8')
    ]
)

def get_chromium_options(browser_path: str, arguments: list) -> ChromiumOptions:
    """
    Configures and returns Chromium options.

    :param browser_path: Path to the Chromium browser executable.
    :param arguments: List of arguments for the Chromium browser.
    :return: Configured ChromiumOptions instance.
    """
    try:
        options = ChromiumOptions().auto_port()

        # 设置浏览器路径
        if browser_path and os.path.exists(browser_path):
            options.set_paths(browser_path=browser_path)
        else:
            logging.warning(f"浏览器路径不存在: {browser_path}")

        # 添加参数
        for argument in arguments:
            try:
                options.set_argument(argument)
            except Exception as e:
                logging.warning(f"无法设置参数 {argument}: {e}")

        return options

    except Exception as e:
        logging.error(f"创建ChromiumOptions时出错: {e}")
        # 返回基本配置
        options = ChromiumOptions()
        return options

def check_cloudflare_protection_fast(driver):
    """
    快速检查页面是否有Cloudflare保护，不等待所有资源加载完成
    """
    try:
        # 等待很短时间让基本DOM加载
        time.sleep(0.5)

        # 立即获取页面源码进行检查
        page_source = driver.html
        logging.info(f"获取到页面源码，长度: {len(page_source)} 字符")

        # 快速检查页面标题
        title = driver.title.lower() if driver.title else ""
        if "just a moment" in title:
            logging.info("检测到Cloudflare挑战页面 (标题检查)")
            return True, page_source

        # 检查页面源码中的Cloudflare特征
        source_lower = page_source.lower()
        cloudflare_indicators = [
            "cloudflare",
            "just a moment",
            "checking your browser",
            "turnstile",
            "cf-challenge",
            "cf-browser-verification"
        ]

        for indicator in cloudflare_indicators:
            if indicator in source_lower:
                logging.info(f"检测到Cloudflare挑战页面 (源码特征: {indicator})")
                return True, page_source

        # 快速检查关键元素，使用很短的超时时间
        try:
            cf_elements = driver.eles('xpath://div[contains(text(), "Cloudflare") or contains(@class, "cf-")]', timeout=1)
            if cf_elements:
                logging.info("检测到Cloudflare挑战页面 (元素检查)")
                return True, page_source
        except:
            pass  # 忽略超时错误

        try:
            turnstile_elements = driver.eles('xpath://div[contains(@class, "turnstile") or @data-sitekey]', timeout=1)
            if turnstile_elements:
                logging.info("检测到Cloudflare Turnstile验证")
                return True, page_source
        except:
            pass  # 忽略超时错误

        logging.info("未检测到Cloudflare保护")
        return False, page_source

    except Exception as e:
        logging.warning(f"检查Cloudflare保护时出错: {e}")
        # 即使出错也尝试获取页面源码
        try:
            page_source = driver.html
            return False, page_source
        except:
            return False, ""

def find_background_report_link_fast(driver):
    """
    使用BeautifulSoup快速查找背景报告链接，不等待页面完全加载
    """
    try:
        logging.info("正在快速查找 'Open Free Background Report' 按钮...")

        # 立即获取页面源码，不等待完全加载
        page_source = driver.html
        logging.info(f"获取页面源码成功，长度: {len(page_source)} 字符")

        # 使用BeautifulSoup解析
        soup = BeautifulSoup(page_source, 'html.parser')

        # 方法1: 通过class查找按钮 (根据您提供的HTML结构)
        target_buttons = soup.find_all('a', class_='btn btn-primary btn-sm btn-block text-center')

        for button in target_buttons:
            button_text = button.get_text().strip()
            href = button.get('href', '')

            if 'Open Free Background Report' in button_text:
                logging.info(f"通过class找到目标按钮: '{button_text}' -> {href}")

                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href

                logging.info(f"准备直接访问背景报告页面: {href}")
                # 直接访问链接，不点击
                driver.get(href)
                return True, href

        # 方法2: 查找包含"Free Background Report"文本的所有链接
        all_links = soup.find_all('a', href=True)

        for link in all_links:
            link_text = link.get_text().strip()
            href = link.get('href', '')
            title = link.get('title', '')

            # 检查文本内容
            if any(keyword in link_text for keyword in ['Open Free Background Report', 'Free Background Report']):
                logging.info(f"通过文本匹配找到链接: '{link_text}' -> {href}")

                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href

                logging.info(f"准备直接访问背景报告页面: {href}")
                driver.get(href)
                return True, href

            # 检查title属性
            if 'Free Background Report' in title:
                logging.info(f"通过title属性找到链接: '{title}' -> {href}")

                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href

                logging.info(f"准备直接访问背景报告页面: {href}")
                driver.get(href)
                return True, href

        # 方法3: 查找href中包含people路径的链接（背景报告通常在people路径下）
        for link in all_links:
            href = link.get('href', '')
            link_text = link.get_text().strip()

            if '/people/' in href and any(keyword in link_text.lower() for keyword in ['background', 'report']):
                logging.info(f"通过people路径匹配找到链接: '{link_text}' -> {href}")

                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href

                logging.info(f"准备直接访问背景报告页面: {href}")
                driver.get(href)
                return True, href

        logging.warning("未找到 'Open Free Background Report' 按钮")
        return False, None

    except Exception as e:
        logging.error(f"查找背景报告按钮时出错: {e}")
        return False, None

def analyze_page_source(page_source):
    """
    分析页面源码，提取有用信息
    """
    try:
        soup = BeautifulSoup(page_source, 'html.parser')

        # 提取基本信息
        title = soup.title.string if soup.title else "无标题"
        logging.info(f"页面标题: {title}")

        # 查找所有链接
        links = soup.find_all('a', href=True)
        logging.info(f"找到 {len(links)} 个链接")

        # 特别查找背景报告相关链接
        background_report_links = []
        for link in links:
            link_text = link.get_text().lower() if link.get_text() else ""
            href = link.get('href', '')

            if any(keyword in link_text for keyword in ['background', 'report', 'free background']):
                background_report_links.append({
                    'text': link.get_text().strip(),
                    'href': href
                })

        if background_report_links:
            logging.info(f"找到 {len(background_report_links)} 个背景报告相关链接:")
            for i, link in enumerate(background_report_links):
                logging.info(f"  {i+1}. {link['text']} -> {link['href']}")

        # 查找表单
        forms = soup.find_all('form')
        logging.info(f"找到 {len(forms)} 个表单")

        # 查找特定的背景调查相关元素
        background_elements = soup.find_all(string=lambda text: text and any(
            keyword in text.lower() for keyword in ['background', 'report', 'record', 'criminal', 'court']
        ))
        if background_elements:
            logging.info(f"找到 {len(background_elements)} 个背景调查相关文本")

        # 查找按钮
        buttons = soup.find_all(['button', 'input'], type=['button', 'submit'])
        logging.info(f"找到 {len(buttons)} 个按钮")

        # 提取所有文本内容的前500个字符作为预览
        text_content = soup.get_text()[:500]
        logging.info(f"页面文本预览: {text_content}")

        return {
            'title': title,
            'links_count': len(links),
            'forms_count': len(forms),
            'buttons_count': len(buttons),
            'background_elements_count': len(background_elements),
            'background_report_links': background_report_links,
            'text_preview': text_content
        }

    except Exception as e:
        logging.error(f"分析页面源码时出错: {e}")
        return None

def main():
    # Chromium Browser Path
    isHeadless = os.getenv('HEADLESS', 'false').lower() == 'true'
    
    if isHeadless:
        from pyvirtualdisplay import Display

        display = Display(visible=0, size=(1920, 1080))
        display.start()

    # 自动检测浏览器路径
    browser_path = None
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]

    for path in possible_paths:
        if os.path.exists(path):
            browser_path = path
            logging.info(f"找到浏览器: {browser_path}")
            break

    if not browser_path:
        browser_path = os.getenv('CHROME_PATH', r"C:\Program Files\Google\Chrome\Application\chrome.exe")
        logging.warning(f"未找到浏览器，使用默认路径: {browser_path}")

    # Arguments to make the browser better for automation and faster loading.
    arguments = [
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-background-mode",
        "--disable-gpu",
        "--disable-dev-shm-usage",
        "--no-sandbox",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-extensions",
        "--disable-plugins",
        "--disable-images",  # 禁用图片加载以提高速度
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-ipc-flooding-protection",
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]

    options = get_chromium_options(browser_path, arguments)
    
    # 移除不支持的方法调用
    # options.set_page_load_strategy('eager')  # 这行导致了错误
    
    # Initialize the browser
    try:
        logging.info('正在初始化浏览器...')
        driver = ChromiumPage(addr_or_opts=options)
        logging.info('浏览器初始化成功')
    except Exception as e:
        logging.error(f"浏览器初始化失败: {e}")
        return

    try:
        logging.info('正在访问目标页面...')
        # 设置页面加载超时
        driver.set.timeouts(page_load=30)
        driver.get('https://www.smartbackgroundchecks.com/phone/5619324217')
        logging.info('页面访问成功')

        # 快速检查Cloudflare保护并获取页面源码
        has_cloudflare, page_source = check_cloudflare_protection_fast(driver)

        if has_cloudflare:
            logging.info('检测到Cloudflare保护，开始绕过...')
            # 创建CloudflareBypasser实例
            cf_bypasser = CloudflareBypasser(driver)
            cf_bypasser.bypass()

            # 绕过后重新获取页面源码
            logging.info('Cloudflare绕过完成，重新获取页面源码...')
            time.sleep(1)  # 短暂等待
            page_source = driver.html
            logging.info(f"绕过后页面源码长度: {len(page_source)} 字符")
        else:
            logging.info('未检测到Cloudflare保护，直接使用页面源码')

        # 查找并访问"Open Free Background Report"页面
        logging.info("=" * 50)
        logging.info("开始快速查找 'Open Free Background Report' 按钮...")
        button_found, button_url = find_background_report_link_fast(driver)

        if button_found:
            logging.info(f"成功点击背景报告按钮，当前页面: {driver.url}")

            # 等待新页面加载并获取源码
            time.sleep(3)
            new_page_source = driver.html
            logging.info(f"背景报告页面源码长度: {len(new_page_source)} 字符")

            # 保存背景报告页面源码
            with open('background_report_page.html', 'w', encoding='utf-8') as f:
                f.write(new_page_source)
            logging.info("背景报告页面源码已保存到 background_report_page.html")

            # 分析背景报告页面
            logging.info("开始分析背景报告页面...")
            background_analysis = analyze_page_source(new_page_source)

            if background_analysis:
                with open('background_report_analysis.json', 'w', encoding='utf-8') as f:
                    import json
                    json.dump(background_analysis, f, ensure_ascii=False, indent=2)
                logging.info("背景报告页面分析结果已保存到 background_report_analysis.json")
        else:
            logging.warning("未找到 'Open Free Background Report' 按钮，继续分析当前页面")

        # 分析原始页面源码
        logging.info("开始分析原始页面源码...")
        analysis_result = analyze_page_source(page_source)

        # 保存页面源码到文件
        with open('page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        logging.info("页面源码已保存到 page_source.html")

        # 保存分析结果
        if analysis_result:
            import json
            with open('page_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            logging.info("页面分析结果已保存到 page_analysis.json")

        logging.info("任务完成!")
        logging.info("页面标题: %s", driver.title)
        logging.info(f"页面源码长度: {len(page_source)} 字符")

        # 简短等待让用户看到结果
        time.sleep(2)
    except Exception as e:
        logging.error("An error occurred: %s", str(e))
    finally:
        logging.info('Closing the browser.')
        driver.quit()
        if isHeadless:
            display.stop()

if __name__ == '__main__':
    main()






