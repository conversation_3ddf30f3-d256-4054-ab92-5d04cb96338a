"""
LTESocks API服务器
提供RESTful API接口来管理LTESocks代理
"""

from fastapi import FastAPI, HTTPException, Query, Path, Body
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import os
from datetime import datetime

from ltesocks_client import (
    LTESocksClient, 
    LTESocksAPIError,
    Port, 
    PortsResponse, 
    PortLog, 
    PortLogResponse,
    User,
    Plan,
    PlansResponse,
    Server,
    ServersResponse,
    PaymentHistoryResponse,
    PlanTarification,
    PortCredentials
)

# 初始化FastAPI应用
app = FastAPI(
    title="LTESocks代理管理API",
    description="LTESocks代理服务的RESTful API接口",
    version="1.0.0"
)

# LTESocks客户端实例
API_KEY = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
ltesocks_client = LTESocksClient(API_KEY)

# 请求模型
class PortFilterRequest(BaseModel):
    plan: Optional[str] = None
    countryCode: Optional[str] = None
    tags: Optional[List[str]] = None
    page: int = 1
    pageSize: int = 20

class LogFilterRequest(BaseModel):
    from_date: Optional[str] = None
    to_date: Optional[str] = None
    page: int = 1
    pageSize: int = 20

class PaymentFilterRequest(BaseModel):
    from_date: Optional[str] = None
    to_date: Optional[str] = None
    page: int = 1
    pageSize: int = 20

class PortResetRequest(BaseModel):
    port_ids: Optional[List[str]] = None  # 如果为空则重置所有端口

class PortTagsUpdateRequest(BaseModel):
    tags: List[str]

class PortAutoResetRequest(BaseModel):
    autoResetInterval: int

class PortSignatureRequest(BaseModel):
    signature: str

class VPNCredentialsRequest(BaseModel):
    login: str
    password: str

# 用户相关接口
@app.get("/api/user", response_model=User, summary="获取用户信息")
async def get_user():
    """获取当前用户信息"""
    try:
        return ltesocks_client.get_user()
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 代理端口相关接口
@app.get("/api/ports", response_model=PortsResponse, summary="获取所有端口")
async def get_ports():
    """获取所有代理端口列表"""
    try:
        return ltesocks_client.get_ports()
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/filter", response_model=PortsResponse, summary="获取端口（带过滤）")
async def get_ports_with_filter(request: PortFilterRequest):
    """根据条件过滤获取端口列表"""
    try:
        return ltesocks_client.get_ports_with_filter(
            plan=request.plan,
            country_code=request.countryCode,
            tags=request.tags,
            page=request.page,
            page_size=request.pageSize
        )
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/ports/{port_id}", response_model=Port, summary="获取端口详情")
async def get_port(port_id: str = Path(..., description="端口ID")):
    """获取特定端口的详细信息"""
    try:
        return ltesocks_client.get_port(port_id)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/{port_id}/reset", response_model=Port, summary="重置端口")
async def reset_port(port_id: str = Path(..., description="端口ID")):
    """重置指定的代理端口"""
    try:
        return ltesocks_client.reset_port(port_id)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/reset-batch", summary="批量重置端口")
async def reset_ports_batch(request: PortResetRequest):
    """批量重置端口，如果port_ids为空则重置所有活跃端口"""
    try:
        if request.port_ids:
            # 重置指定端口
            reset_results = []
            for port_id in request.port_ids:
                try:
                    reset_port = ltesocks_client.reset_port(port_id)
                    reset_results.append({"port_id": port_id, "success": True, "data": reset_port})
                except LTESocksAPIError as e:
                    reset_results.append({"port_id": port_id, "success": False, "error": str(e)})
            return {"results": reset_results}
        else:
            # 重置所有端口
            reset_ports = ltesocks_client.reset_all_ports()
            return {"message": f"成功重置 {len(reset_ports)} 个端口", "ports": reset_ports}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ports/{port_id}/log", response_model=PortLogResponse, summary="获取端口日志")
async def get_port_log(port_id: str = Path(..., description="端口ID")):
    """获取端口操作日志"""
    try:
        return ltesocks_client.get_port_log(port_id)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/{port_id}/log", response_model=PortLogResponse, summary="获取端口日志（带过滤）")
async def get_port_log_with_filter(
    port_id: str = Path(..., description="端口ID"),
    request: LogFilterRequest = Body(...)
):
    """根据时间范围获取端口日志"""
    try:
        return ltesocks_client.get_port_log_with_filter(
            port_id=port_id,
            from_date=request.from_date,
            to_date=request.to_date,
            page=request.page,
            page_size=request.pageSize
        )
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/ports/{port_id}/proxy-info", summary="获取端口代理信息")
async def get_port_proxy_info(port_id: str = Path(..., description="端口ID")):
    """获取端口的代理连接信息"""
    try:
        return ltesocks_client.get_port_proxy_info(port_id)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/ports/{port_id}/vpn", summary="下载VPN配置")
async def download_vpn_config(port_id: str = Path(..., description="端口ID")):
    """下载端口的VPN配置文件"""
    try:
        config_data = ltesocks_client.get_vpn_config(port_id)
        return FileResponse(
            path=None,
            filename=f"port_{port_id}_vpn.zip",
            content=config_data,
            media_type="application/zip"
        )
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/{port_id}/vpn", summary="下载VPN配置（使用凭据）")
async def download_vpn_config_with_credentials(
    port_id: str = Path(..., description="端口ID"),
    request: VPNCredentialsRequest = Body(...)
):
    """使用指定凭据下载VPN配置文件"""
    try:
        config_data = ltesocks_client.get_vpn_config_with_credentials(
            port_id, request.login, request.password
        )
        return FileResponse(
            path=None,
            filename=f"port_{port_id}_vpn.zip",
            content=config_data,
            media_type="application/zip"
        )
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/{port_id}/tags", response_model=Port, summary="更新端口标签")
async def update_port_tags(
    port_id: str = Path(..., description="端口ID"),
    request: PortTagsUpdateRequest = Body(...)
):
    """更新端口的标签"""
    try:
        return ltesocks_client.update_port_tags(port_id, request.tags)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/{port_id}/autoreset", response_model=Port, summary="设置自动重置间隔")
async def update_port_autoreset(
    port_id: str = Path(..., description="端口ID"),
    request: PortAutoResetRequest = Body(...)
):
    """设置端口的自动重置间隔"""
    try:
        return ltesocks_client.update_port_autoreset(port_id, request.autoResetInterval)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/ports/{port_id}/signature", response_model=Port, summary="更新端口签名")
async def update_port_signature(
    port_id: str = Path(..., description="端口ID"),
    request: PortSignatureRequest = Body(...)
):
    """更新端口的签名"""
    try:
        return ltesocks_client.update_port_signature(port_id, request.signature)
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.delete("/api/ports/{port_id}", summary="删除端口")
async def delete_port(port_id: str = Path(..., description="端口ID")):
    """删除指定端口"""
    try:
        success = ltesocks_client.delete_port(port_id)
        if success:
            return {"message": f"端口 {port_id} 删除成功"}
        else:
            raise HTTPException(status_code=400, detail="删除失败")
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 便捷接口
@app.get("/api/ports/active", summary="获取活跃端口")
async def get_active_ports():
    """获取所有状态为活跃的端口"""
    try:
        active_ports = ltesocks_client.get_active_ports()
        return {"count": len(active_ports), "ports": active_ports}
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/ports/country/{country_code}", summary="按国家获取端口")
async def get_ports_by_country(country_code: str = Path(..., description="国家代码")):
    """根据国家代码获取端口"""
    try:
        ports = ltesocks_client.get_ports_by_country(country_code)
        return {"country": country_code, "count": len(ports), "ports": ports}
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 计划相关接口
@app.get("/api/plans", response_model=PlansResponse, summary="获取所有计划")
async def get_plans():
    """获取所有可用的代理计划"""
    try:
        return ltesocks_client.get_plans()
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 服务器相关接口
@app.get("/api/servers", response_model=ServersResponse, summary="获取服务器列表")
async def get_servers():
    """获取VPN和代理服务器列表"""
    try:
        return ltesocks_client.get_servers()
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 签名相关接口
@app.get("/api/signatures", summary="获取可用签名")
async def get_signatures():
    """获取所有可用的设备签名"""
    try:
        signatures = ltesocks_client.get_signatures()
        return {"signatures": signatures}
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 支付相关接口
@app.get("/api/payments", response_model=PaymentHistoryResponse, summary="获取支付历史")
async def get_payments_history():
    """获取支付历史记录"""
    try:
        return ltesocks_client.get_payments_history()
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/payments/filter", response_model=PaymentHistoryResponse, summary="获取支付历史（带过滤）")
async def get_payments_history_with_filter(request: PaymentFilterRequest):
    """根据时间范围获取支付历史"""
    try:
        return ltesocks_client.get_payments_history_with_filter(
            from_date=request.from_date,
            to_date=request.to_date,
            page=request.page,
            page_size=request.pageSize
        )
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/payments/currencies", summary="获取支持的货币")
async def get_currencies():
    """获取支持的支付货币列表"""
    try:
        currencies = ltesocks_client.get_currencies()
        return {"currencies": currencies}
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 统计和监控接口
@app.get("/api/stats/overview", summary="获取概览统计")
async def get_overview_stats():
    """获取账户和端口的概览统计信息"""
    try:
        user = ltesocks_client.get_user()
        ports_response = ltesocks_client.get_ports()
        active_ports = ltesocks_client.get_active_ports()

        # 按状态统计端口
        status_stats = {}
        country_stats = {}

        for port in ports_response.data:
            # 状态统计
            status = port.status
            status_stats[status] = status_stats.get(status, 0) + 1

            # 国家统计
            country = port.plan.countryCode
            country_stats[country] = country_stats.get(country, 0) + 1

        return {
            "user": {
                "login": user.login,
                "balance": user.balance / 100,  # 转换为美元
                "ports_count": user.portsCount,
                "ports_limit": user.portsLimit
            },
            "ports": {
                "total": ports_response.total,
                "active": len(active_ports),
                "by_status": status_stats,
                "by_country": country_stats
            }
        }
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/stats/countries", summary="获取国家统计")
async def get_country_stats():
    """获取按国家分组的端口统计"""
    try:
        ports_response = ltesocks_client.get_ports()
        country_stats = {}

        for port in ports_response.data:
            country = port.plan.countryCode
            if country not in country_stats:
                country_stats[country] = {
                    "total": 0,
                    "active": 0,
                    "plans": set()
                }

            country_stats[country]["total"] += 1
            if port.status == "active":
                country_stats[country]["active"] += 1
            country_stats[country]["plans"].add(port.plan.name)

        # 转换set为list
        for country in country_stats:
            country_stats[country]["plans"] = list(country_stats[country]["plans"])

        return country_stats
    except LTESocksAPIError as e:
        raise HTTPException(status_code=400, detail=str(e))

# 健康检查接口
@app.get("/api/health", summary="健康检查")
async def health_check():
    """API健康检查"""
    try:
        # 尝试获取用户信息来验证API连接
        user = ltesocks_client.get_user()
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "api_connection": "ok",
            "user": user.login
        }
    except LTESocksAPIError as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "api_connection": "failed",
            "error": str(e)
        }

# 根路径
@app.get("/", summary="API信息")
async def root():
    """API根路径，返回基本信息"""
    return {
        "name": "LTESocks代理管理API",
        "version": "1.0.0",
        "description": "LTESocks代理服务的RESTful API接口",
        "endpoints": {
            "docs": "/docs",
            "health": "/api/health",
            "user": "/api/user",
            "ports": "/api/ports",
            "plans": "/api/plans",
            "servers": "/api/servers"
        }
    }

if __name__ == "__main__":
    # 启动服务器
    port = int(os.getenv("PORT", 8001))
    uvicorn.run(app, host="0.0.0.0", port=port)
