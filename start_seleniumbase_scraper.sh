#!/bin/bash

# SeleniumBase批量电话号码爬取器启动脚本

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "================================================================"
echo "🚀 SeleniumBase批量电话号码爬取器"
echo "================================================================"
echo -e "${NC}"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 错误: 未找到Python，请先安装Python 3.7+${NC}"
        echo "Ubuntu/Debian: sudo apt-get install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python已安装${NC}"
$PYTHON_CMD --version

echo
echo "正在启动SeleniumBase爬取器..."
echo

# 运行Python启动脚本
$PYTHON_CMD start_seleniumbase_scraper.py

echo
echo -e "${BLUE}程序已结束${NC}"
