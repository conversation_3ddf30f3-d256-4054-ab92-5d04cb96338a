#!/bin/bash

echo "========================================"
echo "批量电话号码背景报告爬取器"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查依赖文件
if [ ! -f "batch_phone_scraper.py" ]; then
    echo "错误: 未找到 batch_phone_scraper.py"
    exit 1
fi

if [ ! -f "test_instant_load.py" ]; then
    echo "错误: 未找到 test_instant_load.py"
    exit 1
fi

if [ ! -f "KK1000.txt" ]; then
    echo "错误: 未找到电话号码文件 KK1000.txt"
    echo "请确保文件存在，每行一个电话号码（11位，以1开头）"
    exit 1
fi

echo "检查完成，准备启动批量爬取器..."
echo

# 显示菜单
echo "请选择运行模式:"
echo "1. 测试模式 (只处理前3个电话号码)"
echo "2. 正常模式 (处理所有电话号码)"
echo "3. 自定义模式 (指定处理数量)"
echo "4. 退出"
echo

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "启动测试模式..."
        python3 start_batch_scraper.py --test
        ;;
    2)
        echo "启动正常模式..."
        python3 start_batch_scraper.py
        ;;
    3)
        read -p "请输入要处理的电话号码数量: " max_phones
        echo "启动自定义模式，处理 $max_phones 个电话号码..."
        python3 start_batch_scraper.py --max-phones $max_phones
        ;;
    4)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效选择，退出程序"
        exit 1
        ;;
esac

echo
echo "任务完成"
