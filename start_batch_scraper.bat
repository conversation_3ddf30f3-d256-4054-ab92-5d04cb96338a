@echo off
echo ========================================
echo 批量电话号码背景报告爬取器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖文件
if not exist "batch_phone_scraper.py" (
    echo 错误: 未找到 batch_phone_scraper.py
    pause
    exit /b 1
)

if not exist "test_instant_load.py" (
    echo 错误: 未找到 test_instant_load.py
    pause
    exit /b 1
)

if not exist "KK1000.txt" (
    echo 错误: 未找到电话号码文件 KK1000.txt
    echo 请确保文件存在，每行一个电话号码（11位，以1开头）
    pause
    exit /b 1
)

echo 检查完成，准备启动批量爬取器...
echo.

REM 显示菜单
echo 请选择运行模式:
echo 1. 测试模式 (只处理前3个电话号码)
echo 2. 正常模式 (处理所有电话号码)
echo 3. 自定义模式 (指定处理数量)
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo 启动测试模式...
    python start_batch_scraper.py --test
) else if "%choice%"=="2" (
    echo 启动正常模式...
    python start_batch_scraper.py
) else if "%choice%"=="3" (
    set /p max_phones=请输入要处理的电话号码数量: 
    echo 启动自定义模式，处理 %max_phones% 个电话号码...
    python start_batch_scraper.py --max-phones %max_phones%
) else if "%choice%"=="4" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，退出程序
    pause
    exit /b 1
)

echo.
echo 任务完成，按任意键退出...
pause
