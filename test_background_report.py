#!/usr/bin/env python3
"""
测试查找并点击"Open Free Background Report"按钮的功能
"""

import time
import logging
import os
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('background_report_test.log', mode='w', encoding='utf-8')
    ]
)

def find_chrome_path():
    """查找Chrome浏览器路径"""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"找到Chrome浏览器: {path}")
            return path
    
    logging.warning("未找到Chrome浏览器")
    return None

def find_and_click_background_report_button(driver):
    """
    查找并点击"Open Free Background Report"按钮
    """
    try:
        logging.info("正在查找 'Open Free Background Report' 按钮...")
        
        # 等待页面基本加载
        time.sleep(2)
        
        # 方法1: 通过文本内容查找链接
        try:
            background_link = driver.ele('xpath://a[contains(text(), "Open Free Background Report")]', timeout=5)
            if background_link:
                href = background_link.attr('href')
                logging.info(f"找到背景报告按钮，链接: {href}")
                
                # 如果是相对链接，转换为绝对链接
                if href and href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                logging.info(f"准备访问背景报告页面: {href}")
                background_link.click()
                time.sleep(3)  # 等待页面加载
                return True, href
        except Exception as e:
            logging.warning(f"方法1失败: {e}")
        
        # 方法2: 通过部分文本匹配查找
        try:
            background_link = driver.ele('xpath://a[contains(text(), "Background Report") or contains(text(), "Free Background")]', timeout=5)
            if background_link:
                href = background_link.attr('href')
                logging.info(f"通过部分匹配找到链接: {href}")
                
                if href and href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                logging.info(f"准备访问背景报告页面: {href}")
                background_link.click()
                time.sleep(3)
                return True, href
        except Exception as e:
            logging.warning(f"方法2失败: {e}")
        
        # 方法3: 查找所有包含"background"或"report"的链接
        try:
            all_links = driver.eles('tag:a')
            logging.info(f"找到 {len(all_links)} 个链接，开始逐个检查...")
            
            for i, link in enumerate(all_links):
                try:
                    link_text = link.text.lower() if link.text else ""
                    href = link.attr('href') if link.attr('href') else ""
                    
                    if any(keyword in link_text for keyword in ['background', 'report', 'free background']):
                        logging.info(f"找到可能的背景报告链接 {i+1}: '{link.text}' -> {href}")
                        
                        if href and href.startswith('/'):
                            base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                            href = base_url + href
                        
                        if 'background' in link_text and 'report' in link_text:
                            logging.info(f"匹配到最佳链接，准备点击: {href}")
                            link.click()
                            time.sleep(3)
                            return True, href
                        elif 'free' in link_text and 'background' in link_text:
                            logging.info(f"匹配到免费背景链接，准备点击: {href}")
                            link.click()
                            time.sleep(3)
                            return True, href
                except Exception as link_error:
                    logging.debug(f"检查链接 {i+1} 时出错: {link_error}")
                    continue
                    
        except Exception as e:
            logging.warning(f"方法3失败: {e}")
        
        # 方法4: 通过CSS选择器查找
        try:
            # 查找包含特定文本的按钮或链接
            possible_selectors = [
                'a[href*="background"]',
                'a[href*="report"]',
                'button:contains("Background")',
                '.btn:contains("Background")',
                '.button:contains("Background")'
            ]
            
            for selector in possible_selectors:
                try:
                    elements = driver.eles(f'css:{selector}')
                    for element in elements:
                        text = element.text.lower() if element.text else ""
                        if 'background' in text or 'report' in text:
                            href = element.attr('href') if element.attr('href') else element.attr('onclick')
                            logging.info(f"通过CSS选择器找到: '{element.text}' -> {href}")
                            element.click()
                            time.sleep(3)
                            return True, href
                except:
                    continue
        except Exception as e:
            logging.warning(f"方法4失败: {e}")
        
        logging.warning("未找到 'Open Free Background Report' 按钮")
        return False, None
        
    except Exception as e:
        logging.error(f"查找背景报告按钮时出错: {e}")
        return False, None

def main():
    """主测试函数"""
    logging.info("开始测试背景报告按钮查找功能")
    logging.info("=" * 60)
    
    # 查找浏览器
    chrome_path = find_chrome_path()
    if not chrome_path:
        logging.error("无法找到Chrome浏览器")
        return
    
    # 创建浏览器选项
    try:
        options = ChromiumOptions()
        options.set_paths(browser_path=chrome_path)
        
        # 优化参数
        args = [
            "--no-first-run",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-web-security",
            "--disable-extensions"
        ]
        
        for arg in args:
            options.set_argument(arg)
            
        logging.info("浏览器选项配置完成")
        
    except Exception as e:
        logging.error(f"配置浏览器选项失败: {e}")
        return
    
    # 启动浏览器并测试
    try:
        logging.info("正在启动浏览器...")
        driver = ChromiumPage(addr_or_opts=options)
        logging.info("浏览器启动成功")
        
        # 访问目标页面
        target_url = "https://www.smartbackgroundchecks.com/phone/5619324217"
        logging.info(f"正在访问: {target_url}")
        
        driver.get(target_url)
        logging.info("页面加载完成")
        
        # 等待页面稳定
        time.sleep(3)
        
        # 检查是否有Cloudflare保护
        page_source = driver.html
        if any(keyword in page_source.lower() for keyword in ["cloudflare", "just a moment", "checking your browser"]):
            logging.info("检测到Cloudflare保护，开始绕过...")
            cf_bypasser = CloudflareBypasser(driver)
            cf_bypasser.bypass()
            logging.info("Cloudflare绕过完成")
            time.sleep(2)
        
        # 查找并点击背景报告按钮
        logging.info("=" * 50)
        button_found, button_url = find_and_click_background_report_button(driver)
        
        if button_found:
            logging.info(f"✅ 成功找到并点击背景报告按钮!")
            logging.info(f"当前页面URL: {driver.url}")
            logging.info(f"按钮链接: {button_url}")
            
            # 获取新页面信息
            time.sleep(3)
            new_page_source = driver.html
            soup = BeautifulSoup(new_page_source, 'html.parser')
            new_title = soup.title.string if soup.title else "无标题"
            
            logging.info(f"新页面标题: {new_title}")
            logging.info(f"新页面源码长度: {len(new_page_source)} 字符")
            
            # 保存新页面源码
            with open('background_report_page.html', 'w', encoding='utf-8') as f:
                f.write(new_page_source)
            logging.info("背景报告页面源码已保存到 background_report_page.html")
            
        else:
            logging.error("❌ 未找到背景报告按钮")
            
            # 输出当前页面的所有链接供调试
            logging.info("当前页面的所有链接:")
            all_links = driver.eles('tag:a')
            for i, link in enumerate(all_links[:20]):  # 只显示前20个
                try:
                    text = link.text.strip() if link.text else ""
                    href = link.attr('href') if link.attr('href') else ""
                    if text:  # 只显示有文本的链接
                        logging.info(f"  {i+1}. '{text}' -> {href}")
                except:
                    continue
        
        # 等待用户查看结果
        time.sleep(5)
        
    except Exception as e:
        logging.error(f"测试过程中出错: {e}")
    finally:
        try:
            driver.quit()
            logging.info("浏览器已关闭")
        except:
            pass
    
    logging.info("=" * 60)
    logging.info("测试完成")

if __name__ == '__main__':
    main()
