@echo off
echo ========================================
echo LTESocks端口重置定时任务启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖文件
if not exist "ltesocks_port_reset_scheduler.py" (
    echo 错误: 未找到 ltesocks_port_reset_scheduler.py
    pause
    exit /b 1
)

if not exist "ltesocks_client.py" (
    echo 错误: 未找到 ltesocks_client.py
    pause
    exit /b 1
)

echo 正在启动LTESocks端口重置定时任务...
echo 每3分钟自动重置一次端口
echo 按 Ctrl+C 停止任务
echo.

REM 启动定时任务
python start_port_reset_scheduler.py --interval 180

echo.
echo 任务已停止
pause
