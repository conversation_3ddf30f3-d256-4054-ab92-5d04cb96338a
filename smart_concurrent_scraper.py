#!/usr/bin/env python3
"""
智能并发电话号码爬取器
使用单浏览器多标签页 + 智能延迟策略，避免触发反爬机制
"""

import os
import sys
import time
import logging
import traceback
import random
import threading
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import csv
from pathlib import Path
from queue import Queue

from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
from CloudflareBypasser import CloudflareBypasser
from test_instant_load import (
    find_chrome_path, instant_page_load, find_background_report_instant,
    extract_person_info, extract_relatives_info, click_relative_link_and_extract
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [Tab-%(thread)d] %(message)s',
    handlers=[
        logging.FileHandler('smart_concurrent_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class SmartConcurrentScraper:
    """智能并发爬取器 - 单浏览器多标签页策略"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data", 
                 max_tabs: int = 3, smart_delay: bool = True):
        """
        初始化智能并发爬取器
        
        Args:
            phone_file: 电话号码文件路径
            output_dir: 输出目录
            max_tabs: 最大标签页数量（建议2-4个）
            smart_delay: 是否启用智能延迟
        """
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.max_tabs = max_tabs
        self.smart_delay = smart_delay
        
        # 统计信息
        self.stats_lock = threading.Lock()
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None,
            'active_tabs': 0
        }
        
        # 浏览器实例（单例）
        self.driver = None
        self.tabs = {}  # 标签页管理
        
        # 电话号码队列
        self.phone_queue = Queue()
        self.result_queue = Queue()
        
        # 已处理记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()
        
        # CSV文件
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_lock = threading.Lock()
        self.csv_initialized = False
        
        # 智能延迟控制
        self.last_request_time = {}  # 每个标签页的最后请求时间
        self.min_delay = 3.0  # 最小延迟（秒）
        self.max_delay = 8.0  # 最大延迟（秒）
        
        logging.info(f"智能并发爬取器初始化完成")
        logging.info(f"最大标签页数: {max_tabs}")
        logging.info(f"智能延迟: {'启用' if smart_delay else '禁用'}")
        logging.info(f"已处理电话号码: {len(self.processed_phones)}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")
    
    def init_unified_csv(self):
        """初始化CSV文件"""
        with self.csv_lock:
            if not self.csv_initialized:
                try:
                    if not self.unified_csv_file.exists():
                        with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                            writer = csv.writer(f)
                            writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                        logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                    else:
                        logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
                    self.csv_initialized = True
                except Exception as e:
                    logging.error(f"初始化CSV文件失败: {e}")
    
    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """保存数据到CSV"""
        try:
            self.init_unified_csv()
            with self.csv_lock:
                with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        phone,
                        data_type,
                        info.get('姓名', ''),
                        info.get('年龄', ''),
                        info.get('手机', ''),
                        info.get('地址', ''),
                        info.get('关系', data_type),
                        info.get('链接', '')
                    ])
            return True
        except Exception as e:
            logging.error(f"保存数据失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                        
                        # 验证电话号码格式
                        import re
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def init_browser(self) -> bool:
        """初始化单个浏览器实例"""
        try:
            chrome_path = find_chrome_path()
            if not chrome_path:
                logging.error("无法找到Chrome浏览器")
                return False
            
            # 创建浏览器配置 - 更加隐蔽
            options = ChromiumOptions()
            options.set_paths(browser_path=chrome_path)
            
            # 反检测参数
            stealth_args = [
                "--no-first-run",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",  # 提升速度
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-back-forward-cache",
                "--disable-sync",
                "--disable-translate",
                "--disable-default-apps",
                "--no-default-browser-check",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-component-update",
                "--disable-domain-reliability",
                "--disable-client-side-phishing-detection",
                "--disable-background-networking",
                "--aggressive-cache-discard"
            ]
            
            for arg in stealth_args:
                options.set_argument(arg)
            
            # 设置User-Agent模拟真实浏览器
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            options.set_argument(f"--user-agent={random.choice(user_agents)}")
            
            # 资源过滤
            prefs = {
                "profile.default_content_setting_values": {
                    "images": 2,
                    "plugins": 2,
                    "popups": 2,
                    "geolocation": 2,
                    "notifications": 2,
                    "media_stream": 2,
                }
            }
            options.set_pref("prefs", prefs)
            
            # 启动浏览器
            logging.info("正在启动浏览器...")
            self.driver = ChromiumPage(addr_or_opts=options)
            self.driver.set.timeouts(page_load=5)
            self.driver.set.load_mode.none()
            
            # 执行反检测脚本
            self.driver.run_js("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
                
                window.chrome = {
                    runtime: {},
                };
            """)
            
            logging.info("浏览器初始化成功")
            return True

        except Exception as e:
            logging.error(f"初始化浏览器失败: {e}")
            return False

    def create_new_tab(self, tab_id: str) -> bool:
        """创建新标签页"""
        try:
            # 创建新标签页
            self.driver.new_tab()

            # 切换到新标签页
            tabs = self.driver.tab_ids
            if len(tabs) > 0:
                new_tab_id = tabs[-1]  # 最新的标签页
                self.driver.to_tab(new_tab_id)

                # 记录标签页信息
                self.tabs[tab_id] = {
                    'tab_id': new_tab_id,
                    'created_time': time.time(),
                    'last_request_time': 0,
                    'request_count': 0,
                    'status': 'idle'
                }

                logging.info(f"标签页 {tab_id} 创建成功，浏览器标签ID: {new_tab_id}")
                return True
            else:
                logging.error(f"创建标签页 {tab_id} 失败：无法获取标签页ID")
                return False

        except Exception as e:
            logging.error(f"创建标签页 {tab_id} 失败: {e}")
            return False

    def close_tab(self, tab_id: str):
        """关闭标签页"""
        try:
            if tab_id in self.tabs:
                browser_tab_id = self.tabs[tab_id]['tab_id']
                self.driver.close_tab(browser_tab_id)
                del self.tabs[tab_id]
                logging.info(f"标签页 {tab_id} 已关闭")
        except Exception as e:
            logging.error(f"关闭标签页 {tab_id} 失败: {e}")

    def switch_to_tab(self, tab_id: str) -> bool:
        """切换到指定标签页"""
        try:
            if tab_id in self.tabs:
                browser_tab_id = self.tabs[tab_id]['tab_id']
                self.driver.to_tab(browser_tab_id)
                return True
            else:
                logging.error(f"标签页 {tab_id} 不存在")
                return False
        except Exception as e:
            logging.error(f"切换到标签页 {tab_id} 失败: {e}")
            return False

    def calculate_smart_delay(self, tab_id: str) -> float:
        """计算智能延迟时间"""
        if not self.smart_delay:
            return random.uniform(0.5, 1.0)

        current_time = time.time()

        if tab_id in self.last_request_time:
            time_since_last = current_time - self.last_request_time[tab_id]

            # 如果距离上次请求时间太短，增加延迟
            if time_since_last < self.min_delay:
                additional_delay = self.min_delay - time_since_last
                base_delay = random.uniform(self.min_delay, self.max_delay)
                total_delay = base_delay + additional_delay
            else:
                # 正常延迟
                total_delay = random.uniform(self.min_delay, self.max_delay)
        else:
            # 首次请求，使用最小延迟
            total_delay = random.uniform(1.0, 2.0)

        # 记录请求时间
        self.last_request_time[tab_id] = current_time + total_delay

        return total_delay

    def scrape_phone_in_tab(self, tab_id: str, phone: str) -> Tuple[bool, Dict]:
        """在指定标签页中爬取电话号码数据"""
        start_time = time.time()

        try:
            # 切换到指定标签页
            if not self.switch_to_tab(tab_id):
                return False, {}

            # 更新标签页状态
            self.tabs[tab_id]['status'] = 'working'
            self.tabs[tab_id]['request_count'] += 1

            logging.info(f"[{tab_id}] 开始处理电话号码: {phone}")

            # 智能延迟
            delay = self.calculate_smart_delay(tab_id)
            logging.info(f"[{tab_id}] 智能延迟: {delay:.2f}秒")
            time.sleep(delay)

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"[{tab_id}] 访问URL: {target_url}")

            # 页面加载
            if not instant_page_load(self.driver, target_url, max_wait=2):
                logging.error(f"[{tab_id}] 页面加载失败: {phone}")
                return False, {}

            # Cloudflare处理
            page_source = self.driver.html
            cloudflare_keywords = ["cloudflare", "just a moment", "checking your browser"]
            has_cloudflare = any(keyword in page_source.lower() for keyword in cloudflare_keywords)

            if has_cloudflare:
                logging.info(f"[{tab_id}] 检测到Cloudflare，使用bypasser处理")
                cf_bypasser = CloudflareBypasser(self.driver, max_retries=3, log=True)
                cf_bypasser.bypass()

                # 重新获取页面源码
                page_source = self.driver.html

            # 查找背景报告按钮
            button_found, button_url = find_background_report_instant(self.driver)

            if not button_found:
                logging.warning(f"[{tab_id}] 未找到背景报告按钮: {phone}")
                return False, {}

            logging.info(f"[{tab_id}] 找到背景报告链接: {button_url}")

            # 访问背景报告页面
            if not instant_page_load(self.driver, button_url, max_wait=2):
                logging.error(f"[{tab_id}] 背景报告页面加载失败: {phone}")
                return False, {}

            # 处理背景报告页面的Cloudflare
            bg_page_source = self.driver.html
            has_cloudflare = any(keyword in bg_page_source.lower() for keyword in cloudflare_keywords)

            if has_cloudflare:
                logging.info(f"[{tab_id}] 背景报告页面检测到Cloudflare")
                cf_bypasser = CloudflareBypasser(self.driver, max_retries=5, log=True)
                cf_bypasser.bypass()
                bg_page_source = self.driver.html

            # 解析页面内容
            time.sleep(0.5)  # 短暂等待
            soup = BeautifulSoup(bg_page_source, 'html.parser')

            # 提取个人信息
            person_info = extract_person_info(soup)
            if not person_info.get('姓名'):
                logging.warning(f"[{tab_id}] 未提取到个人信息: {phone}")
                return False, {}

            # 保存个人信息
            self.save_to_csv(phone, '本人', person_info)

            # 提取亲属信息
            relatives_info = extract_relatives_info(soup, min_age=40)
            logging.info(f"[{tab_id}] 找到 {len(relatives_info)} 个符合条件的亲属")

            # 处理亲属信息
            detailed_relatives_count = 0
            for i, relative in enumerate(relatives_info):
                relative_name = relative.get('姓名', '')
                if relative_name:
                    try:
                        logging.info(f"[{tab_id}] 处理亲属 {i+1}/{len(relatives_info)}: {relative_name}")

                        relative_details = click_relative_link_and_extract(self.driver, relative_name, soup)

                        combined_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': relative_details.get('手机', ''),
                            '地址': relative_details.get('地址', '')
                        }

                        # 保存亲属信息
                        if self.save_to_csv(phone, '亲属', combined_info):
                            detailed_relatives_count += 1

                        # 短暂延迟
                        time.sleep(random.uniform(0.3, 0.8))

                    except Exception as e:
                        logging.error(f"[{tab_id}] 处理亲属 {relative_name} 失败: {e}")
                        # 保存基本信息
                        basic_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': '',
                            '地址': ''
                        }
                        self.save_to_csv(phone, '亲属', basic_info)

            duration = time.time() - start_time
            logging.info(f"[{tab_id}] ✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"[{tab_id}] 个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"[{tab_id}] 成功保存亲属数量: {detailed_relatives_count}")

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"[{tab_id}] ❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"[{tab_id}] 错误详情: {e}")
            logging.error(f"[{tab_id}] 错误堆栈: {traceback.format_exc()}")
            return False, {}
        finally:
            # 更新标签页状态
            if tab_id in self.tabs:
                self.tabs[tab_id]['status'] = 'idle'

    def worker_thread(self, tab_id: str):
        """工作线程 - 处理分配给特定标签页的任务"""
        thread_name = f"Tab-{tab_id}"
        logging.info(f"{thread_name} 工作线程启动")

        try:
            # 创建标签页
            if not self.create_new_tab(tab_id):
                logging.error(f"{thread_name} 创建标签页失败")
                return

            # 更新活跃标签页计数
            with self.stats_lock:
                self.stats['active_tabs'] += 1

            # 处理队列中的电话号码
            while True:
                try:
                    # 从队列获取电话号码，设置超时避免无限等待
                    phone = self.phone_queue.get(timeout=5)

                    if phone is None:  # 结束信号
                        break

                    logging.info(f"{thread_name} 获取到任务: {phone}")

                    # 检查是否已处理
                    if phone in self.processed_phones:
                        logging.info(f"{thread_name} 跳过已处理的号码: {phone}")
                        self.phone_queue.task_done()
                        continue

                    # 处理电话号码
                    success, result_data = self.scrape_phone_in_tab(tab_id, phone)

                    # 更新统计
                    with self.stats_lock:
                        self.stats['processed_phones'] += 1
                        if success:
                            self.stats['successful_scrapes'] += 1
                            self.save_processed_phone(phone)
                            logging.info(f"{thread_name} ✅ 成功处理: {phone}")
                        else:
                            self.stats['failed_scrapes'] += 1
                            logging.error(f"{thread_name} ❌ 处理失败: {phone}")

                    # 标记任务完成
                    self.phone_queue.task_done()

                    # 打印当前统计
                    self.print_stats()

                except Exception as e:
                    if "timeout" not in str(e).lower():
                        logging.error(f"{thread_name} 处理任务时发生异常: {e}")
                    break

        except Exception as e:
            logging.error(f"{thread_name} 工作线程异常: {e}")
        finally:
            # 清理标签页
            self.close_tab(tab_id)

            # 更新活跃标签页计数
            with self.stats_lock:
                self.stats['active_tabs'] -= 1

            logging.info(f"{thread_name} 工作线程结束")

    def monitor_progress(self):
        """监控进度的线程"""
        logging.info("进度监控线程启动")

        while True:
            try:
                time.sleep(10)  # 每10秒检查一次

                with self.stats_lock:
                    if self.stats['active_tabs'] == 0:
                        break

                    # 打印详细统计
                    elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
                    logging.info("=" * 60)
                    logging.info(f"📊 进度监控 - 运行时间: {elapsed:.0f}秒")
                    logging.info(f"活跃标签页: {self.stats['active_tabs']}")
                    logging.info(f"已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}")
                    logging.info(f"成功: {self.stats['successful_scrapes']}")
                    logging.info(f"失败: {self.stats['failed_scrapes']}")
                    if self.stats['processed_phones'] > 0:
                        success_rate = self.stats['successful_scrapes'] / self.stats['processed_phones'] * 100
                        logging.info(f"成功率: {success_rate:.1f}%")
                    logging.info("=" * 60)

                    # 标签页状态
                    for tab_id, tab_info in self.tabs.items():
                        status = tab_info['status']
                        request_count = tab_info['request_count']
                        logging.info(f"标签页 {tab_id}: {status}, 已处理 {request_count} 个请求")

            except Exception as e:
                logging.error(f"进度监控异常: {e}")
                break

        logging.info("进度监控线程结束")

    def run_smart_concurrent_scraping(self, max_phones: Optional[int] = None) -> Dict:
        """
        运行智能并发爬取

        Args:
            max_phones: 最大处理电话号码数量，None表示处理所有

        Returns:
            统计结果字典
        """
        from datetime import datetime

        self.stats['start_time'] = datetime.now()
        logging.info("🚀 开始智能并发爬取任务")
        logging.info(f"最大标签页数: {self.max_tabs}")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 初始化浏览器
        if not self.init_browser():
            logging.error("浏览器初始化失败，无法继续")
            return self.stats

        try:
            # 将电话号码添加到队列
            for phone in unprocessed_phones:
                self.phone_queue.put(phone)

            # 启动工作线程（每个标签页一个线程）
            threads = []

            for i in range(self.max_tabs):
                tab_id = f"tab_{i+1}"
                thread = threading.Thread(
                    target=self.worker_thread,
                    args=(tab_id,),
                    name=f"Worker-{tab_id}"
                )
                thread.start()
                threads.append(thread)

                # 错开启动时间，避免同时创建标签页
                time.sleep(1)

            # 启动进度监控线程
            monitor_thread = threading.Thread(target=self.monitor_progress, name="Monitor")
            monitor_thread.start()

            # 等待所有任务完成
            self.phone_queue.join()

            # 发送结束信号给所有工作线程
            for _ in range(self.max_tabs):
                self.phone_queue.put(None)

            # 等待所有工作线程结束
            for thread in threads:
                thread.join()

            # 等待监控线程结束
            monitor_thread.join()

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在优雅退出...")
        except Exception as e:
            logging.error(f"智能并发处理过程中发生严重错误: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
        finally:
            # 关闭浏览器
            self.close_browser()

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 智能并发爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        if self.stats['processed_phones'] > 0:
            success_rate = self.stats['successful_scrapes'] / self.stats['processed_phones'] * 100
            logging.info(f"成功率: {success_rate:.1f}%")
            avg_time = duration / self.stats['processed_phones']
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
        logging.info("=" * 80)

        return self.stats

    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                # 关闭所有标签页
                for tab_id in list(self.tabs.keys()):
                    self.close_tab(tab_id)

                # 关闭浏览器
                self.driver.quit()
                logging.info("浏览器已关闭")
            except Exception as e:
                logging.error(f"关闭浏览器失败: {e}")
            finally:
                self.driver = None
                self.tabs.clear()

    def print_stats(self):
        """打印当前统计信息"""
        with self.stats_lock:
            if self.stats['start_time']:
                elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
                logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                            f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                            f"活跃标签页: {self.stats['active_tabs']}, 耗时: {elapsed:.0f}秒")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='智能并发电话号码背景报告爬取器 - 单浏览器多标签页策略')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--max-tabs', type=int, default=3, help='最大标签页数量（建议2-4个）')
    parser.add_argument('--smart-delay', action='store_true', default=True, help='启用智能延迟')
    parser.add_argument('--no-smart-delay', action='store_false', dest='smart_delay', help='禁用智能延迟')

    args = parser.parse_args()

    try:
        # 验证参数
        if args.max_tabs < 1 or args.max_tabs > 8:
            logging.error("标签页数量应该在1-8之间")
            sys.exit(1)

        # 创建智能并发爬取器
        scraper = SmartConcurrentScraper(
            phone_file=args.phone_file,
            output_dir=args.output_dir,
            max_tabs=args.max_tabs,
            smart_delay=args.smart_delay
        )

        # 运行智能并发爬取
        stats = scraper.run_smart_concurrent_scraping(args.max_phones)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
