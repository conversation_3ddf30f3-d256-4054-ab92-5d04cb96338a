#!/usr/bin/env python3
"""
批量爬取启动脚本
提供简单的命令行界面来启动批量爬取任务
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from batch_phone_scraper import BatchPhoneScraper

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 批量电话号码背景报告爬取器")
    print("=" * 80)
    
    parser = argparse.ArgumentParser(description='批量电话号码背景报告爬取器')
    parser.add_argument('--phone-file', '-f', default='KK1000.txt', 
                       help='电话号码文件路径 (默认: KK1000.txt)')
    parser.add_argument('--output-dir', '-o', default='scraped_data', 
                       help='输出目录 (默认: scraped_data)')
    parser.add_argument('--max-phones', '-m', type=int, 
                       help='最大处理电话号码数量 (默认: 处理所有)')
    parser.add_argument('--delay', '-d', type=int, default=0,
                       help='电话号码之间的延迟秒数 (默认: 0秒，最大效率)')
    parser.add_argument('--resume', '-r', action='store_true',
                       help='从上次中断的地方继续处理')
    parser.add_argument('--test', '-t', action='store_true',
                       help='测试模式，只处理前3个电话号码')
    
    args = parser.parse_args()
    
    # 检查电话号码文件
    if not os.path.exists(args.phone_file):
        print(f"❌ 错误: 电话号码文件不存在: {args.phone_file}")
        print("请确保文件存在，每行一个电话号码（11位，以1开头）")
        sys.exit(1)
    
    # 测试模式
    if args.test:
        args.max_phones = 3
        args.delay = 2
        print("🧪 测试模式: 只处理前3个电话号码")
    
    # 显示配置
    print(f"📁 电话号码文件: {args.phone_file}")
    print(f"📂 输出目录: {args.output_dir}")
    print(f"📊 最大处理数量: {args.max_phones or '全部'}")
    print(f"⏰ 延迟间隔: {args.delay}秒")
    print(f"🔄 继续模式: {'是' if args.resume else '否'}")
    print()
    
    # 确认开始
    if not args.test:
        try:
            confirm = input("是否开始批量爬取？(y/N): ").lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                sys.exit(0)
        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
            sys.exit(0)
    
    try:
        # 创建爬取器
        scraper = BatchPhoneScraper(args.phone_file, args.output_dir)
        
        # 运行批量爬取
        print("\n🚀 开始批量爬取...")
        stats = scraper.run_batch_scraping(args.max_phones, args.delay)
        
        # 显示最终结果
        print("\n" + "=" * 80)
        print("🎯 批量爬取完成!")
        print("=" * 80)
        print(f"📊 处理统计:")
        print(f"   总数量: {stats['total_phones']}")
        print(f"   已处理: {stats['processed_phones']}")
        print(f"   成功: {stats['successful_scrapes']}")
        print(f"   失败: {stats['failed_scrapes']}")
        
        if stats['processed_phones'] > 0:
            success_rate = stats['successful_scrapes'] / stats['processed_phones'] * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        print(f"📂 输出目录: {args.output_dir}")
        print(f"📝 日志文件: batch_scraper.log")
        
        # 退出码
        if stats['successful_scrapes'] > 0:
            print("✅ 任务完成")
            sys.exit(0)
        else:
            print("❌ 没有成功处理任何电话号码")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        print("💡 提示: 下次可以使用 --resume 参数从中断处继续")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        print("📝 详细错误信息请查看日志文件: batch_scraper.log")
        sys.exit(1)

if __name__ == "__main__":
    main()
