#!/usr/bin/env python3
"""
测试优化版SeleniumBase爬取器
"""

import sys
import logging
from seleniumbase_optimized_scraper import OptimizedSeleniumBaseScraper

def main():
    """测试主函数"""
    
    print("=" * 60)
    print("🧪 测试优化版SeleniumBase爬取器")
    print("=" * 60)
    
    # 测试配置
    phone_file = "KK1000.txt"
    output_dir = "scraped_data"
    max_phones = 1  # 只测试1个号码
    base_delay = 1.5
    max_relatives = 10  # 限制亲属数量
    
    print(f"📁 电话号码文件: {phone_file}")
    print(f"📂 输出目录: {output_dir}")
    print(f"📞 测试号码数量: {max_phones}")
    print(f"⏰ 基础延迟: {base_delay}秒")
    print(f"👥 最大亲属数量: {max_relatives}")
    print("=" * 60)
    
    try:
        # 创建优化版爬取器
        scraper = OptimizedSeleniumBaseScraper(phone_file, output_dir)
        scraper.max_relatives_per_phone = max_relatives
        
        # 运行测试
        stats = scraper.run_optimized_batch_scraping(
            max_phones=max_phones,
            base_delay=base_delay
        )
        
        # 显示结果
        print("\n" + "=" * 60)
        print("📊 测试结果")
        print("=" * 60)
        print(f"✅ 成功处理: {stats['successful_scrapes']}")
        print(f"❌ 处理失败: {stats['failed_scrapes']}")
        print(f"🚫 Cloudflare阻止: {stats['cloudflare_blocks']}")
        print(f"🔍 按钮未找到: {stats['button_not_found']}")
        print(f"👥 提取亲属数量: {stats['relatives_extracted']}")
        
        if stats['processed_phones'] > 0:
            success_rate = stats['successful_scrapes'] / stats['processed_phones'] * 100
            print(f"📈 成功率: {success_rate:.1f}%")
        
        print("=" * 60)
        
        if stats['successful_scrapes'] > 0:
            print("🎉 测试成功！优化版爬取器工作正常。")
            return 0
        else:
            print("⚠️ 测试失败，请检查日志了解详情。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断了测试。")
        return 1
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logging.error(f"测试失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
