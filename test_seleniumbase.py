#!/usr/bin/env python3
"""
SeleniumBase功能测试脚本
测试UC Mode和CDP Mode的基本功能
"""

import sys
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_basic_import():
    """测试基本导入"""
    try:
        from seleniumbase import SB
        logging.info("✅ SeleniumBase导入成功")
        return True
    except ImportError as e:
        logging.error(f"❌ SeleniumBase导入失败: {e}")
        return False

def test_uc_mode():
    """测试UC Mode基本功能"""
    try:
        from seleniumbase import SB
        
        logging.info("🧪 测试UC Mode...")
        
        with SB(uc=True, test=True, headless=False) as sb:
            # 测试访问一个简单页面
            test_url = "https://seleniumbase.io/demo_page"
            sb.open(test_url)
            sb.sleep(2)
            
            # 检查页面标题
            title = sb.get_title()
            logging.info(f"页面标题: {title}")
            
            if "demo" in title.lower():
                logging.info("✅ UC Mode基本功能测试通过")
                return True
            else:
                logging.error("❌ UC Mode测试失败：页面标题不符合预期")
                return False
                
    except Exception as e:
        logging.error(f"❌ UC Mode测试失败: {e}")
        return False

def test_cdp_mode():
    """测试CDP Mode基本功能"""
    try:
        from seleniumbase import SB
        
        logging.info("🧪 测试CDP Mode...")
        
        with SB(uc=True, test=True, headless=False) as sb:
            # 测试访问页面并激活CDP Mode
            test_url = "https://seleniumbase.io/demo_page"
            sb.activate_cdp_mode(test_url)
            sb.sleep(2)
            
            # 使用CDP方法获取页面信息
            title = sb.cdp.get_title()
            current_url = sb.cdp.get_current_url()
            
            logging.info(f"CDP获取的标题: {title}")
            logging.info(f"CDP获取的URL: {current_url}")
            
            # 测试CDP点击功能
            if sb.cdp.is_element_visible("button"):
                sb.cdp.click("button")
                sb.sleep(1)
                logging.info("✅ CDP点击测试通过")
            
            if "demo" in title.lower():
                logging.info("✅ CDP Mode基本功能测试通过")
                return True
            else:
                logging.error("❌ CDP Mode测试失败：页面标题不符合预期")
                return False
                
    except Exception as e:
        logging.error(f"❌ CDP Mode测试失败: {e}")
        return False

def test_captcha_handling():
    """测试CAPTCHA处理功能"""
    try:
        from seleniumbase import SB
        
        logging.info("🧪 测试CAPTCHA处理...")
        
        with SB(uc=True, test=True, headless=False) as sb:
            # 访问一个有Turnstile的测试页面
            test_url = "https://seleniumbase.io/apps/turnstile"
            sb.activate_cdp_mode(test_url)
            sb.sleep(3)
            
            # 尝试自动处理CAPTCHA
            try:
                sb.uc_gui_click_captcha()
                sb.sleep(2)
                logging.info("✅ CAPTCHA处理功能可用")
                return True
            except Exception as e:
                logging.warning(f"⚠️ CAPTCHA处理测试: {e}")
                # CAPTCHA处理失败不算致命错误
                return True
                
    except Exception as e:
        logging.error(f"❌ CAPTCHA处理测试失败: {e}")
        return False

def test_data_extraction():
    """测试数据提取功能"""
    try:
        from seleniumbase import SB
        from bs4 import BeautifulSoup
        
        logging.info("🧪 测试数据提取...")
        
        with SB(uc=True, test=True, headless=False) as sb:
            # 访问测试页面
            test_url = "https://seleniumbase.io/demo_page"
            sb.activate_cdp_mode(test_url)
            sb.sleep(2)
            
            # 测试获取页面源码
            page_source = sb.cdp.get_page_source()
            if len(page_source) > 1000:
                logging.info(f"✅ 页面源码获取成功，长度: {len(page_source)}")
            else:
                logging.error("❌ 页面源码获取失败")
                return False
            
            # 测试BeautifulSoup解析
            soup = BeautifulSoup(page_source, 'html.parser')
            title_tag = soup.find('title')
            if title_tag:
                logging.info(f"✅ BeautifulSoup解析成功，标题: {title_tag.text}")
                return True
            else:
                logging.error("❌ BeautifulSoup解析失败")
                return False
                
    except Exception as e:
        logging.error(f"❌ 数据提取测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作功能"""
    try:
        import csv
        from pathlib import Path
        
        logging.info("🧪 测试文件操作...")
        
        # 测试创建目录
        test_dir = Path("test_output")
        test_dir.mkdir(exist_ok=True)
        
        # 测试CSV写入
        test_csv = test_dir / "test_data.csv"
        with open(test_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['电话', '姓名', '年龄'])
            writer.writerow(['1234567890', '测试用户', '30'])
        
        # 测试CSV读取
        with open(test_csv, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
            if len(rows) == 2:
                logging.info("✅ CSV文件操作测试通过")
                
                # 清理测试文件
                test_csv.unlink()
                test_dir.rmdir()
                return True
            else:
                logging.error("❌ CSV文件操作测试失败")
                return False
                
    except Exception as e:
        logging.error(f"❌ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始SeleniumBase功能测试")
    
    tests = [
        ("基本导入", test_basic_import),
        ("UC Mode", test_uc_mode),
        ("CDP Mode", test_cdp_mode),
        ("CAPTCHA处理", test_captcha_handling),
        ("数据提取", test_data_extraction),
        ("文件操作", test_file_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logging.info(f"\n{'='*50}")
        logging.info(f"测试: {test_name}")
        logging.info(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
                logging.info(f"✅ {test_name} 测试通过")
            else:
                logging.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logging.error(f"❌ {test_name} 测试异常: {e}")
    
    logging.info(f"\n{'='*50}")
    logging.info(f"测试结果: {passed}/{total} 通过")
    logging.info(f"{'='*50}")
    
    if passed == total:
        logging.info("🎉 所有测试通过！SeleniumBase功能正常")
        logging.info("现在可以运行批量爬取器:")
        logging.info("python seleniumbase_batch_scraper.py --max-phones 1 --delay 3.0")
        return True
    else:
        logging.error("⚠️ 部分测试失败，请检查SeleniumBase安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
