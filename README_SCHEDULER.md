# LTESocks端口重置定时任务

## 📋 功能说明

这个定时任务程序可以自动重置LTESocks的所有活跃端口，帮助您保持代理IP的新鲜度。

### 🎯 主要功能

- ⏰ **定时重置**: 每3分钟自动重置所有活跃端口
- 📊 **状态监控**: 实时显示重置状态和统计信息
- 📝 **日志记录**: 详细记录所有操作和错误信息
- 🔧 **灵活配置**: 支持自定义重置间隔和API密钥
- 🛡️ **错误处理**: 自动重试和错误恢复机制

## 🚀 快速开始

### 1. 使用批处理文件启动（Windows）

```bash
# 双击运行或在命令行执行
start_scheduler.bat
```

### 2. 使用Shell脚本启动（Linux/Mac）

```bash
# 添加执行权限
chmod +x start_scheduler.sh

# 运行脚本
./start_scheduler.sh
```

### 3. 使用Python直接启动

```bash
# 默认配置（每3分钟重置）
python start_port_reset_scheduler.py

# 自定义间隔（每5分钟重置）
python start_port_reset_scheduler.py --interval 300

# 只执行一次重置
python start_port_reset_scheduler.py --once
```

## ⚙️ 配置选项

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--api-key` | LTESocks API密钥 | 配置文件中的密钥 |
| `--interval` | 重置间隔（秒） | 180（3分钟） |
| `--once` | 只执行一次重置 | False |
| `--status-interval` | 状态打印间隔（秒） | 30 |

### 配置文件

编辑 `scheduler_config.json` 文件来修改默认配置：

```json
{
  "ltesocks": {
    "api_key": "your_api_key_here",
    "base_url": "https://api.ltesocks.io/v2"
  },
  "scheduler": {
    "reset_interval": 180,
    "status_interval": 30,
    "max_retry_attempts": 3,
    "retry_delay": 10
  }
}
```

## 📊 使用示例

### 基本使用

```bash
# 启动定时任务，每3分钟重置一次
python start_port_reset_scheduler.py
```

输出示例：
```
2024-01-01 10:00:00 - INFO - 🚀 定时任务已启动，每 180 秒重置一次端口
2024-01-01 10:00:00 - INFO - 按 Ctrl+C 停止任务

============================================================
🔄 开始重置所有活跃端口...
2024-01-01 10:00:01 - INFO - 获取到 5 个活跃端口
2024-01-01 10:00:01 - INFO - 正在重置第 1/5 个端口: 12345
2024-01-01 10:00:02 - INFO - ✅ 端口 12345 重置成功 - 状态: active
...
2024-01-01 10:00:10 - INFO - 🎯 重置结果: 重置完成: 成功 5, 失败 0
2024-01-01 10:00:10 - INFO - ⏱️ 耗时: 8.45秒
============================================================
```

### 自定义间隔

```bash
# 每1分钟重置一次
python start_port_reset_scheduler.py --interval 60

# 每10分钟重置一次
python start_port_reset_scheduler.py --interval 600
```

### 一次性重置

```bash
# 立即重置所有端口，不启动定时任务
python start_port_reset_scheduler.py --once
```

## 📈 状态监控

程序运行时会定期显示状态信息：

```
==================================================
📊 LTESocks端口重置调度器状态
==================================================
运行状态: 🟢 运行中
重置间隔: 180秒 (3.0分钟)
已执行重置: 15次
连续失败: 0次
上次重置: 2024-01-01T10:45:00

📈 统计信息:
启动时间: 2024-01-01T10:00:00
总重置次数: 15
成功重置端口: 75
失败次数: 0
==================================================
```

## 📝 日志文件

程序会自动生成日志文件 `ltesocks_scheduler.log`，记录所有操作：

```
2024-01-01 10:00:00,123 - INFO - LTESocks端口重置调度器初始化完成
2024-01-01 10:00:00,124 - INFO - 重置间隔: 180秒 (3.0分钟)
2024-01-01 10:00:00,125 - INFO - 🚀 定时任务开始运行...
2024-01-01 10:00:01,200 - INFO - 🔄 开始重置所有活跃端口...
2024-01-01 10:00:01,250 - INFO - 获取到 5 个活跃端口
2024-01-01 10:00:02,100 - INFO - ✅ 端口 12345 重置成功 - 状态: active
```

## 🔧 高级用法

### 作为系统服务运行（Linux）

创建systemd服务文件 `/etc/systemd/system/ltesocks-scheduler.service`：

```ini
[Unit]
Description=LTESocks Port Reset Scheduler
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/python3 /path/to/your/project/start_port_reset_scheduler.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable ltesocks-scheduler
sudo systemctl start ltesocks-scheduler
sudo systemctl status ltesocks-scheduler
```

### 作为Windows服务运行

可以使用 `nssm` 或 `pywin32` 将程序注册为Windows服务。

## ⚠️ 注意事项

1. **API密钥安全**: 请妥善保管您的LTESocks API密钥
2. **网络连接**: 确保网络连接稳定，程序需要访问LTESocks API
3. **重置频率**: 不建议设置过于频繁的重置间隔，可能会影响服务稳定性
4. **权限要求**: 确保API密钥有足够的权限执行端口重置操作
5. **资源消耗**: 程序运行时会占用少量系统资源

## 🐛 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 确认LTESocks服务状态

2. **端口重置失败**
   - 检查端口是否存在
   - 确认端口状态是否允许重置
   - 验证账户权限和余额

3. **程序无法启动**
   - 检查Python环境
   - 确认依赖文件存在
   - 查看错误日志

### 调试模式

启用详细日志输出：

```python
# 修改日志级别为DEBUG
logging.basicConfig(level=logging.DEBUG)
```

## 📞 技术支持

如果遇到问题，请检查：

1. 日志文件 `ltesocks_scheduler.log`
2. LTESocks API状态
3. 网络连接状况
4. API密钥有效性

## 🔄 更新日志

- **v1.0.0**: 初始版本，支持基本的定时重置功能
- 支持自定义重置间隔
- 添加状态监控和日志记录
- 提供多种启动方式
