#!/usr/bin/env python3
"""
SeleniumBase简单测试脚本
验证SeleniumBase UC Mode是否正常工作
"""

import sys
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_seleniumbase_import():
    """测试SeleniumBase导入"""
    try:
        from seleniumbase import SB
        logging.info("✅ SeleniumBase导入成功")
        return True
    except ImportError as e:
        logging.error(f"❌ SeleniumBase导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from seleniumbase import SB
        
        logging.info("🧪 开始基本功能测试...")
        
        with SB(uc=True, test=True, incognito=True, headless=False) as sb:
            # 测试访问页面
            logging.info("📄 访问测试页面...")
            sb.open("https://seleniumbase.io/demo_page")
            
            # 检查页面标题
            title = sb.get_title()
            logging.info(f"📋 页面标题: {title}")
            
            # 测试元素查找
            if sb.is_element_visible("#myTextInput"):
                logging.info("✅ 找到文本输入框")
                sb.type("#myTextInput", "SeleniumBase测试")
                logging.info("✅ 文本输入成功")
            
            # 测试按钮点击
            if sb.is_element_visible("#myButton"):
                logging.info("✅ 找到按钮")
                sb.click("#myButton")
                logging.info("✅ 按钮点击成功")
            
            # 等待一下
            sb.sleep(2)
            
        logging.info("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        logging.error(f"❌ 基本功能测试失败: {e}")
        return False

def test_uc_mode():
    """测试UC Mode反检测功能"""
    try:
        from seleniumbase import SB
        
        logging.info("🤖 开始UC Mode反检测测试...")
        
        with SB(uc=True, test=True, incognito=True) as sb:
            # 测试访问可能有反机器人检测的页面
            test_url = "https://www.smartbackgroundchecks.com"
            logging.info(f"🌐 访问测试URL: {test_url}")
            
            # 使用UC Mode打开页面
            sb.uc_open_with_reconnect(test_url, reconnect_time=3)
            
            # 尝试处理CAPTCHA
            try:
                sb.uc_gui_handle_captcha()
                logging.info("🔓 CAPTCHA处理完成")
            except Exception as e:
                logging.info(f"ℹ️ 无需处理CAPTCHA: {e}")
            
            # 检查页面是否正常加载
            sb.wait_for_ready_state_complete(timeout=10)
            
            current_url = sb.get_current_url()
            logging.info(f"📍 当前URL: {current_url}")
            
            page_source_length = len(sb.get_page_source())
            logging.info(f"📄 页面源码长度: {page_source_length} 字符")
            
            if page_source_length > 1000:
                logging.info("✅ UC Mode反检测测试通过")
                return True
            else:
                logging.warning("⚠️ 页面内容可能不完整")
                return False
        
    except Exception as e:
        logging.error(f"❌ UC Mode测试失败: {e}")
        return False

def test_phone_scraping_logic():
    """测试电话号码爬取逻辑"""
    try:
        from seleniumbase import SB
        from bs4 import BeautifulSoup
        
        logging.info("📞 开始电话号码爬取逻辑测试...")
        
        with SB(uc=True, test=True, incognito=True) as sb:
            # 测试访问一个电话号码页面
            test_phone = "5551234567"
            test_url = f"https://www.smartbackgroundchecks.com/phone/{test_phone}"
            
            logging.info(f"🔍 测试电话号码: {test_phone}")
            logging.info(f"🌐 访问URL: {test_url}")
            
            # 使用UC Mode打开页面
            sb.uc_open_with_reconnect(test_url, reconnect_time=2)
            
            # 处理可能的CAPTCHA
            try:
                sb.uc_gui_handle_captcha()
            except Exception:
                pass
            
            # 等待页面加载
            sb.wait_for_ready_state_complete(timeout=10)
            sb.sleep(2)
            
            # 获取页面源码
            page_source = sb.get_page_source()
            logging.info(f"📄 页面源码长度: {len(page_source)} 字符")
            
            # 使用BeautifulSoup解析
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 查找背景报告按钮
            button_selectors = [
                'a.btn.btn-primary.btn-sm.btn-block.text-center',
                'a[class*="btn"][class*="primary"]',
                'a[href*="/people/"]'
            ]
            
            button_found = False
            for selector in button_selectors:
                try:
                    if sb.is_element_visible(selector, timeout=2):
                        href = sb.get_attribute(selector, "href")
                        if href:
                            logging.info(f"✅ 找到背景报告按钮: {href}")
                            button_found = True
                            break
                except Exception:
                    continue
            
            if button_found:
                logging.info("✅ 电话号码爬取逻辑测试通过")
                return True
            else:
                logging.warning("⚠️ 未找到背景报告按钮，可能需要调整选择器")
                return False
        
    except Exception as e:
        logging.error(f"❌ 电话号码爬取逻辑测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 SeleniumBase功能测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_seleniumbase_import),
        ("基本功能测试", test_basic_functionality),
        ("UC Mode反检测测试", test_uc_mode),
        ("电话号码爬取逻辑测试", test_phone_scraping_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name}通过")
                passed += 1
            else:
                print(f"❌ {test_name}失败")
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！SeleniumBase配置正确。")
        print("💡 现在可以运行: python seleniumbase_batch_scraper.py")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
