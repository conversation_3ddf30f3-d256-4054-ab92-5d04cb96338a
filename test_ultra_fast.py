#!/usr/bin/env python3
"""
超快速页面加载测试 - 不等待页面完全加载
"""

import time
import logging
import os
import threading
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ultra_fast_test.log', mode='w', encoding='utf-8')
    ]
)

def find_chrome_path():
    """查找Chrome浏览器路径"""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"找到Chrome浏览器: {path}")
            return path
    
    logging.warning("未找到Chrome浏览器")
    return None

def stop_loading_after_delay(driver, delay=3):
    """在指定延迟后停止页面加载"""
    def stop_loading():
        time.sleep(delay)
        try:
            driver.stop_loading()
            logging.info(f"已在 {delay} 秒后停止页面加载")
        except Exception as e:
            logging.warning(f"停止加载失败: {e}")
    
    thread = threading.Thread(target=stop_loading)
    thread.daemon = True
    thread.start()

def ultra_fast_page_load(driver, url, max_wait=3):
    """超快速页面加载，最多等待指定秒数"""
    try:
        logging.info(f"开始超快速加载: {url}")
        start_time = time.time()
        
        # 启动停止加载的定时器
        stop_loading_after_delay(driver, max_wait)
        
        # 开始加载页面
        try:
            driver.get(url)
        except Exception as e:
            # 忽略超时错误，继续处理
            logging.info(f"页面加载被中断: {e}")
        
        load_time = time.time() - start_time
        logging.info(f"页面加载耗时: {load_time:.3f}秒")
        
        # 等待一下让DOM稳定
        time.sleep(0.5)
        
        return True
        
    except Exception as e:
        logging.error(f"页面加载失败: {e}")
        return False

def find_background_report_ultra_fast(driver):
    """超快速查找背景报告链接"""
    try:
        start_time = time.time()
        logging.info("开始超快速查找背景报告按钮...")
        
        # 立即获取页面源码
        page_source = driver.html
        parse_time = time.time() - start_time
        logging.info(f"获取页面源码耗时: {parse_time:.3f}秒，长度: {len(page_source)} 字符")
        
        # 快速检查是否有内容
        if len(page_source) < 1000:
            logging.warning("页面源码太短，可能未加载完成")
            return False, None
        
        # 使用BeautifulSoup解析
        soup_start = time.time()
        soup = BeautifulSoup(page_source, 'html.parser')
        soup_time = time.time() - soup_start
        logging.info(f"BeautifulSoup解析耗时: {soup_time:.3f}秒")
        
        # 直接通过class查找
        target_buttons = soup.find_all('a', class_='btn btn-primary btn-sm btn-block text-center')
        logging.info(f"找到 {len(target_buttons)} 个目标按钮")
        
        for i, button in enumerate(target_buttons):
            button_text = button.get_text().strip()
            href = button.get('href', '')
            
            logging.info(f"按钮 {i+1}: '{button_text}' -> {href}")
            
            if 'Open Free Background Report' in button_text:
                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                total_time = time.time() - start_time
                logging.info(f"✅ 找到目标按钮，总耗时: {total_time:.3f}秒")
                logging.info(f"目标链接: {href}")
                
                return True, href
        
        # 如果class方法失败，尝试文本搜索
        all_links = soup.find_all('a', href=True)
        logging.info(f"尝试文本搜索，共 {len(all_links)} 个链接")
        
        for link in all_links:
            link_text = link.get_text().strip()
            href = link.get('href', '')
            
            if 'Open Free Background Report' in link_text or 'Free Background Report' in link_text:
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                total_time = time.time() - start_time
                logging.info(f"✅ 通过文本搜索找到目标，总耗时: {total_time:.3f}秒")
                return True, href
        
        total_time = time.time() - start_time
        logging.warning(f"❌ 未找到目标按钮，总耗时: {total_time:.3f}秒")
        return False, None
        
    except Exception as e:
        total_time = time.time() - start_time
        logging.error(f"查找过程出错，耗时: {total_time:.3f}秒，错误: {e}")
        return False, None

def main():
    """主测试函数"""
    logging.info("开始超快速页面加载测试")
    logging.info("=" * 60)
    
    # 查找浏览器
    chrome_path = find_chrome_path()
    if not chrome_path:
        logging.error("无法找到Chrome浏览器")
        return
    
    # 创建最快的浏览器配置
    try:
        options = ChromiumOptions()
        options.set_paths(browser_path=chrome_path)
        
        # 极速参数配置
        ultra_fast_args = [
            "--no-first-run",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-web-security",
            "--disable-extensions",
            "--disable-images",
            "--disable-javascript",  # 禁用JS
            "--disable-plugins",
            "--disable-background-timer-throttling",
            "--aggressive-cache-discard",
            "--disable-background-networking",
            "--disable-sync",
            "--disable-translate",
            "--disable-ipc-flooding-protection",
            "--disable-client-side-phishing-detection",
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-domain-reliability",
            "--disable-features=TranslateUI,BlinkGenPropertyTrees",
            "--page-load-strategy=none",  # 不等待任何加载完成
        ]
        
        for arg in ultra_fast_args:
            options.set_argument(arg)
            
        logging.info("超快速浏览器选项配置完成")
        
    except Exception as e:
        logging.error(f"配置浏览器选项失败: {e}")
        return
    
    # 启动浏览器并测试
    try:
        total_start = time.time()
        
        logging.info("正在启动浏览器...")
        browser_start = time.time()
        driver = ChromiumPage(addr_or_opts=options)
        browser_time = time.time() - browser_start
        logging.info(f"浏览器启动耗时: {browser_time:.3f}秒")
        
        # 设置极短的超时
        try:
            driver.set.timeouts(page_load=3)  # 3秒超时
            logging.info("设置页面加载超时为3秒")
        except Exception as e:
            logging.warning(f"设置超时失败: {e}")
        
        # 超快速页面加载
        target_url = "https://www.smartbackgroundchecks.com/phone/5619324217"
        if ultra_fast_page_load(driver, target_url, max_wait=3):
            
            # 快速检查Cloudflare
            cf_start = time.time()
            page_source = driver.html
            has_cloudflare = any(keyword in page_source.lower() for keyword in ["cloudflare", "just a moment", "checking your browser"])
            cf_time = time.time() - cf_start
            logging.info(f"Cloudflare检测耗时: {cf_time:.3f}秒")
            
            if has_cloudflare:
                logging.info("检测到Cloudflare保护，开始绕过...")
                bypass_start = time.time()
                cf_bypasser = CloudflareBypasser(driver)
                cf_bypasser.bypass()
                bypass_time = time.time() - bypass_start
                logging.info(f"Cloudflare绕过耗时: {bypass_time:.3f}秒")
            
            # 超快速查找背景报告
            logging.info("=" * 50)
            button_found, button_url = find_background_report_ultra_fast(driver)
            
            if button_found:
                logging.info(f"✅ 成功找到背景报告按钮!")
                logging.info(f"目标链接: {button_url}")
                
                # 超快速访问新页面
                logging.info("开始超快速访问背景报告页面...")
                if ultra_fast_page_load(driver, button_url, max_wait=3):
                    
                    # 获取新页面信息
                    time.sleep(0.5)
                    new_page_source = driver.html
                    soup = BeautifulSoup(new_page_source, 'html.parser')
                    new_title = soup.title.string if soup.title else "无标题"
                    
                    logging.info(f"新页面标题: {new_title}")
                    logging.info(f"新页面源码长度: {len(new_page_source)} 字符")
                    
                    # 保存新页面源码
                    with open('ultra_fast_background_report.html', 'w', encoding='utf-8') as f:
                        f.write(new_page_source)
                    logging.info("背景报告页面源码已保存到 ultra_fast_background_report.html")
                
            else:
                logging.error("❌ 未找到背景报告按钮")
        
        total_time = time.time() - total_start
        logging.info("=" * 60)
        logging.info(f"🚀 总执行时间: {total_time:.3f}秒")
        
    except Exception as e:
        total_time = time.time() - total_start
        logging.error(f"测试过程中出错，总耗时: {total_time:.3f}秒，错误: {e}")
    finally:
        try:
            driver.quit()
            logging.info("浏览器已关闭")
        except:
            pass
    
    logging.info("超快速测试完成")

if __name__ == '__main__':
    main()
