#!/usr/bin/env python3
"""
SeleniumBase批量爬取器启动脚本
提供友好的用户界面和自动化设置
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import seleniumbase
        logging.info(f"✅ SeleniumBase已安装，版本: {seleniumbase.__version__}")
        return True
    except ImportError:
        logging.error("❌ SeleniumBase未安装")
        return False

def check_phone_file(phone_file):
    """检查电话号码文件"""
    if not os.path.exists(phone_file):
        logging.error(f"❌ 电话号码文件不存在: {phone_file}")
        return False
    
    try:
        with open(phone_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            valid_phones = [line.strip() for line in lines if line.strip()]
            
        if not valid_phones:
            logging.error(f"❌ 电话号码文件为空: {phone_file}")
            return False
        
        logging.info(f"✅ 找到 {len(valid_phones)} 个电话号码")
        return True
        
    except Exception as e:
        logging.error(f"❌ 读取电话号码文件失败: {e}")
        return False

def create_sample_phone_file():
    """创建示例电话号码文件"""
    sample_phones = [
        "5551234567",
        "5559876543", 
        "5555555555",
        "5551111111",
        "5552222222"
    ]
    
    try:
        with open("KK1000.txt", 'w', encoding='utf-8') as f:
            for phone in sample_phones:
                f.write(f"1{phone}\n")  # 添加前缀1
        
        logging.info("✅ 创建示例电话号码文件: KK1000.txt")
        return True
        
    except Exception as e:
        logging.error(f"❌ 创建示例文件失败: {e}")
        return False

def get_user_input():
    """获取用户输入的配置"""
    print("\n" + "="*60)
    print("🚀 SeleniumBase批量电话号码爬取器")
    print("="*60)
    
    config = {}
    
    # 电话号码文件
    phone_file = input("📁 电话号码文件路径 (默认: KK1000.txt): ").strip()
    if not phone_file:
        phone_file = "KK1000.txt"
    config['phone_file'] = phone_file
    
    # 输出目录
    output_dir = input("📂 输出目录 (默认: scraped_data): ").strip()
    if not output_dir:
        output_dir = "scraped_data"
    config['output_dir'] = output_dir
    
    # 最大处理数量
    max_phones = input("🔢 最大处理数量 (默认: 无限制): ").strip()
    if max_phones and max_phones.isdigit():
        config['max_phones'] = int(max_phones)
    else:
        config['max_phones'] = None
    
    # 延迟时间
    delay = input("⏰ 基础延迟时间/秒 (默认: 2.0): ").strip()
    try:
        config['delay'] = float(delay) if delay else 2.0
    except ValueError:
        config['delay'] = 2.0
    
    return config

def build_command(config):
    """构建运行命令"""
    cmd = ["python", "seleniumbase_batch_scraper.py"]
    
    cmd.extend(["--phone-file", config['phone_file']])
    cmd.extend(["--output-dir", config['output_dir']])
    cmd.extend(["--delay", str(config['delay'])])
    
    if config['max_phones']:
        cmd.extend(["--max-phones", str(config['max_phones'])])
    
    return cmd

def run_scraper(config):
    """运行爬取器"""
    cmd = build_command(config)
    cmd_str = " ".join(cmd)
    
    print(f"\n🚀 启动命令: {cmd_str}")
    print("="*60)
    
    try:
        # 运行爬取器
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        logging.error(f"❌ 运行爬取器失败: {e}")
        return False

def show_menu():
    """显示主菜单"""
    while True:
        print("\n" + "="*60)
        print("🎯 SeleniumBase批量爬取器 - 主菜单")
        print("="*60)
        print("1. 🔧 安装/检查依赖")
        print("2. 🧪 运行功能测试")
        print("3. 📝 创建示例电话号码文件")
        print("4. 🚀 启动批量爬取")
        print("5. 📖 查看使用说明")
        print("6. 🚪 退出")
        print("="*60)
        
        choice = input("请选择操作 (1-6): ").strip()
        
        if choice == "1":
            install_dependencies()
        elif choice == "2":
            run_tests()
        elif choice == "3":
            create_sample_phone_file()
        elif choice == "4":
            start_scraping()
        elif choice == "5":
            show_usage()
        elif choice == "6":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

def install_dependencies():
    """安装依赖"""
    print("\n🔧 检查和安装依赖...")
    
    if check_dependencies():
        print("✅ 所有依赖已安装")
        return
    
    print("正在安装SeleniumBase...")
    try:
        subprocess.run([sys.executable, "install_seleniumbase.py"], check=True)
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败，请手动安装")
    except FileNotFoundError:
        print("❌ 找不到install_seleniumbase.py文件")

def run_tests():
    """运行测试"""
    print("\n🧪 运行功能测试...")
    
    if not check_dependencies():
        print("❌ 请先安装依赖")
        return
    
    try:
        subprocess.run([sys.executable, "test_seleniumbase.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 测试失败")
    except FileNotFoundError:
        print("❌ 找不到test_seleniumbase.py文件")

def start_scraping():
    """开始爬取"""
    print("\n🚀 启动批量爬取...")
    
    if not check_dependencies():
        print("❌ 请先安装依赖")
        return
    
    # 获取配置
    config = get_user_input()
    
    # 检查电话号码文件
    if not check_phone_file(config['phone_file']):
        create_file = input("是否创建示例电话号码文件? (y/n): ").strip().lower()
        if create_file == 'y':
            if create_sample_phone_file():
                config['phone_file'] = "KK1000.txt"
            else:
                return
        else:
            return
    
    # 确认配置
    print(f"\n📋 配置确认:")
    print(f"电话号码文件: {config['phone_file']}")
    print(f"输出目录: {config['output_dir']}")
    print(f"最大处理数量: {config['max_phones'] or '无限制'}")
    print(f"基础延迟: {config['delay']}秒")
    
    confirm = input("\n确认开始爬取? (y/n): ").strip().lower()
    if confirm == 'y':
        success = run_scraper(config)
        if success:
            print("🎉 爬取完成！")
        else:
            print("⚠️ 爬取过程中出现问题")
    else:
        print("❌ 取消操作")

def show_usage():
    """显示使用说明"""
    print("\n📖 使用说明:")
    print("="*60)
    print("1. 首先安装依赖: 选择菜单选项1")
    print("2. 运行功能测试: 选择菜单选项2")
    print("3. 准备电话号码文件 (每行一个号码)")
    print("4. 启动批量爬取: 选择菜单选项4")
    print("5. 查看输出目录中的CSV文件获取结果")
    print("\n💡 提示:")
    print("- 建议先用少量电话号码测试")
    print("- 可以随时按Ctrl+C中断程序")
    print("- 程序支持断点续传")
    print("- 详细说明请查看README_SELENIUMBASE.md")

def main():
    """主函数"""
    try:
        show_menu()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        logging.error(f"程序异常: {e}")

if __name__ == "__main__":
    main()
