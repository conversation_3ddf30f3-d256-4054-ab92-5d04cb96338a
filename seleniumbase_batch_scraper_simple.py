#!/usr/bin/env python3
"""
SeleniumBase批量电话号码背景报告爬取器 - 简化版本
基于Checkpoint 17的成功版本
"""

import os
import sys
import csv
import time
import random
import logging
import traceback
import re
from datetime import datetime
from typing import Dict, List, Tuple
from bs4 import BeautifulSoup
from seleniumbase import SB

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

class SeleniumBaseBatchScraper:
    def __init__(self, phone_file: str, output_dir: str):
        self.phone_file = phone_file
        self.output_dir = output_dir
        self.processed_phones_file = os.path.join(output_dir, 'processed_phones.txt')
        self.unified_csv_file = os.path.join(output_dir, 'all_phone_data.csv')
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载已处理的电话号码
        self.processed_phones = self.load_processed_phones()
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'current_phone': None
        }
        
        # 自适应延迟参数
        self.min_delay = 1.0
        self.max_delay = 3.0
        self.adaptive_delay = 1.5
        self.success_count = 0
        self.failure_count = 0
        
        logging.info(f"加载了 {len(self.processed_phones)} 个已处理的电话号码")
        logging.info("SeleniumBase批量爬取器初始化完成")

    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        if os.path.exists(self.processed_phones_file):
            with open(self.processed_phones_file, 'r', encoding='utf-8') as f:
                return set(line.strip() for line in f if line.strip())
        return set()

    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        with open(self.processed_phones_file, 'a', encoding='utf-8') as f:
            f.write(f"{phone}\n")
        self.processed_phones.add(phone)

    def load_phone_numbers(self) -> List[str]:
        """从文件加载电话号码"""
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                phones = []
                for line in f:
                    phone = line.strip()
                    if phone and phone.isdigit() and len(phone) >= 10:
                        # 移除前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                        if len(phone) == 10:
                            phones.append(phone)
                
                logging.info(f"成功加载 {len(phones)} 个有效电话号码")
                return phones
        except Exception as e:
            logging.error(f"加载电话号码失败: {e}")
            return []

    def init_unified_csv(self):
        """初始化统一的CSV文件"""
        try:
            if not os.path.exists(self.unified_csv_file):
                with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['电话号码', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                logging.info(f"创建新的CSV文件: {self.unified_csv_file}")
            else:
                logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
        except Exception as e:
            logging.error(f"初始化CSV文件失败: {e}")

    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """立即保存数据到CSV文件"""
        try:
            self.init_unified_csv()
            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    data_type,
                    info.get('姓名', ''),
                    info.get('年龄', ''),
                    info.get('手机', ''),
                    info.get('地址', ''),
                    info.get('关系', ''),
                    info.get('链接', '')
                ])
        except Exception as e:
            logging.error(f"保存CSV数据失败: {e}")

    def calculate_adaptive_delay(self, success: bool) -> float:
        """计算自适应延迟"""
        if success:
            self.success_count += 1
            self.failure_count = max(0, self.failure_count - 1)
        else:
            self.failure_count += 1
            self.success_count = max(0, self.success_count - 1)
        
        # 根据成功/失败比例调整延迟
        if self.failure_count > self.success_count:
            self.adaptive_delay = min(self.max_delay, self.adaptive_delay * 1.2)
        elif self.success_count > self.failure_count * 2:
            self.adaptive_delay = max(self.min_delay, self.adaptive_delay * 0.9)
        
        # 添加随机性
        return self.adaptive_delay + random.uniform(-0.3, 0.3)

    def extract_person_info_sb(self, soup) -> Dict:
        """提取个人信息"""
        try:
            person_info = {}
            
            # 查找姓名
            name_selectors = [
                'h1.text-dark',
                'h1',
                '.person-name',
                '.profile-name'
            ]
            
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem:
                    person_info['姓名'] = name_elem.get_text().strip()
                    break
            
            # 查找年龄
            age_pattern = r'(\d+)\s*years?\s*old'
            page_text = soup.get_text()
            age_match = re.search(age_pattern, page_text, re.IGNORECASE)
            if age_match:
                person_info['年龄'] = age_match.group(1)
            
            # 查找电话号码
            phone_pattern = r'\((\d{3})\)\s*(\d{3})-(\d{4})'
            phone_match = re.search(phone_pattern, page_text)
            if phone_match:
                person_info['手机'] = f"({phone_match.group(1)}){phone_match.group(2)}-{phone_match.group(3)}"
            
            # 查找地址
            address_patterns = [
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),\s*([A-Z]{2})',
                r'([A-Z][a-z\s]+),\s*([A-Z]{2})\s*\d{5}'
            ]
            
            for pattern in address_patterns:
                addr_match = re.search(pattern, page_text)
                if addr_match:
                    person_info['地址'] = f"{addr_match.group(1)}, {addr_match.group(2)}"
                    break
            
            logging.info(f"提取到个人信息: {person_info}")
            return person_info
            
        except Exception as e:
            logging.error(f"提取个人信息失败: {e}")
            return {}

    def extract_relatives_info_sb(self, soup, min_age: int = 40) -> List[Dict]:
        """提取亲属信息 - 简化版本"""
        try:
            relatives = []
            
            # 查找亲属信息区域
            relatives_section = soup.find(string=re.compile(r'Relatives Found', re.IGNORECASE))
            if not relatives_section:
                logging.warning("未找到亲属信息区域")
                return relatives
            
            # 找到包含亲属信息的父元素
            relatives_container = relatives_section.parent
            while relatives_container and relatives_container.name != 'div':
                relatives_container = relatives_container.parent
            
            if not relatives_container:
                logging.warning("未找到亲属信息容器")
                return relatives
            
            # 查找所有包含年龄信息的div元素
            card_blocks = relatives_container.find_all('div', class_='card-block')
            
            for card in card_blocks:
                # 查找链接和年龄信息
                link = card.find('a', class_='link-underline')
                small_tag = card.find('small')
                
                if link and small_tag:
                    name = link.get_text().strip()
                    age_text = small_tag.get_text().strip()
                    
                    # 提取年龄数字
                    age_match = re.search(r'(\d+)\s*years?\s*old', age_text, re.IGNORECASE)
                    if age_match:
                        age = int(age_match.group(1))
                        
                        # 只保留年龄>=min_age的亲属
                        if age >= min_age and name:
                            relative_info = {
                                '姓名': name,
                                '年龄': age,
                                '关系': '亲属',
                                '链接': link.get('href', '')
                            }
                            relatives.append(relative_info)
                            logging.info(f"✅ 找到符合条件的亲属: {name}, {age}岁")
            
            logging.info(f"📊 共找到 {len(relatives)} 个符合条件的亲属（年龄>={min_age}）")
            return relatives
            
        except Exception as e:
            logging.error(f"提取亲属信息失败: {e}")
            return []

    def find_background_report_button_sb(self, sb) -> Tuple[bool, str]:
        """查找背景报告按钮"""
        try:
            page_source = sb.cdp.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 查找包含特定文本的链接
            target_texts = ['open free background report', 'view background report', 'background report']
            
            for text in target_texts:
                links = soup.find_all('a', string=re.compile(text, re.IGNORECASE))
                for link in links:
                    href = link.get('href')
                    if href and '/people/' in href:
                        if href.startswith('/'):
                            current_url = sb.cdp.get_current_url()
                            base_url = f"{current_url.split('/')[0]}//{current_url.split('/')[2]}"
                            href = base_url + href
                        logging.info(f"✅ 选择最佳链接: {href}")
                        return True, href
            
            return False, ""
            
        except Exception as e:
            logging.error(f"查找背景报告按钮失败: {e}")
            return False, ""

    def click_relative_link_and_extract_sb(self, sb, relative_name: str, relative_link: str) -> Dict:
        """点击亲属链接并提取详细信息"""
        try:
            logging.info(f"开始访问亲属页面: {relative_name} -> {relative_link}")

            # 构造完整URL
            if relative_link.startswith('/'):
                current_url = sb.cdp.get_current_url()
                base_url = f"{current_url.split('/')[0]}//{current_url.split('/')[2]}"
                full_url = base_url + relative_link
            else:
                full_url = relative_link

            # 访问亲属页面
            sb.cdp.get(full_url)
            sb.sleep(1.5)

            # 获取页面源码并解析
            page_source = sb.cdp.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取亲属详细信息
            relative_details = self.extract_person_info_sb(soup)
            logging.info(f"提取到亲属详细信息: {relative_name} - {relative_details}")

            return relative_details

        except Exception as e:
            logging.error(f"处理亲属链接失败 {relative_name}: {e}")
            return {}

    def scrape_phone_data_sb(self, sb, phone: str) -> Tuple[bool, Dict]:
        """使用SeleniumBase爬取单个电话号码的背景报告数据"""
        start_time = time.time()
        logging.info("=" * 80)
        logging.info(f"🔍 开始处理电话号码: {phone}")

        try:
            # 自适应延迟
            delay = self.calculate_adaptive_delay(True)
            logging.info(f"⏰ 智能延迟: {delay:.2f}秒 (自适应延迟: {self.adaptive_delay:.2f})")
            sb.sleep(delay)

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"目标URL: {target_url}")

            # 使用CDP Mode访问页面
            sb.activate_cdp_mode(target_url)
            sb.sleep(1.5)

            # 自动处理可能的CAPTCHA
            try:
                sb.uc_gui_click_captcha()
                sb.sleep(1)
            except Exception as e:
                logging.debug(f"CAPTCHA处理: {e}")

            # 获取页面信息
            try:
                page_title = sb.cdp.get_title()
                current_url = sb.cdp.get_current_url()
                logging.info(f"页面标题: {page_title}")
                logging.info(f"当前URL: {current_url}")
            except Exception as e:
                logging.warning(f"获取页面信息失败: {e}")

            # 查找背景报告按钮
            button_found, button_url = self.find_background_report_button_sb(sb)

            if not button_found:
                logging.warning(f"未找到背景报告按钮: {phone}")
                self.calculate_adaptive_delay(False)
                return False, {}

            logging.info(f"找到背景报告链接: {button_url}")

            # 访问背景报告页面
            sb.cdp.get(button_url)
            sb.sleep(2)

            # 再次处理可能的CAPTCHA
            try:
                sb.uc_gui_click_captcha()
                sb.sleep(1)
            except Exception as e:
                logging.debug(f"背景报告页面CAPTCHA处理: {e}")

            # 等待页面内容加载
            sb.sleep(1)

            # 获取页面源码并使用BeautifulSoup解析
            page_source = sb.cdp.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取个人信息
            person_info = self.extract_person_info_sb(soup)
            if not person_info.get('姓名'):
                logging.warning(f"未提取到个人信息: {phone}")
                self.calculate_adaptive_delay(False)
                return False, {}

            # 保存个人信息到CSV
            self.save_to_csv(phone, "本人", person_info)
            logging.info(f"✅ 已保存本人: {person_info.get('姓名', 'Unknown')}")

            # 提取亲属信息
            relatives_info = self.extract_relatives_info_sb(soup, min_age=40)
            logging.info(f"找到 {len(relatives_info)} 个符合条件的亲属")

            # 处理每个亲属的详细信息
            detailed_relatives_count = 0
            for relative in relatives_info:
                try:
                    relative_name = relative.get('姓名', '')
                    relative_link = relative.get('链接', '')

                    if relative_link:
                        # 点击亲属链接并提取信息
                        relative_details = self.click_relative_link_and_extract_sb(sb, relative_name, relative_link)

                        if relative_details:
                            # 合并基本信息和详细信息
                            combined_info = {**relative, **relative_details}
                            self.save_to_csv(phone, "亲属", combined_info)
                            detailed_relatives_count += 1
                            logging.info(f"✅ 已保存亲属: {relative_name}")
                        else:
                            # 即使没有详细信息，也保存基本信息
                            self.save_to_csv(phone, "亲属", relative)
                            detailed_relatives_count += 1
                            logging.info(f"✅ 已保存亲属基本信息: {relative_name}")
                    else:
                        # 没有链接的情况下，保存基本信息
                        self.save_to_csv(phone, "亲属", relative)
                        detailed_relatives_count += 1
                        logging.info(f"✅ 已保存亲属基本信息: {relative_name}")

                except Exception as e:
                    logging.error(f"处理亲属信息失败 {relative.get('姓名', 'Unknown')}: {e}")
                    continue

            duration = time.time() - start_time
            logging.info(f"✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"成功保存亲属数量: {detailed_relatives_count}")

            # 更新成功状态
            self.calculate_adaptive_delay(True)

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"错误详情: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")

            # 更新失败状态
            self.calculate_adaptive_delay(False)

            return False, {}

    def run_seleniumbase_batch_scraping(self, max_phones: int = None, base_delay: float = 1.5) -> Dict:
        """运行SeleniumBase批量爬取"""
        self.stats['start_time'] = datetime.now()
        self.min_delay = base_delay
        self.max_delay = base_delay * 2.5
        self.adaptive_delay = base_delay

        logging.info("🚀 开始SeleniumBase批量爬取任务")
        logging.info(f"基础延迟: {base_delay}秒")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 使用SeleniumBase UC Mode + CDP Mode
        with SB(uc=True, test=True, incognito=True, locale="en", ad_block=True) as sb:
            try:
                logging.info("🎯 开始处理电话号码，跳过预热以避免会话干扰")

                # 处理每个电话号码
                for i, phone in enumerate(unprocessed_phones, 1):
                    self.stats['current_phone'] = phone
                    self.stats['processed_phones'] = i

                    logging.info(f"\n📞 处理进度: {i}/{len(unprocessed_phones)} ({i/len(unprocessed_phones)*100:.1f}%)")
                    logging.info("=" * 80)

                    try:
                        success, _ = self.scrape_phone_data_sb(sb, phone)

                        if success:
                            self.stats['successful_scrapes'] += 1
                            self.save_processed_phone(phone)
                            logging.info(f"✅ 成功处理: {phone}")
                        else:
                            self.stats['failed_scrapes'] += 1
                            logging.error(f"❌ 处理失败: {phone}")

                    except Exception as e:
                        self.stats['failed_scrapes'] += 1
                        logging.error(f"❌ 处理电话号码 {phone} 时发生异常: {e}")
                        logging.error(f"异常堆栈: {traceback.format_exc()}")

                    # 打印当前统计
                    self.print_stats()

            except KeyboardInterrupt:
                logging.info("收到中断信号，正在优雅退出...")
            except Exception as e:
                logging.error(f"批量处理过程中发生严重错误: {e}")
                logging.error(f"错误堆栈: {traceback.format_exc()}")

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 SeleniumBase批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        if self.stats['processed_phones'] > 0:
            success_rate = self.stats['successful_scrapes']/self.stats['processed_phones']*100
            avg_time = duration / self.stats['processed_phones']
            logging.info(f"成功率: {success_rate:.1f}%")
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
        logging.info(f"最终自适应延迟: {self.adaptive_delay:.2f}秒")
        logging.info("=" * 80)

        return self.stats

    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"自适应延迟: {self.adaptive_delay:.2f}s, 耗时: {elapsed:.0f}秒")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='SeleniumBase批量电话号码背景报告爬取器 - 简化版本')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=float, default=1.5, help='基础延迟时间（秒）')

    args = parser.parse_args()

    try:
        # 创建SeleniumBase爬取器
        scraper = SeleniumBaseBatchScraper(args.phone_file, args.output_dir)

        # 运行SeleniumBase批量爬取
        stats = scraper.run_seleniumbase_batch_scraping(args.max_phones, args.delay)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
