@echo off
chcp 65001 >nul
title SeleniumBase批量电话号码爬取器

echo.
echo ================================================================
echo 🚀 SeleniumBase批量电话号码爬取器
echo ================================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

echo.
echo 正在启动SeleniumBase爬取器...
echo.

REM 运行Python启动脚本
python start_seleniumbase_scraper.py

echo.
echo 程序已结束，按任意键退出...
pause >nul
