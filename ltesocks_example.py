"""
LTESocks API客户端使用示例
演示如何使用LTESocks客户端进行各种操作
"""

import asyncio
from ltesocks_client import LTESocksClient, LTESocksAPIError, PlanTarification, PortCredentials
from datetime import datetime, timedelta


def main():
    """主函数 - 演示LTESocks API的使用"""
    
    # 初始化客户端
    api_key = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
    client = LTESocksClient(api_key)
    
    try:
        print("=== LTESocks API客户端示例 ===\n")
        
        # 1. 获取用户信息
        print("1. 获取用户信息:")
        user = client.get_user()
        print(f"   用户: {user.login}")
        print(f"   邮箱: {user.email}")
        print(f"   余额: ${user.balance / 100:.2f}")
        print(f"   端口数量: {user.portsCount}/{user.portsLimit}")
        print()
        
        # 2. 获取所有端口
        print("2. 获取所有端口:")
        ports_response = client.get_ports()
        print(f"   总端口数: {ports_response.total}")
        for port in ports_response.data[:3]:  # 只显示前3个
            print(f"   端口 {port.port}: {port.ip} ({port.status}) - {port.plan.countryCode}")
        print()
        
        # 3. 获取活跃端口
        print("3. 获取活跃端口:")
        active_ports = client.get_active_ports()
        print(f"   活跃端口数: {len(active_ports)}")
        for port in active_ports[:2]:  # 只显示前2个
            print(f"   端口 {port.port}: {port.ip} - {port.plan.name}")
        print()
        
        # 4. 根据国家获取端口
        print("4. 获取美国端口:")
        us_ports = client.get_ports_by_country("US")
        print(f"   美国端口数: {len(us_ports)}")
        print()
        
        # 5. 获取计划列表
        print("5. 获取计划列表:")
        plans = client.get_plans()
        print(f"   可用计划数: {len(plans.plans)}")
        for plan in plans.plans[:3]:  # 只显示前3个
            print(f"   计划: {plan.name} ({plan.countryCode}) - 可用: {plan.available}")
        print()
        
        # 6. 获取服务器列表
        print("6. 获取服务器列表:")
        servers = client.get_servers()
        print(f"   VPN服务器数: {len(servers.vpnServers)}")
        print(f"   代理服务器数: {len(servers.proxyServers)}")
        print(f"   支持的协议: {', '.join(servers.proxyProtocols)}")
        print()
        
        # 7. 如果有端口，演示端口操作
        if active_ports:
            port_id = active_ports[0].port
            print(f"7. 端口操作示例 (端口 {port_id}):")
            
            # 获取端口详情
            port_detail = client.get_port(port_id)
            print(f"   端口详情: {port_detail.ip} - {port_detail.status}")
            
            # 获取端口代理信息
            proxy_info = client.get_port_proxy_info(port_id)
            print(f"   代理信息: {proxy_info['host']}:{proxy_info['port']}")
            
            # 获取端口日志
            try:
                logs = client.get_port_log(port_id)
                print(f"   日志条数: {logs.total}")
            except LTESocksAPIError as e:
                print(f"   获取日志失败: {e}")
            
            print()
        
        # 8. 获取支付历史
        print("8. 获取支付历史:")
        try:
            payments = client.get_payments_history()
            print(f"   支付记录数: {payments.total}")
        except LTESocksAPIError as e:
            print(f"   获取支付历史失败: {e}")
        print()
        
        # 9. 获取支持的货币
        print("9. 获取支持的货币:")
        try:
            currencies = client.get_currencies()
            print(f"   支持的货币: {', '.join(currencies)}")
        except LTESocksAPIError as e:
            print(f"   获取货币列表失败: {e}")
        print()
        
        # 10. 获取可用签名
        print("10. 获取可用签名:")
        try:
            signatures = client.get_signatures()
            print(f"   可用签名: {', '.join(signatures)}")
        except LTESocksAPIError as e:
            print(f"   获取签名列表失败: {e}")
        print()
        
    except LTESocksAPIError as e:
        print(f"API错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")


def demo_port_management():
    """演示端口管理功能"""
    
    api_key = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
    client = LTESocksClient(api_key)
    
    try:
        print("=== 端口管理示例 ===\n")
        
        # 获取活跃端口
        active_ports = client.get_active_ports()
        if not active_ports:
            print("没有活跃的端口")
            return
        
        port_id = active_ports[0].port
        print(f"使用端口: {port_id}")
        
        # 1. 重置端口
        print("1. 重置端口:")
        try:
            reset_port = client.reset_port(port_id)
            print(f"   重置成功: {reset_port.status}")
        except LTESocksAPIError as e:
            print(f"   重置失败: {e}")
        
        # 2. 更新端口标签
        print("2. 更新端口标签:")
        try:
            updated_port = client.update_port_tags(port_id, ["test", "demo"])
            print(f"   标签更新成功: {updated_port.tags}")
        except LTESocksAPIError as e:
            print(f"   标签更新失败: {e}")
        
        # 3. 设置自动重置间隔
        print("3. 设置自动重置间隔:")
        try:
            updated_port = client.update_port_autoreset(port_id, 3600)  # 1小时
            print(f"   自动重置间隔设置成功: {updated_port.autoResetInterval}秒")
        except LTESocksAPIError as e:
            print(f"   设置自动重置间隔失败: {e}")
        
        # 4. 更新端口签名
        print("4. 更新端口签名:")
        try:
            signatures = client.get_signatures()
            if signatures:
                updated_port = client.update_port_signature(port_id, signatures[0])
                print(f"   签名更新成功: {updated_port.signature}")
        except LTESocksAPIError as e:
            print(f"   签名更新失败: {e}")
        
    except Exception as e:
        print(f"错误: {e}")


def demo_batch_operations():
    """演示批量操作"""
    
    api_key = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
    client = LTESocksClient(api_key)
    
    try:
        print("=== 批量操作示例 ===\n")
        
        # 1. 重置所有端口
        print("1. 重置所有活跃端口:")
        reset_ports = client.reset_all_ports()
        print(f"   成功重置 {len(reset_ports)} 个端口")
        
        # 2. 按国家分组显示端口
        print("2. 按国家分组显示端口:")
        all_ports = client.get_ports().data
        countries = {}
        for port in all_ports:
            country = port.plan.countryCode
            if country not in countries:
                countries[country] = []
            countries[country].append(port)
        
        for country, ports in countries.items():
            print(f"   {country}: {len(ports)} 个端口")
        
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    # 运行基本示例
    main()
    
    print("\n" + "="*50 + "\n")
    
    # 运行端口管理示例
    demo_port_management()
    
    print("\n" + "="*50 + "\n")
    
    # 运行批量操作示例
    demo_batch_operations()
