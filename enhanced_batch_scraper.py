#!/usr/bin/env python3
"""
增强版批量电话号码爬取器
单浏览器 + 智能延迟 + 优化性能策略
避免多标签页的复杂性，专注于稳定性和效率
"""

import os
import sys
import time
import logging
import traceback
import re
import random
from datetime import datetime
from typing import List, Dict, Tuple
import csv
from pathlib import Path

# 导入现有的模块
from test_instant_load import (
    find_chrome_path, instant_page_load, find_background_report_instant,
    extract_person_info, extract_relatives_info, click_relative_link_and_extract
)
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
from CloudflareBypasser import CloudflareBypasser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_batch_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class EnhancedBatchScraper:
    """增强版批量爬取器 - 单浏览器智能延迟策略"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data"):
        """
        初始化增强版爬取器
        
        Args:
            phone_file: 电话号码文件路径
            output_dir: 输出目录
        """
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None,
            'current_phone': None
        }

        # 浏览器实例
        self.driver = None
        self.chrome_path = None

        # 已处理的电话号码记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()

        # 统一的CSV文件
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_initialized = False

        # 智能延迟控制
        self.last_request_time = 0
        self.min_delay = 2.0  # 最小延迟（秒）
        self.max_delay = 5.0  # 最大延迟（秒）
        self.adaptive_delay = 2.0  # 自适应延迟
        self.success_count = 0  # 连续成功次数
        self.fail_count = 0  # 连续失败次数

        logging.info(f"增强版批量爬取器初始化完成")
        logging.info(f"电话号码文件: {phone_file}")
        logging.info(f"输出目录: {output_dir}")
        logging.info(f"已处理电话号码: {len(self.processed_phones)}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")

    def init_unified_csv(self):
        """初始化统一的CSV文件"""
        if not self.csv_initialized:
            try:
                if not self.unified_csv_file.exists():
                    with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                    logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                else:
                    logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
                self.csv_initialized = True
            except Exception as e:
                logging.error(f"初始化CSV文件失败: {e}")

    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """立即保存数据到CSV文件"""
        try:
            self.init_unified_csv()
            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    data_type,
                    info.get('姓名', ''),
                    info.get('年龄', ''),
                    info.get('手机', ''),
                    info.get('地址', ''),
                    info.get('关系', data_type),
                    info.get('链接', '')
                ])
            logging.info(f"✅ 已保存{data_type}: {info.get('姓名', 'N/A')}")
            return True
        except Exception as e:
            logging.error(f"保存{data_type}信息失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """从文件中加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                            logging.debug(f"去掉前缀1: {line.strip()} -> {phone}")
                        
                        # 验证电话号码格式（10位数字）
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def calculate_adaptive_delay(self, success: bool) -> float:
        """计算自适应延迟时间"""
        current_time = time.time()
        
        # 更新成功/失败计数
        if success:
            self.success_count += 1
            self.fail_count = 0
            # 连续成功时逐渐减少延迟
            if self.success_count >= 3:
                self.adaptive_delay = max(self.min_delay, self.adaptive_delay * 0.9)
        else:
            self.fail_count += 1
            self.success_count = 0
            # 失败时增加延迟
            self.adaptive_delay = min(self.max_delay * 2, self.adaptive_delay * 1.5)
        
        # 确保最小间隔
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_delay:
            additional_delay = self.min_delay - time_since_last
        else:
            additional_delay = 0
        
        # 计算总延迟
        base_delay = random.uniform(self.adaptive_delay * 0.8, self.adaptive_delay * 1.2)
        total_delay = base_delay + additional_delay
        
        # 记录请求时间
        self.last_request_time = current_time + total_delay
        
        return total_delay

    def init_browser(self) -> bool:
        """初始化浏览器"""
        try:
            # 查找Chrome浏览器
            self.chrome_path = find_chrome_path()
            if not self.chrome_path:
                logging.error("无法找到Chrome浏览器")
                return False
            
            # 创建浏览器配置
            options = ChromiumOptions()
            options.set_paths(browser_path=self.chrome_path)
            
            # 优化参数配置
            optimized_args = [
                "--no-first-run",
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-web-security",
                "--disable-extensions",
                "--disable-images",  # 禁用图片
                "--disable-plugins",
                "--disable-background-timer-throttling",
                "--aggressive-cache-discard",
                "--disable-background-networking",
                "--disable-sync",
                "--disable-translate",
                "--disable-ipc-flooding-protection",
                "--disable-client-side-phishing-detection",
                "--disable-component-update",
                "--disable-default-apps",
                "--disable-domain-reliability",
                "--disable-features=TranslateUI,BlinkGenPropertyTrees,VizDisplayCompositor",
                "--disable-blink-features=AutomationControlled",
                "--disable-infobars",
                "--disable-notifications",
                "--disable-popup-blocking",
                "--disable-background-mode",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--no-default-browser-check",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-field-trial-config",
                "--disable-back-forward-cache"
            ]

            # 设置资源过滤偏好
            prefs = {
                "profile.default_content_setting_values": {
                    "images": 2,  # 禁用图片
                    "plugins": 2,  # 禁用插件
                    "popups": 2,  # 禁用弹窗
                    "geolocation": 2,  # 禁用地理位置
                    "notifications": 2,  # 禁用通知
                    "media_stream": 2,  # 禁用媒体流
                }
            }
            options.set_pref("prefs", prefs)
            
            for arg in optimized_args:
                options.set_argument(arg)
            
            # 随机User-Agent
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            options.set_argument(f"--user-agent={random.choice(user_agents)}")
            
            # 启动浏览器
            logging.info("正在启动浏览器...")
            self.driver = ChromiumPage(addr_or_opts=options)

            # 设置超时和加载模式
            try:
                self.driver.set.timeouts(page_load=3)
                self.driver.set.load_mode.none()  # 不等待任何资源加载完成
                logging.info("浏览器初始化成功")
            except Exception as e:
                logging.warning(f"设置浏览器参数失败: {e}")

            return True
            
        except Exception as e:
            logging.error(f"初始化浏览器失败: {e}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("浏览器已关闭")
            except Exception as e:
                logging.error(f"关闭浏览器失败: {e}")
            finally:
                self.driver = None

    def scrape_phone_data(self, phone: str) -> Tuple[bool, Dict]:
        """
        爬取单个电话号码的背景报告数据

        Args:
            phone: 10位电话号码

        Returns:
            (成功标志, 数据字典)
        """
        start_time = time.time()
        logging.info("=" * 80)
        logging.info(f"🔍 开始处理电话号码: {phone}")

        try:
            # 自适应延迟（基于上次结果）
            delay = self.calculate_adaptive_delay(True)  # 先假设会成功
            logging.info(f"⏰ 智能延迟: {delay:.2f}秒 (自适应延迟: {self.adaptive_delay:.2f})")
            time.sleep(delay)

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"目标URL: {target_url}")

            # 页面加载 - 极速加载
            if not instant_page_load(self.driver, target_url, max_wait=2):
                logging.error(f"页面加载失败: {phone}")
                self.calculate_adaptive_delay(False)  # 更新失败状态
                return False, {}

            # 智能Cloudflare处理
            try:
                page_source = self.driver.html
                page_length = len(page_source)

                cloudflare_keywords = ["cloudflare", "just a moment", "checking your browser", "please wait"]
                has_cloudflare = any(keyword in page_source.lower() for keyword in cloudflare_keywords)

                # 只有在真正检测到Cloudflare或页面内容极少时才处理
                needs_refresh = has_cloudflare or page_length < 8000

                if needs_refresh:
                    if has_cloudflare:
                        logging.info(f"检测到Cloudflare保护，页面长度: {page_length}字符，使用CloudflareBypasser...")
                        cf_bypasser = CloudflareBypasser(self.driver, max_retries=3, log=True)
                        cf_bypasser.bypass()

                        # 检查绕过结果
                        final_source = self.driver.html
                        final_length = len(final_source)
                        final_has_cloudflare = any(keyword in final_source.lower() for keyword in cloudflare_keywords)

                        if not final_has_cloudflare:
                            logging.info(f"✅ CloudflareBypasser成功，页面长度: {final_length}")
                        else:
                            logging.warning(f"⚠️ CloudflareBypasser未完全成功，但继续处理，页面长度: {final_length}")
                    else:
                        logging.info(f"页面内容过少({page_length}字符)，尝试简单刷新...")
                        self.driver.refresh()
                        time.sleep(1.5)
                        new_source = self.driver.html
                        logging.info(f"刷新后页面长度: {len(new_source)}")
                else:
                    logging.info(f"页面状态良好，内容长度: {page_length}字符，直接处理")

            except Exception as e:
                logging.warning(f"Cloudflare检查失败: {e}")
                # 继续处理，不中断流程

            # 查找背景报告按钮
            button_found, button_url = find_background_report_instant(self.driver)

            if not button_found:
                logging.warning(f"未找到背景报告按钮: {phone}")
                self.calculate_adaptive_delay(False)  # 更新失败状态
                return False, {}

            logging.info(f"找到背景报告链接: {button_url}")

            # 访问背景报告页面 - 极速加载
            if not instant_page_load(self.driver, button_url, max_wait=2):
                logging.error(f"背景报告页面加载失败: {phone}")
                self.calculate_adaptive_delay(False)  # 更新失败状态
                return False, {}

            # 处理背景报告页面的Cloudflare
            try:
                bg_page_source = self.driver.html
                cloudflare_keywords = ["cloudflare", "just a moment", "checking your browser", "please wait"]
                has_cloudflare = any(keyword in bg_page_source.lower() for keyword in cloudflare_keywords)

                if has_cloudflare:
                    logging.info("背景报告页面检测到Cloudflare，使用CloudflareBypasser...")
                    cf_bypasser = CloudflareBypasser(self.driver, max_retries=5, log=True)
                    cf_bypasser.bypass()

                    # 检查绕过结果
                    final_source = self.driver.html
                    final_has_cloudflare = any(keyword in final_source.lower() for keyword in cloudflare_keywords)

                    if not final_has_cloudflare:
                        logging.info("✅ 背景报告页面CloudflareBypasser成功")
                    else:
                        logging.warning("⚠️ 背景报告页面Cloudflare仍然存在，但继续处理...")
                else:
                    logging.info("背景报告页面无Cloudflare，直接处理")

            except Exception as e:
                logging.warning(f"背景报告页面Cloudflare处理失败: {e}")
                # 继续处理

            # 解析页面内容 - 优化等待时间
            logging.info("等待页面内容稳定...")
            time.sleep(0.5)  # 减少等待时间

            # 获取最新页面源码
            new_page_source = self.driver.html
            soup = BeautifulSoup(new_page_source, 'html.parser')

            # 提取个人信息
            person_info = extract_person_info(soup)
            if not person_info.get('姓名'):
                logging.warning(f"未提取到个人信息: {phone}")
                self.calculate_adaptive_delay(False)  # 更新失败状态
                return False, {}

            # 立即保存个人信息
            self.save_to_csv(phone, '本人', person_info)

            # 提取亲属信息
            relatives_info = extract_relatives_info(soup, min_age=40)
            logging.info(f"找到 {len(relatives_info)} 个符合条件的亲属")

            # 处理亲属信息并实时保存
            detailed_relatives_count = 0
            for i, relative in enumerate(relatives_info):
                relative_name = relative.get('姓名', '')
                if relative_name:
                    # 智能重试机制
                    max_retries = 2
                    success = False
                    for retry in range(max_retries):
                        try:
                            logging.info(f"处理亲属 {i+1}/{len(relatives_info)}: {relative_name} (尝试 {retry+1}/{max_retries})")

                            # 如果是重试，短暂等待
                            if retry > 0:
                                time.sleep(0.8)

                            relative_details = click_relative_link_and_extract(self.driver, relative_name, soup)

                            combined_info = {
                                '姓名': relative.get('姓名', ''),
                                '年龄': relative.get('年龄', ''),
                                '关系': relative.get('关系', '亲属'),
                                '链接': relative.get('链接', ''),
                                '手机': relative_details.get('手机', ''),
                                '地址': relative_details.get('地址', '')
                            }

                            # 立即保存每个亲属信息
                            if self.save_to_csv(phone, '亲属', combined_info):
                                detailed_relatives_count += 1

                            # 检查是否获取到有效数据
                            has_contact_info = combined_info.get('手机') or combined_info.get('地址')

                            if has_contact_info:
                                # 获取到联系信息，正常延迟
                                time.sleep(random.uniform(0.4, 0.8))
                            else:
                                # 没有联系信息，快速处理下一个
                                time.sleep(random.uniform(0.2, 0.4))

                            success = True
                            break  # 成功处理，跳出重试循环

                        except Exception as e:
                            error_msg = str(e).lower()
                            logging.error(f"处理亲属 {relative_name} 失败 (尝试 {retry+1}/{max_retries}): {e}")

                            # 智能错误判断：某些错误直接跳过，不重试
                            if any(keyword in error_msg for keyword in ['页面已被刷新', 'context lost', 'page refresh']):
                                logging.warning(f"检测到页面刷新错误，直接跳过亲属 {relative_name}")
                                break

                            if retry == max_retries - 1:
                                logging.warning(f"亲属 {relative_name} 处理失败，保存基本信息")
                                # 保存基本信息，即使没有详细联系方式
                                basic_info = {
                                    '姓名': relative.get('姓名', ''),
                                    '年龄': relative.get('年龄', ''),
                                    '关系': relative.get('关系', '亲属'),
                                    '链接': relative.get('链接', ''),
                                    '手机': '',
                                    '地址': ''
                                }
                                self.save_to_csv(phone, '亲属', basic_info)
                            else:
                                time.sleep(0.3)  # 减少重试前等待时间

            duration = time.time() - start_time
            logging.info(f"✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"成功保存亲属数量: {detailed_relatives_count}")

            # 更新成功状态
            self.calculate_adaptive_delay(True)

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"错误详情: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")

            # 更新失败状态
            self.calculate_adaptive_delay(False)

            return False, {}

    def run_enhanced_batch_scraping(self, max_phones: int = None,
                                  base_delay: float = 2.0) -> Dict:
        """
        运行增强版批量爬取

        Args:
            max_phones: 最大处理电话号码数量，None表示处理所有
            base_delay: 基础延迟时间（秒）

        Returns:
            统计结果字典
        """
        self.stats['start_time'] = datetime.now()
        self.min_delay = base_delay
        self.max_delay = base_delay * 2.5
        self.adaptive_delay = base_delay

        logging.info("🚀 开始增强版批量爬取任务")
        logging.info(f"基础延迟: {base_delay}秒")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 初始化浏览器
        if not self.init_browser():
            logging.error("浏览器初始化失败，无法继续")
            return self.stats

        try:
            # 处理每个电话号码
            for i, phone in enumerate(unprocessed_phones, 1):
                self.stats['current_phone'] = phone
                self.stats['processed_phones'] = i

                logging.info(f"\n📞 处理进度: {i}/{len(unprocessed_phones)} ({i/len(unprocessed_phones)*100:.1f}%)")

                try:
                    success, result_data = self.scrape_phone_data(phone)

                    if success:
                        self.stats['successful_scrapes'] += 1
                        self.save_processed_phone(phone)
                        logging.info(f"✅ 成功处理: {phone}")
                    else:
                        self.stats['failed_scrapes'] += 1
                        logging.error(f"❌ 处理失败: {phone}")

                except Exception as e:
                    self.stats['failed_scrapes'] += 1
                    logging.error(f"❌ 处理电话号码 {phone} 时发生异常: {e}")
                    logging.error(f"异常堆栈: {traceback.format_exc()}")

                # 打印当前统计
                self.print_stats()

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在优雅退出...")
        except Exception as e:
            logging.error(f"批量处理过程中发生严重错误: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
        finally:
            self.close_browser()

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 增强版批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        if self.stats['processed_phones'] > 0:
            success_rate = self.stats['successful_scrapes']/self.stats['processed_phones']*100
            avg_time = duration / self.stats['processed_phones']
            logging.info(f"成功率: {success_rate:.1f}%")
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
        logging.info(f"最终自适应延迟: {self.adaptive_delay:.2f}秒")
        logging.info("=" * 80)

        return self.stats

    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"自适应延迟: {self.adaptive_delay:.2f}s, 耗时: {elapsed:.0f}秒")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='增强版批量电话号码背景报告爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=float, default=2.0, help='基础延迟时间（秒）')

    args = parser.parse_args()

    try:
        # 创建增强版爬取器
        scraper = EnhancedBatchScraper(args.phone_file, args.output_dir)

        # 运行增强版批量爬取
        stats = scraper.run_enhanced_batch_scraping(args.max_phones, args.delay)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
