#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高效requests方式电话号码背景报告爬取器
基于batch_phone_scraper模式，使用requests替代selenium
包含完整的反爬机制规避
"""

import requests
import time
import random
import json
import csv
import os
import logging
import traceback
import sys
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re
from fake_useragent import UserAgent
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('requests_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class RequestsPhoneScraper:
    """基于requests的高效电话号码爬取器"""
    
    def __init__(self, phone_file, output_dir):
        self.phone_file = phone_file
        self.output_dir = output_dir
        self.csv_file = os.path.join(output_dir, 'all_phone_data.csv')
        self.processed_phones_file = os.path.join(output_dir, 'processed_phones.txt')
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化会话和反爬机制
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        
        # 加载已处理的电话号码
        self.processed_phones = self.load_processed_phones()
        
        # 统计信息
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None
        }
        
        logging.info(f"加载了 {len(self.processed_phones)} 个已处理的电话号码")
        logging.info("高效requests爬取器初始化完成")
    
    def setup_session(self):
        """设置会话和反爬机制"""
        # 更真实的浏览器头部
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })

        # 设置超时和重试
        self.session.timeout = 30

        # 禁用SSL验证警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 设置代理轮换（如果需要）
        self.proxy_list = []  # 可以添加代理列表
    
    def rotate_user_agent(self):
        """轮换User-Agent"""
        self.session.headers['User-Agent'] = self.ua.random
    
    def random_delay(self, min_delay=0.5, max_delay=2.0):
        """随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def make_request(self, url, max_retries=2, timeout=10):
        """发送快速请求，优化性能"""
        for attempt in range(max_retries):
            try:
                # 快速请求，减少超时时间
                response = self.session.get(url, verify=False, allow_redirects=True, timeout=timeout)

                # 快速检查响应
                if response.status_code == 200:
                    # 简单检查是否为有效页面
                    if len(response.text) > 1000 and 'smartbackgroundchecks' in response.text.lower():
                        logging.info(f"✅ 快速访问成功: {url[:50]}...")
                        return response
                    else:
                        logging.warning(f"页面内容异常，重试 {attempt + 1}/{max_retries}")
                elif response.status_code == 403:
                    logging.warning(f"403错误，跳过此号码")
                    return None
                else:
                    logging.warning(f"HTTP {response.status_code}, 重试 {attempt + 1}/{max_retries}")

                # 快速重试
                if attempt < max_retries - 1:
                    self.random_delay(0.5, 1.0)
                    self.rotate_user_agent()

            except Exception as e:
                logging.error(f"请求异常 {attempt + 1}/{max_retries}: {str(e)[:100]}")
                if attempt < max_retries - 1:
                    self.random_delay(0.5, 1.0)

        return None
    
    def is_cloudflare_page(self, html):
        """检测是否为Cloudflare页面"""
        cloudflare_keywords = [
            'cloudflare', 'just a moment', 'checking your browser',
            'ray id', 'cf-ray', 'please wait'
        ]
        html_lower = html.lower()
        return any(keyword in html_lower for keyword in cloudflare_keywords)
    
    def load_processed_phones(self):
        """加载已处理的电话号码"""
        if os.path.exists(self.processed_phones_file):
            with open(self.processed_phones_file, 'r', encoding='utf-8') as f:
                return set(line.strip() for line in f if line.strip())
        return set()
    
    def save_processed_phone(self, phone):
        """保存已处理的电话号码"""
        with open(self.processed_phones_file, 'a', encoding='utf-8') as f:
            f.write(f"{phone}\n")
        self.processed_phones.add(phone)
    
    def load_phone_numbers(self, max_phones=None):
        """加载电话号码"""
        phones = []
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line in f:
                    phone = line.strip()
                    if phone and len(phone) >= 10:
                        # 移除前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                        phones.append(phone)
                        
                        if max_phones and len(phones) >= max_phones:
                            break
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            return phones
            
        except Exception as e:
            logging.error(f"加载电话号码失败: {e}")
            return []
    
    def extract_person_info(self, soup):
        """提取个人信息"""
        person_info = {'姓名': '', '年龄': '', '手机': '', '地址': ''}
        
        try:
            # 提取姓名
            name_selectors = [
                'h1.person-name', 'h1[data-testid="person-name"]',
                '.person-header h1', '.profile-name', 'h1.name'
            ]
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem:
                    person_info['姓名'] = name_elem.get_text(strip=True)
                    break
            
            # 提取年龄
            age_pattern = r'(\d{1,3})\s*(?:years?\s*old|岁|age)'
            age_match = re.search(age_pattern, soup.get_text(), re.IGNORECASE)
            if age_match:
                person_info['年龄'] = age_match.group(1)
            
            # 提取手机号码
            phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
            phone_matches = re.findall(phone_pattern, soup.get_text())
            if phone_matches:
                person_info['手机'] = phone_matches[0]
            
            # 提取地址
            address_selectors = [
                '.address', '.location', '.city-state',
                '[data-testid="address"]', '.person-address'
            ]
            for selector in address_selectors:
                addr_elem = soup.select_one(selector)
                if addr_elem:
                    person_info['地址'] = addr_elem.get_text(strip=True)
                    break
            
        except Exception as e:
            logging.error(f"提取个人信息失败: {e}")
        
        return person_info
    
    def find_background_report_link(self, soup, base_url):
        """查找背景报告链接"""
        try:
            # 查找包含背景报告的链接
            link_patterns = [
                'a[href*="/people/"]',
                'a[href*="background"]',
                'a.btn-primary',
                'a.report-link'
            ]
            
            for pattern in link_patterns:
                links = soup.select(pattern)
                for link in links:
                    href = link.get('href')
                    if href and '/people/' in href:
                        full_url = urljoin(base_url, href)
                        logging.info(f"找到背景报告链接: {full_url}")
                        return full_url
            
            return None
            
        except Exception as e:
            logging.error(f"查找背景报告链接失败: {e}")
            return None
    
    def extract_relatives_info(self, soup):
        """提取亲属信息"""
        relatives = []
        
        try:
            # 查找亲属信息区域
            relatives_sections = soup.select('.relatives, .family, .associates, .related-people')
            
            for section in relatives_sections:
                # 查找亲属链接和信息
                relative_links = section.select('a[href*="/people/"]')
                
                for link in relative_links:
                    try:
                        name = link.get_text(strip=True)
                        href = link.get('href')
                        
                        # 提取年龄
                        age_text = link.parent.get_text() if link.parent else ""
                        age_match = re.search(r'(\d{1,3})', age_text)
                        age = int(age_match.group(1)) if age_match else 0
                        
                        if name and age >= 40:  # 只处理40岁以上的亲属
                            relatives.append({
                                '姓名': name,
                                '年龄': str(age),
                                '链接': href
                            })
                            
                    except Exception as e:
                        logging.error(f"处理亲属信息失败: {e}")
                        continue
            
            logging.info(f"找到 {len(relatives)} 个符合条件的亲属")
            return relatives
            
        except Exception as e:
            logging.error(f"提取亲属信息失败: {e}")
            return []
    
    def scrape_phone_data(self, phone):
        """爬取单个电话号码的数据"""
        try:
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"🔍 开始处理电话号码: {phone}")
            logging.info(f"目标URL: {target_url}")
            
            # 随机延迟
            self.random_delay(0.5, 1.5)
            
            # 获取主页面
            response = self.make_request(target_url)
            if not response:
                logging.error(f"无法访问页面: {phone}")
                return False, {}
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找背景报告链接
            report_link = self.find_background_report_link(soup, target_url)
            if not report_link:
                logging.error(f"未找到背景报告链接: {phone}")
                return False, {}
            
            # 随机延迟
            self.random_delay(0.5, 1.0)
            
            # 访问背景报告页面
            report_response = self.make_request(report_link)
            if not report_response:
                logging.error(f"无法访问背景报告页面: {phone}")
                return False, {}
            
            report_soup = BeautifulSoup(report_response.text, 'html.parser')
            
            # 提取个人信息
            person_info = self.extract_person_info(report_soup)
            if not person_info.get('姓名'):
                logging.error(f"未提取到个人信息: {phone}")
                return False, {}
            
            # 保存个人信息
            self.save_person_data(person_info, '本人', phone)
            logging.info(f"✅ 已保存本人信息: {person_info['姓名']}")
            
            # 提取亲属信息
            relatives = self.extract_relatives_info(report_soup)
            
            # 处理亲属信息
            for i, relative in enumerate(relatives[:20], 1):  # 限制处理前20个亲属
                try:
                    logging.info(f"处理亲属 {i}/{len(relatives)}: {relative['姓名']}")
                    
                    # 随机延迟
                    self.random_delay(0.3, 0.8)
                    
                    # 访问亲属页面
                    relative_url = urljoin(report_link, relative['链接'])
                    relative_response = self.make_request(relative_url)
                    
                    if relative_response:
                        relative_soup = BeautifulSoup(relative_response.text, 'html.parser')
                        relative_info = self.extract_person_info(relative_soup)
                        
                        if relative_info.get('姓名'):
                            self.save_person_data(relative_info, '亲属', phone)
                            logging.info(f"✅ 已保存亲属: {relative_info['姓名']}, {relative_info['年龄']}岁")
                        
                except Exception as e:
                    logging.error(f"处理亲属失败: {e}")
                    continue
            
            return True, person_info
            
        except Exception as e:
            logging.error(f"爬取电话号码 {phone} 失败: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return False, {}
    
    def save_person_data(self, person_info, relation_type, phone):
        """保存个人数据到CSV"""
        try:
            # 检查CSV文件是否存在，不存在则创建
            file_exists = os.path.exists(self.csv_file)
            
            with open(self.csv_file, 'a', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['查询电话', '关系', '姓名', '年龄', '手机', '地址', '爬取时间']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # 如果文件不存在，写入表头
                if not file_exists:
                    writer.writeheader()
                
                # 写入数据
                writer.writerow({
                    '查询电话': phone,
                    '关系': relation_type,
                    '姓名': person_info.get('姓名', ''),
                    '年龄': person_info.get('年龄', ''),
                    '手机': person_info.get('手机', ''),
                    '地址': person_info.get('地址', ''),
                    '爬取时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                
        except Exception as e:
            logging.error(f"保存数据失败: {e}")

    def run_batch_scraping(self, max_phones=None, delay_between_phones=1, max_workers=3):
        """运行批量爬取任务"""
        try:
            self.stats['start_time'] = datetime.now()
            logging.info("🚀 开始高效批量爬取任务")

            # 加载电话号码
            all_phones = self.load_phone_numbers(max_phones)
            if not all_phones:
                logging.error("没有找到有效的电话号码")
                return self.stats

            # 过滤未处理的电话号码
            unprocessed_phones = [phone for phone in all_phones if phone not in self.processed_phones]

            self.stats['total_phones'] = len(all_phones)
            logging.info(f"总电话号码: {len(all_phones)}, 未处理: {len(unprocessed_phones)}")

            if not unprocessed_phones:
                logging.info("所有电话号码都已处理完成")
                return self.stats

            # 使用线程池进行并发处理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交任务
                future_to_phone = {}
                for i, phone in enumerate(unprocessed_phones):
                    # 控制提交速度，避免过快
                    if i > 0:
                        time.sleep(delay_between_phones)

                    future = executor.submit(self.process_single_phone, phone, i + 1, len(unprocessed_phones))
                    future_to_phone[future] = phone

                # 处理完成的任务
                for future in as_completed(future_to_phone):
                    phone = future_to_phone[future]
                    try:
                        success = future.result()
                        if success:
                            self.stats['successful_scrapes'] += 1
                        else:
                            self.stats['failed_scrapes'] += 1

                        self.stats['processed_phones'] += 1
                        self.save_processed_phone(phone)

                    except Exception as e:
                        self.stats['failed_scrapes'] += 1
                        logging.error(f"处理电话号码 {phone} 时发生异常: {e}")

                    # 打印进度
                    self.print_stats()

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在优雅退出...")
        except Exception as e:
            logging.error(f"批量处理过程中发生严重错误: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 高效批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        if self.stats['processed_phones'] > 0:
            logging.info(f"成功率: {self.stats['successful_scrapes']/self.stats['processed_phones']*100:.1f}%")
        logging.info("=" * 80)

        return self.stats

    def process_single_phone(self, phone, index, total):
        """处理单个电话号码（线程安全）"""
        try:
            logging.info(f"\n📞 处理进度: {index}/{total} ({index/total*100:.1f}%)")
            logging.info("=" * 80)

            success, result_data = self.scrape_phone_data(phone)
            return success

        except Exception as e:
            logging.error(f"处理电话号码 {phone} 失败: {e}")
            return False

    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"耗时: {elapsed:.0f}秒")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='高效requests方式电话号码背景报告爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data_requests', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=int, default=1, help='电话号码之间的延迟（秒）')
    parser.add_argument('--workers', type=int, default=3, help='并发线程数')

    args = parser.parse_args()

    try:
        # 创建爬取器
        scraper = RequestsPhoneScraper(args.phone_file, args.output_dir)

        # 运行批量爬取
        stats = scraper.run_batch_scraping(args.max_phones, args.delay, args.workers)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()
