# LTESocks API客户端和服务器

这是一个完整的LTESocks API客户端实现，包含所有代理管理功能和RESTful API服务器。

## 文件说明

### 核心文件

1. **ltesocks_client.py** - LTESocks API客户端
   - 完整的API接口封装
   - 包含所有代理管理功能
   - 支持端口重置、获取代理信息等

2. **ltesocks_server.py** - FastAPI服务器
   - 提供RESTful API接口
   - 基于ltesocks_client构建
   - 包含Swagger文档

3. **ltesocks_example.py** - 使用示例
   - 演示如何使用客户端
   - 包含各种操作示例

## 安装依赖

```bash
pip install requests pydantic fastapi uvicorn
```

## 快速开始

### 1. 使用客户端

```python
from ltesocks_client import LTESocksClient

# 初始化客户端
api_key = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
client = LTESocksClient(api_key)

# 获取用户信息
user = client.get_user()
print(f"用户: {user.login}, 余额: ${user.balance/100:.2f}")

# 获取所有端口
ports = client.get_ports()
print(f"总端口数: {ports.total}")

# 重置端口
if ports.data:
    port_id = ports.data[0].port
    reset_port = client.reset_port(port_id)
    print(f"端口 {port_id} 重置成功")
```

### 2. 启动API服务器

```bash
python ltesocks_server.py
```

服务器将在 http://localhost:8001 启动

访问 http://localhost:8001/docs 查看Swagger API文档

### 3. 运行示例

```bash
python ltesocks_example.py
```

## API接口说明

### 用户接口
- `GET /api/user` - 获取用户信息

### 代理端口接口
- `GET /api/ports` - 获取所有端口
- `POST /api/ports/filter` - 获取端口（带过滤）
- `GET /api/ports/{port_id}` - 获取端口详情
- `POST /api/ports/{port_id}/reset` - 重置端口
- `POST /api/ports/reset-batch` - 批量重置端口
- `GET /api/ports/{port_id}/log` - 获取端口日志
- `GET /api/ports/{port_id}/proxy-info` - 获取代理信息
- `GET /api/ports/{port_id}/vpn` - 下载VPN配置
- `POST /api/ports/{port_id}/tags` - 更新端口标签
- `POST /api/ports/{port_id}/autoreset` - 设置自动重置
- `DELETE /api/ports/{port_id}` - 删除端口

### 便捷接口
- `GET /api/ports/active` - 获取活跃端口
- `GET /api/ports/country/{country_code}` - 按国家获取端口

### 其他接口
- `GET /api/plans` - 获取计划列表
- `GET /api/servers` - 获取服务器列表
- `GET /api/signatures` - 获取可用签名
- `GET /api/payments` - 获取支付历史
- `GET /api/stats/overview` - 获取概览统计
- `GET /api/health` - 健康检查

## 主要功能

### 1. 代理管理
- 获取所有代理端口
- 按国家、计划、标签过滤端口
- 重置单个或批量端口
- 获取端口详细信息和状态

### 2. 端口操作
- 重置端口IP
- 更新端口标签
- 设置自动重置间隔
- 更新设备签名
- 管理端口凭据

### 3. 监控和统计
- 获取账户概览
- 按国家统计端口分布
- 端口状态统计
- 支付历史查询

### 4. VPN支持
- 下载VPN配置文件
- 支持自定义凭据

## 使用示例

### 重置所有端口
```python
# 重置所有活跃端口
reset_ports = client.reset_all_ports()
print(f"成功重置 {len(reset_ports)} 个端口")
```

### 获取特定国家的端口
```python
# 获取美国的端口
us_ports = client.get_ports_by_country("US")
for port in us_ports:
    print(f"美国端口: {port.port} - {port.ip}")
```

### 获取代理连接信息
```python
# 获取端口的代理信息
proxy_info = client.get_port_proxy_info("10000")
print(f"代理地址: {proxy_info['host']}:{proxy_info['port']}")
print(f"支持协议: {proxy_info['protocols']}")
```

### 批量重置端口（API）
```bash
curl -X POST "http://localhost:8001/api/ports/reset-batch" \
     -H "Content-Type: application/json" \
     -d '{"port_ids": ["10000", "10001"]}'
```

### 获取统计信息（API）
```bash
curl "http://localhost:8001/api/stats/overview"
```

## 错误处理

所有API调用都包含适当的错误处理：

```python
try:
    port = client.get_port("invalid_id")
except LTESocksAPIError as e:
    print(f"API错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 配置

### 环境变量
- `PORT` - API服务器端口（默认：8001）

### API密钥
当前使用的API密钥：`36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd`

如需更换密钥，请修改以下文件中的API_KEY变量：
- `ltesocks_server.py`
- `ltesocks_example.py`

## 注意事项

1. 确保API密钥有效且有足够权限
2. 重置端口操作不可逆，请谨慎使用
3. 批量操作可能需要较长时间
4. VPN配置下载需要端口支持VPN功能
5. 某些操作可能有频率限制

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 确认LTESocks服务状态

2. **端口重置失败**
   - 检查端口是否存在
   - 确认端口状态是否允许重置
   - 验证账户权限

3. **VPN配置下载失败**
   - 确认端口支持VPN功能
   - 检查凭据是否正确
   - 验证端口状态

### 健康检查
访问 `/api/health` 端点检查API服务状态：

```bash
curl "http://localhost:8001/api/health"
```

## 许可证

本项目基于原CloudflareBypassForScraping项目，请遵循相应的许可证条款。
