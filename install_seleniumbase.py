#!/usr/bin/env python3
"""
SeleniumBase安装脚本
自动安装SeleniumBase及其依赖
"""

import subprocess
import sys
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def run_command(command, description):
    """运行命令并处理错误"""
    try:
        logging.info(f"正在{description}...")
        logging.info(f"执行命令: {command}")
        
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if result.stdout:
            logging.info(f"输出: {result.stdout}")
        
        logging.info(f"✅ {description}成功")
        return True
        
    except subprocess.CalledProcessError as e:
        logging.error(f"❌ {description}失败")
        logging.error(f"错误代码: {e.returncode}")
        if e.stdout:
            logging.error(f"标准输出: {e.stdout}")
        if e.stderr:
            logging.error(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        logging.error(f"❌ {description}时发生异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logging.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        logging.error("❌ SeleniumBase需要Python 3.7或更高版本")
        return False
    
    logging.info("✅ Python版本符合要求")
    return True

def install_seleniumbase():
    """安装SeleniumBase"""
    commands = [
        # 升级pip
        ("python -m pip install --upgrade pip", "升级pip"),
        
        # 安装SeleniumBase
        ("pip install seleniumbase", "安装SeleniumBase"),
        
        # 安装额外依赖
        ("pip install beautifulsoup4", "安装BeautifulSoup4"),
        ("pip install lxml", "安装lxml"),
        ("pip install requests", "安装requests"),
        ("pip install pyautogui", "安装PyAutoGUI"),
        
        # 安装SeleniumBase驱动
        ("seleniumbase install chromedriver", "安装ChromeDriver"),
        ("seleniumbase install geckodriver", "安装GeckoDriver"),
    ]
    
    success_count = 0
    for command, description in commands:
        if run_command(command, description):
            success_count += 1
        else:
            logging.warning(f"⚠️ {description}失败，但继续安装其他组件")
    
    logging.info(f"安装完成: {success_count}/{len(commands)} 个组件安装成功")
    return success_count > len(commands) // 2  # 超过一半成功就认为安装成功

def verify_installation():
    """验证安装"""
    try:
        logging.info("正在验证SeleniumBase安装...")
        
        # 测试导入
        import seleniumbase
        logging.info(f"✅ SeleniumBase版本: {seleniumbase.__version__}")
        
        # 测试基本功能
        test_code = '''
from seleniumbase import SB
print("SeleniumBase导入成功")
'''
        
        result = subprocess.run(
            [sys.executable, "-c", test_code],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            logging.info("✅ SeleniumBase基本功能测试通过")
            return True
        else:
            logging.error(f"❌ SeleniumBase测试失败: {result.stderr}")
            return False
            
    except ImportError as e:
        logging.error(f"❌ SeleniumBase导入失败: {e}")
        return False
    except Exception as e:
        logging.error(f"❌ 验证过程中发生异常: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始安装SeleniumBase")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装SeleniumBase
    if not install_seleniumbase():
        logging.error("❌ SeleniumBase安装失败")
        sys.exit(1)
    
    # 验证安装
    if not verify_installation():
        logging.error("❌ SeleniumBase验证失败")
        sys.exit(1)
    
    logging.info("🎉 SeleniumBase安装完成！")
    logging.info("")
    logging.info("现在你可以运行以下命令来测试批量爬取器:")
    logging.info("python seleniumbase_batch_scraper.py --max-phones 5 --delay 3.0")
    logging.info("")
    logging.info("更多选项:")
    logging.info("python seleniumbase_batch_scraper.py --help")

if __name__ == "__main__":
    main()
