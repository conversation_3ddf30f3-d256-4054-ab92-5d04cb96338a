#!/usr/bin/env python3
"""
简化版测试脚本 - 用于验证基本功能
"""

import time
import logging
import os
from DrissionPage import ChromiumPage, ChromiumOptions

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def find_chrome_path():
    """查找Chrome浏览器路径"""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"找到Chrome浏览器: {path}")
            return path
    
    logging.warning("未找到Chrome浏览器")
    return None

def test_basic_functionality():
    """测试基本功能"""
    
    # 查找浏览器
    chrome_path = find_chrome_path()
    if not chrome_path:
        logging.error("无法找到Chrome浏览器，请安装Chrome或设置CHROME_PATH环境变量")
        return False
    
    # 创建简单的浏览器选项
    try:
        options = ChromiumOptions()
        options.set_paths(browser_path=chrome_path)
        
        # 基本参数
        basic_args = [
            "--no-first-run",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-sandbox"
        ]
        
        for arg in basic_args:
            options.set_argument(arg)
            
        logging.info("浏览器选项配置完成")
        
    except Exception as e:
        logging.error(f"配置浏览器选项失败: {e}")
        return False
    
    # 启动浏览器
    try:
        logging.info("正在启动浏览器...")
        driver = ChromiumPage(addr_or_opts=options)
        logging.info("浏览器启动成功")
        
        # 测试访问简单页面
        test_url = "https://httpbin.org/html"
        logging.info(f"正在访问测试页面: {test_url}")
        
        driver.get(test_url)
        logging.info("页面加载成功")
        
        # 等待一下让页面加载
        time.sleep(2)
        
        # 获取页面信息
        title = driver.title
        page_source = driver.html
        
        logging.info(f"页面标题: {title}")
        logging.info(f"页面源码长度: {len(page_source)} 字符")
        
        # 保存页面源码
        with open('test_page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        logging.info("页面源码已保存到 test_page_source.html")
        
        # 关闭浏览器
        driver.quit()
        logging.info("浏览器已关闭")
        
        return True
        
    except Exception as e:
        logging.error(f"测试过程中出错: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def test_target_website():
    """测试目标网站"""
    
    chrome_path = find_chrome_path()
    if not chrome_path:
        return False
    
    try:
        options = ChromiumOptions()
        options.set_paths(browser_path=chrome_path)
        
        # 优化参数
        args = [
            "--no-first-run",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-images",  # 禁用图片加载
            "--disable-extensions"
        ]
        
        for arg in args:
            options.set_argument(arg)
        
        logging.info("正在启动浏览器测试目标网站...")
        driver = ChromiumPage(addr_or_opts=options)
        
        # 访问目标网站
        target_url = "https://www.smartbackgroundchecks.com/phone/5619324217"
        logging.info(f"正在访问目标网站: {target_url}")
        
        start_time = time.time()
        driver.get(target_url)
        
        # 快速检查页面
        time.sleep(1)  # 等待1秒
        
        title = driver.title
        page_source = driver.html
        elapsed_time = time.time() - start_time
        
        logging.info(f"页面加载完成，耗时: {elapsed_time:.2f}秒")
        logging.info(f"页面标题: {title}")
        logging.info(f"页面源码长度: {len(page_source)} 字符")
        
        # 检查是否有Cloudflare
        source_lower = page_source.lower()
        has_cloudflare = any(keyword in source_lower for keyword in [
            "cloudflare", "just a moment", "checking your browser"
        ])
        
        if has_cloudflare:
            logging.info("检测到Cloudflare保护")
        else:
            logging.info("未检测到Cloudflare保护")
        
        # 保存结果
        with open('target_page_source.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        logging.info("目标页面源码已保存到 target_page_source.html")
        
        driver.quit()
        return True
        
    except Exception as e:
        logging.error(f"测试目标网站时出错: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def main():
    """主函数"""
    logging.info("开始简化版测试")
    logging.info("=" * 50)
    
    # 测试1: 基本功能
    logging.info("测试1: 基本功能测试")
    if test_basic_functionality():
        logging.info("✓ 基本功能测试通过")
    else:
        logging.error("✗ 基本功能测试失败")
        return
    
    time.sleep(2)
    
    # 测试2: 目标网站
    logging.info("\n测试2: 目标网站测试")
    if test_target_website():
        logging.info("✓ 目标网站测试通过")
    else:
        logging.error("✗ 目标网站测试失败")
    
    logging.info("=" * 50)
    logging.info("测试完成")

if __name__ == '__main__':
    main()
