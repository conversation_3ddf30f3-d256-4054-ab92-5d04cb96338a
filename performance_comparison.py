#!/usr/bin/env python3
"""
性能对比分析脚本
分析不同版本爬取器的性能表现
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path

def parse_log_file(log_file_path):
    """解析日志文件，提取性能数据"""
    if not os.path.exists(log_file_path):
        return None
    
    stats = {
        'total_phones': 0,
        'successful_scrapes': 0,
        'failed_scrapes': 0,
        'cloudflare_blocks': 0,
        'button_not_found': 0,
        'relatives_extracted': 0,
        'total_duration': 0,
        'avg_processing_time': 0,
        'success_rate': 0,
        'errors': []
    }
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取统计信息
        patterns = {
            'total_phones': r'处理电话号码:\s*(\d+)',
            'successful_scrapes': r'成功:\s*(\d+)',
            'failed_scrapes': r'失败:\s*(\d+)',
            'cloudflare_blocks': r'Cloudflare阻止:\s*(\d+)',
            'button_not_found': r'按钮未找到:\s*(\d+)',
            'relatives_extracted': r'提取亲属数量:\s*(\d+)',
            'total_duration': r'总耗时:\s*([\d.]+)秒',
            'avg_processing_time': r'平均处理时间:\s*([\d.]+)秒/个',
            'success_rate': r'成功率:\s*([\d.]+)%'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                try:
                    stats[key] = float(match.group(1))
                except ValueError:
                    stats[key] = 0
        
        # 提取错误信息
        error_pattern = r'ERROR.*?- (.*?)(?=\n|$)'
        errors = re.findall(error_pattern, content)
        stats['errors'] = errors[:10]  # 只保留前10个错误
        
        return stats
        
    except Exception as e:
        print(f"解析日志文件失败 {log_file_path}: {e}")
        return None

def analyze_performance():
    """分析性能数据"""
    
    # 日志文件列表
    log_files = {
        'SeleniumBase原版': 'seleniumbase_batch_scraper.log',
        'SeleniumBase简化版': 'seleniumbase_batch_scraper_simple.log',
        'SeleniumBase优化版': 'seleniumbase_optimized_scraper.log',
        'Enhanced批量版': 'enhanced_batch_scraper.log',
        'DrissionPage版': 'batch_scraper.log'
    }
    
    print("=" * 80)
    print("📊 爬取器性能对比分析")
    print("=" * 80)
    
    results = {}
    
    for name, log_file in log_files.items():
        print(f"\n🔍 分析 {name}...")
        stats = parse_log_file(log_file)
        
        if stats:
            results[name] = stats
            print(f"  ✅ 成功解析日志文件")
            print(f"  📞 处理号码: {stats['total_phones']}")
            print(f"  ✅ 成功: {stats['successful_scrapes']}")
            print(f"  ❌ 失败: {stats['failed_scrapes']}")
            print(f"  📈 成功率: {stats['success_rate']:.1f}%")
            print(f"  ⏱️ 平均时间: {stats['avg_processing_time']:.2f}秒/个")
        else:
            print(f"  ❌ 无法解析日志文件或文件不存在")
    
    if not results:
        print("\n⚠️ 没有找到可分析的日志文件")
        return
    
    # 生成对比报告
    print("\n" + "=" * 80)
    print("📈 性能对比报告")
    print("=" * 80)
    
    # 按成功率排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['success_rate'], reverse=True)
    
    print(f"{'版本':<20} {'成功率':<10} {'平均时间':<12} {'Cloudflare阻止':<15} {'亲属提取':<10}")
    print("-" * 80)
    
    for name, stats in sorted_results:
        print(f"{name:<20} {stats['success_rate']:<9.1f}% {stats['avg_processing_time']:<11.2f}s "
              f"{stats['cloudflare_blocks']:<14} {stats['relatives_extracted']:<10}")
    
    # 找出最佳性能
    best_success_rate = max(results.items(), key=lambda x: x[1]['success_rate'])
    best_speed = min(results.items(), key=lambda x: x[1]['avg_processing_time'] if x[1]['avg_processing_time'] > 0 else float('inf'))
    
    print("\n🏆 性能冠军:")
    print(f"  🎯 最高成功率: {best_success_rate[0]} ({best_success_rate[1]['success_rate']:.1f}%)")
    if best_speed[1]['avg_processing_time'] > 0:
        print(f"  ⚡ 最快速度: {best_speed[0]} ({best_speed[1]['avg_processing_time']:.2f}秒/个)")
    
    # 问题分析
    print("\n🔍 问题分析:")
    for name, stats in results.items():
        if stats['cloudflare_blocks'] > 0:
            print(f"  🚫 {name}: Cloudflare阻止 {stats['cloudflare_blocks']} 次")
        if stats['button_not_found'] > 0:
            print(f"  🔍 {name}: 按钮未找到 {stats['button_not_found']} 次")
        if stats['errors']:
            print(f"  ❌ {name}: 发现 {len(stats['errors'])} 个错误")
    
    # 保存分析结果
    output_file = "performance_analysis.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'analysis_time': datetime.now().isoformat(),
                'results': results,
                'best_success_rate': best_success_rate[0],
                'best_speed': best_speed[0] if best_speed[1]['avg_processing_time'] > 0 else None
            }, f, indent=2, ensure_ascii=False)
        print(f"\n💾 分析结果已保存到: {output_file}")
    except Exception as e:
        print(f"\n❌ 保存分析结果失败: {e}")
    
    print("\n" + "=" * 80)

def generate_recommendations():
    """生成优化建议"""
    print("💡 优化建议:")
    print("1. 🛡️ Cloudflare绕过: 使用SeleniumBase UC Mode + 更长延迟")
    print("2. 🎯 按钮查找: 改进CSS选择器和文本匹配逻辑")
    print("3. ⚡ 性能优化: 限制亲属处理数量，设置超时时间")
    print("4. 🔄 错误处理: 增加重试机制和异常恢复")
    print("5. 📊 数据质量: 去重处理和数据验证")

def main():
    """主函数"""
    try:
        analyze_performance()
        generate_recommendations()
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")

if __name__ == "__main__":
    main()
