#!/usr/bin/env python3
"""
LTESocks端口重置定时任务启动脚本
简化版启动器，支持命令行参数
"""

import argparse
import sys
import time
import logging
from ltesocks_port_reset_scheduler import LTESocksPortResetScheduler

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LTESocks端口重置定时任务')
    parser.add_argument('--api-key', 
                       default="36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd",
                       help='LTESocks API密钥')
    parser.add_argument('--interval', 
                       type=int, 
                       default=180,
                       help='重置间隔（秒），默认180秒（3分钟）')
    parser.add_argument('--once', 
                       action='store_true',
                       help='只执行一次重置，不启动定时任务')
    parser.add_argument('--status-interval',
                       type=int,
                       default=30,
                       help='状态打印间隔（秒），默认30秒')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 创建调度器
        scheduler = LTESocksPortResetScheduler(args.api_key, args.interval)
        
        if args.once:
            # 只执行一次
            logging.info("🎯 执行一次性端口重置...")
            result = scheduler.reset_all_ports()
            if result['success']:
                logging.info("✅ 一次性重置完成")
                sys.exit(0)
            else:
                logging.error("❌ 一次性重置失败")
                sys.exit(1)
        else:
            # 启动定时任务
            scheduler.start()
            
            logging.info(f"🚀 定时任务已启动，每 {args.interval} 秒重置一次端口")
            logging.info("按 Ctrl+C 停止任务")
            
            # 主循环
            try:
                while True:
                    time.sleep(args.status_interval)
                    scheduler.print_status()
            except KeyboardInterrupt:
                logging.info("收到停止信号...")
            finally:
                scheduler.stop()
                logging.info("✅ 程序已退出")
    
    except Exception as e:
        logging.error(f"❌ 程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
