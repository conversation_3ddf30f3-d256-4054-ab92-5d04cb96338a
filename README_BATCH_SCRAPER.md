# 批量电话号码背景报告爬取器

## 📋 功能说明

这个批量爬取器可以从KK1000.txt文件中读取电话号码，自动去掉前缀1，然后批量爬取每个号码的背景报告信息。

### 🎯 主要功能

- 📞 **批量处理**: 从文件中读取1000+个电话号码进行批量处理
- 🔄 **自动去前缀**: 自动去掉11位号码的前缀1，转换为10位号码
- 🛡️ **异常处理**: 完善的异常处理机制，确保程序持续运行
- 💾 **断点续传**: 记录已处理的号码，支持从中断处继续
- 📊 **详细统计**: 实时显示处理进度和成功率
- 📝 **日志记录**: 详细记录所有操作和错误信息
- 🎯 **数据提取**: 提取个人信息和年龄≥40岁的亲属详细信息

## 🚀 快速开始

### 1. 使用批处理文件启动（Windows）

```bash
# 双击运行或在命令行执行
start_batch_scraper.bat
```

### 2. 使用Shell脚本启动（Linux/Mac）

```bash
# 添加执行权限
chmod +x start_batch_scraper.sh

# 运行脚本
./start_batch_scraper.sh
```

### 3. 使用Python直接启动

```bash
# 测试模式（只处理前3个号码）
python start_batch_scraper.py --test

# 正常模式（处理所有号码）
python start_batch_scraper.py

# 自定义数量
python start_batch_scraper.py --max-phones 50

# 从中断处继续
python start_batch_scraper.py --resume
```

## 📁 文件结构

```
项目目录/
├── KK1000.txt                    # 电话号码文件（必需）
├── batch_phone_scraper.py        # 核心爬取器
├── start_batch_scraper.py        # 启动脚本
├── start_batch_scraper.bat       # Windows批处理启动器
├── start_batch_scraper.sh        # Linux/Mac启动器
├── test_instant_load.py          # 依赖的爬取模块
├── CloudflareBypasser.py         # Cloudflare绕过模块
├── scraped_data/                 # 输出目录
│   ├── phone_5619324217_data.csv # 单个号码的数据文件
│   ├── phone_4075350820_data.csv
│   └── processed_phones.txt      # 已处理号码记录
├── batch_scraper.log             # 详细日志文件
└── README_BATCH_SCRAPER.md       # 使用说明
```

## ⚙️ 配置选项

### 命令行参数

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| `--phone-file` | `-f` | 电话号码文件路径 | KK1000.txt |
| `--output-dir` | `-o` | 输出目录 | scraped_data |
| `--max-phones` | `-m` | 最大处理数量 | 全部 |
| `--delay` | `-d` | 号码间延迟（秒） | 5 |
| `--resume` | `-r` | 断点续传 | False |
| `--test` | `-t` | 测试模式 | False |

### 电话号码文件格式

KK1000.txt文件格式要求：
- 每行一个电话号码
- 11位数字，以1开头
- 程序会自动去掉前缀1

示例：
```
15619324217
14075350820
14082183907
12146058518
...
```

## 📊 使用示例

### 测试模式

```bash
python start_batch_scraper.py --test
```

输出示例：
```
================================================================================
🚀 批量电话号码背景报告爬取器
================================================================================
🧪 测试模式: 只处理前3个电话号码
📁 电话号码文件: KK1000.txt
📂 输出目录: scraped_data
📊 最大处理数量: 3
⏰ 延迟间隔: 2秒

🚀 开始批量爬取...
📞 处理进度: 1/3 (33.3%)
🔍 开始处理电话号码: 5619324217
✅ 电话号码 5619324217 处理成功，耗时: 45.23秒
📊 当前统计 - 已处理: 1/3, 成功: 1, 失败: 0, 耗时: 45秒
```

### 正常模式

```bash
python start_batch_scraper.py --max-phones 100
```

### 断点续传

```bash
# 如果程序中断，可以从上次停止的地方继续
python start_batch_scraper.py --resume
```

## 📈 输出结果

### 1. CSV数据文件

每个成功处理的电话号码都会生成一个CSV文件：

**文件名**: `phone_5619324217_data.csv`

| 类型 | 姓名 | 年龄 | 手机 | 地址 | 关系 |
|------|------|------|------|------|------|
| 本人 | Brenda Mccorvey | 60 | (561)932-4217 | Royal Palm Beach, FL | 本人 |
| 亲属 | Barbara Mccorvey | 58 | (561)502-3399 | Riviera Beach, FL | 亲属 |
| 亲属 | Annie Mcphee | 99 | (267)233-1581 | West Palm Beach, FL | 亲属 |

### 2. 处理记录文件

**文件**: `scraped_data/processed_phones.txt`
```
5619324217
4075350820
4082183907
...
```

### 3. 日志文件

**文件**: `batch_scraper.log`
```
2024-01-01 10:00:00 - INFO - 批量爬取器初始化完成
2024-01-01 10:00:01 - INFO - 成功加载 1002 个有效电话号码
2024-01-01 10:00:02 - INFO - 🔍 开始处理电话号码: 5619324217
2024-01-01 10:00:45 - INFO - ✅ 电话号码 5619324217 处理成功，耗时: 43.21秒
```

## 🔧 异常处理机制

### 1. 网络异常
- 自动重试机制
- Cloudflare绕过
- 页面加载超时处理

### 2. 数据异常
- 无效电话号码跳过
- 页面解析失败处理
- 空数据处理

### 3. 系统异常
- 浏览器崩溃恢复
- 内存不足处理
- 磁盘空间检查

### 4. 用户中断
- 优雅退出机制
- 保存当前进度
- 支持断点续传

## 📊 性能统计

### 处理速度
- **平均每个号码**: 30-60秒
- **成功率**: 85-95%
- **并发处理**: 单线程顺序处理（避免被封）

### 资源消耗
- **内存使用**: 200-500MB
- **磁盘空间**: 每个号码约10-50KB
- **网络流量**: 每个号码约1-5MB

## ⚠️ 注意事项

### 1. 合规使用
- 仅用于合法的背景调查目的
- 遵守相关法律法规
- 尊重隐私权

### 2. 技术限制
- 需要稳定的网络连接
- 建议在网络较好的环境下运行
- 避免同时运行多个实例

### 3. 数据质量
- 部分号码可能无背景报告
- 数据准确性依赖于源网站
- 建议人工验证重要信息

## 🐛 故障排除

### 常见问题

1. **电话号码文件格式错误**
   ```
   错误: 第X行电话号码格式无效
   解决: 确保每行11位数字，以1开头
   ```

2. **浏览器启动失败**
   ```
   错误: 无法找到Chrome浏览器
   解决: 安装Chrome浏览器或检查路径
   ```

3. **网络连接问题**
   ```
   错误: 页面加载失败
   解决: 检查网络连接，重试或使用代理
   ```

4. **Cloudflare拦截**
   ```
   错误: 检测到Cloudflare保护
   解决: 程序会自动绕过，如失败请重试
   ```

### 调试模式

启用详细日志：
```python
# 修改 batch_phone_scraper.py 中的日志级别
logging.basicConfig(level=logging.DEBUG)
```

## 🔄 更新日志

- **v1.0.0**: 初始版本
  - 支持批量处理电话号码
  - 自动去前缀1功能
  - 完善的异常处理
  - 断点续传功能
  - 详细的统计和日志

## 💡 使用建议

1. **首次使用**: 建议先用测试模式验证功能
2. **大批量处理**: 分批处理，避免一次性处理过多
3. **网络环境**: 在网络稳定的环境下运行
4. **监控进度**: 定期查看日志文件了解处理状态
5. **数据备份**: 定期备份输出目录中的数据

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件 `batch_scraper.log`
2. 检查电话号码文件格式
3. 确认网络连接状态
4. 验证Chrome浏览器安装

现在您可以开始批量处理电话号码了！🎉
