#!/usr/bin/env python3
"""
爬取器性能对比测试脚本
对比enhanced_batch_scraper.py和seleniumbase_batch_scraper.py的性能
"""

import os
import sys
import time
import logging
import subprocess
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_test_phone_file(filename="test_phones.txt", count=5):
    """创建测试用的电话号码文件"""
    test_phones = [
        "15619324217",
        "15551234567", 
        "15559876543",
        "15555555555",
        "15551111111"
    ]
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for i, phone in enumerate(test_phones[:count]):
                f.write(f"{phone}\n")
        
        logging.info(f"创建测试电话号码文件: {filename}，包含 {count} 个号码")
        return True
        
    except Exception as e:
        logging.error(f"创建测试文件失败: {e}")
        return False

def run_scraper_test(script_name, phone_file, output_dir, max_phones=3, delay=1.5):
    """运行单个爬取器测试"""
    logging.info(f"开始测试: {script_name}")
    
    # 清理输出目录
    output_path = Path(output_dir)
    if output_path.exists():
        import shutil
        shutil.rmtree(output_path)
    
    # 构建命令
    cmd = [
        sys.executable, script_name,
        "--phone-file", phone_file,
        "--output-dir", output_dir,
        "--max-phones", str(max_phones),
        "--delay", str(delay)
    ]
    
    start_time = time.time()
    
    try:
        # 运行爬取器
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600,  # 10分钟超时
            encoding='utf-8'
        )
        
        duration = time.time() - start_time
        
        # 分析结果
        success = result.returncode == 0
        
        # 统计输出文件
        csv_files = list(output_path.glob("*.csv")) if output_path.exists() else []
        total_records = 0
        
        for csv_file in csv_files:
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    total_records += len(lines) - 1  # 减去表头
            except:
                pass
        
        return {
            'script': script_name,
            'success': success,
            'duration': duration,
            'total_records': total_records,
            'avg_time_per_phone': duration / max_phones if max_phones > 0 else 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'csv_files': len(csv_files)
        }
        
    except subprocess.TimeoutExpired:
        duration = time.time() - start_time
        logging.error(f"{script_name} 测试超时")
        return {
            'script': script_name,
            'success': False,
            'duration': duration,
            'total_records': 0,
            'avg_time_per_phone': 0,
            'stdout': '',
            'stderr': 'Timeout',
            'csv_files': 0
        }
        
    except Exception as e:
        duration = time.time() - start_time
        logging.error(f"{script_name} 测试失败: {e}")
        return {
            'script': script_name,
            'success': False,
            'duration': duration,
            'total_records': 0,
            'avg_time_per_phone': 0,
            'stdout': '',
            'stderr': str(e),
            'csv_files': 0
        }

def print_comparison_results(results):
    """打印对比结果"""
    print("\n" + "="*80)
    print("🏆 爬取器性能对比结果")
    print("="*80)
    
    for result in results:
        script = result['script']
        success = "✅ 成功" if result['success'] else "❌ 失败"
        duration = result['duration']
        records = result['total_records']
        avg_time = result['avg_time_per_phone']
        
        print(f"\n📊 {script}")
        print(f"   状态: {success}")
        print(f"   总耗时: {duration:.2f}秒")
        print(f"   提取记录数: {records}")
        print(f"   平均处理时间: {avg_time:.2f}秒/个")
        print(f"   CSV文件数: {result['csv_files']}")
        
        if result['stderr'] and result['stderr'] != 'Timeout':
            print(f"   错误信息: {result['stderr'][:200]}...")
    
    # 性能对比
    if len(results) >= 2:
        enhanced_result = next((r for r in results if 'enhanced' in r['script']), None)
        selenium_result = next((r for r in results if 'seleniumbase' in r['script']), None)
        
        if enhanced_result and selenium_result:
            print(f"\n🔥 性能对比:")
            
            if enhanced_result['success'] and selenium_result['success']:
                time_improvement = enhanced_result['avg_time_per_phone'] - selenium_result['avg_time_per_phone']
                improvement_percent = (time_improvement / enhanced_result['avg_time_per_phone']) * 100
                
                print(f"   平均处理时间改进: {time_improvement:.2f}秒 ({improvement_percent:.1f}%)")
                
                if selenium_result['total_records'] > enhanced_result['total_records']:
                    record_improvement = selenium_result['total_records'] - enhanced_result['total_records']
                    print(f"   数据提取改进: +{record_improvement} 条记录")
                
                if improvement_percent > 0:
                    print(f"   🎉 SeleniumBase版本性能更优！")
                else:
                    print(f"   ⚠️ Enhanced版本性能更优")
            else:
                print(f"   ⚠️ 无法进行性能对比（部分测试失败）")

def main():
    """主函数"""
    print("🚀 开始爬取器性能对比测试")
    
    # 检查脚本文件是否存在
    scripts_to_test = [
        "enhanced_batch_scraper.py",
        "seleniumbase_batch_scraper.py"
    ]
    
    available_scripts = []
    for script in scripts_to_test:
        if os.path.exists(script):
            available_scripts.append(script)
            logging.info(f"✅ 找到脚本: {script}")
        else:
            logging.warning(f"⚠️ 脚本不存在: {script}")
    
    if len(available_scripts) < 2:
        logging.error("❌ 需要至少两个脚本进行对比测试")
        return
    
    # 创建测试电话号码文件
    test_phone_file = "test_phones.txt"
    if not create_test_phone_file(test_phone_file, count=3):
        return
    
    # 测试参数
    test_params = {
        'phone_file': test_phone_file,
        'max_phones': 3,
        'delay': 1.5
    }
    
    results = []
    
    # 运行每个脚本的测试
    for script in available_scripts:
        output_dir = f"test_output_{script.replace('.py', '')}"
        
        logging.info(f"\n{'='*50}")
        logging.info(f"测试脚本: {script}")
        logging.info(f"输出目录: {output_dir}")
        logging.info(f"{'='*50}")
        
        result = run_scraper_test(
            script, 
            test_params['phone_file'],
            output_dir,
            test_params['max_phones'],
            test_params['delay']
        )
        
        results.append(result)
        
        # 短暂休息
        time.sleep(2)
    
    # 打印对比结果
    print_comparison_results(results)
    
    # 清理测试文件
    try:
        os.remove(test_phone_file)
        logging.info(f"清理测试文件: {test_phone_file}")
    except:
        pass
    
    print(f"\n🎯 对比测试完成！")

if __name__ == "__main__":
    main()
