#!/usr/bin/env python3
"""
并发电话号码背景报告爬取器
使用多浏览器实例并发处理，大幅提升抓取效率
"""

import os
import sys
import time
import logging
import traceback
import random
import asyncio
import threading
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import csv
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import multiprocessing as mp

from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
from CloudflareBypasser import CloudflareBypasser
from test_instant_load import (
    find_chrome_path, instant_page_load, find_background_report_instant,
    extract_person_info, extract_relatives_info, click_relative_link_and_extract
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] %(message)s',
    handlers=[
        logging.FileHandler('concurrent_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class ConcurrentPhoneScraper:
    """并发电话号码背景报告爬取器"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data", 
                 max_workers: int = 3, batch_size: int = 50):
        """
        初始化并发爬取器
        
        Args:
            phone_file: 电话号码文件路径
            output_dir: 输出目录
            max_workers: 最大并发工作线程数
            batch_size: 每批处理的电话号码数量
        """
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.max_workers = max_workers
        self.batch_size = batch_size
        
        # 统计信息（线程安全）
        self.stats_lock = threading.Lock()
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None,
            'workers_active': 0
        }
        
        # 已处理的电话号码记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()
        
        # 统一的CSV文件（线程安全写入）
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_lock = threading.Lock()
        self.csv_initialized = False
        
        # 电话号码队列
        self.phone_queue = Queue()
        
        logging.info(f"并发爬取器初始化完成")
        logging.info(f"最大并发数: {max_workers}")
        logging.info(f"批处理大小: {batch_size}")
        logging.info(f"已处理电话号码: {len(self.processed_phones)}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """线程安全保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")
    
    def init_unified_csv(self):
        """线程安全初始化统一的CSV文件"""
        with self.csv_lock:
            if not self.csv_initialized:
                try:
                    if not self.unified_csv_file.exists():
                        with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                            writer = csv.writer(f)
                            writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                        logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                    else:
                        logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
                    self.csv_initialized = True
                except Exception as e:
                    logging.error(f"初始化CSV文件失败: {e}")
    
    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """线程安全保存数据到CSV"""
        try:
            self.init_unified_csv()
            with self.csv_lock:
                with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        phone,
                        data_type,
                        info.get('姓名', ''),
                        info.get('年龄', ''),
                        info.get('手机', ''),
                        info.get('地址', ''),
                        info.get('关系', data_type),
                        info.get('链接', '')
                    ])
            return True
        except Exception as e:
            logging.error(f"保存数据失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """从文件中加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                        
                        # 验证电话号码格式（10位数字）
                        import re
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def create_browser_instance(self, worker_id: int) -> ChromiumPage:
        """为每个工作线程创建独立的浏览器实例"""
        try:
            chrome_path = find_chrome_path()
            if not chrome_path:
                raise Exception("无法找到Chrome浏览器")
            
            # 创建浏览器配置
            options = ChromiumOptions()
            options.set_paths(browser_path=chrome_path)
            
            # 为每个实例设置不同的用户数据目录，避免冲突
            user_data_dir = self.output_dir / f"chrome_profile_{worker_id}"
            user_data_dir.mkdir(exist_ok=True)
            options.set_argument(f"--user-data-dir={user_data_dir}")
            
            # 高效抓取参数
            fast_args = [
                "--no-first-run",
                "--disable-gpu",
                "--disable-dev-shm-usage", 
                "--no-sandbox",
                "--disable-web-security",
                "--disable-extensions",
                "--disable-images",
                "--disable-plugins",
                "--disable-background-timer-throttling",
                "--aggressive-cache-discard",
                "--disable-background-networking",
                "--disable-sync",
                "--disable-translate",
                "--disable-client-side-phishing-detection",
                "--disable-component-update",
                "--disable-default-apps",
                "--disable-domain-reliability",
                "--page-load-strategy=none",
                "--disable-blink-features=AutomationControlled",
                "--disable-infobars",
                "--disable-notifications",
                "--disable-popup-blocking",
                "--disable-background-mode",
                "--disable-renderer-backgrounding",
                "--no-default-browser-check",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost"
            ]
            
            for arg in fast_args:
                options.set_argument(arg)
            
            # 资源过滤偏好
            prefs = {
                "profile.default_content_setting_values": {
                    "images": 2,
                    "plugins": 2,
                    "popups": 2,
                    "geolocation": 2,
                    "notifications": 2,
                    "media_stream": 2,
                }
            }
            options.set_pref("prefs", prefs)
            
            # 启动浏览器
            driver = ChromiumPage(addr_or_opts=options)
            driver.set.timeouts(page_load=2)
            driver.set.load_mode.none()
            
            logging.info(f"Worker-{worker_id}: 浏览器实例创建成功")
            return driver
            
        except Exception as e:
            logging.error(f"Worker-{worker_id}: 创建浏览器实例失败: {e}")
            raise

    def scrape_single_phone(self, driver: ChromiumPage, phone: str, worker_id: int) -> Tuple[bool, Dict]:
        """单个电话号码的抓取逻辑"""
        start_time = time.time()

        try:
            logging.info(f"Worker-{worker_id}: 开始处理电话号码 {phone}")

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"

            # 页面加载
            if not instant_page_load(driver, target_url, max_wait=2):
                logging.error(f"Worker-{worker_id}: 页面加载失败 {phone}")
                return False, {}

            # Cloudflare处理
            page_source = driver.html
            cloudflare_keywords = ["cloudflare", "just a moment", "checking your browser"]
            has_cloudflare = any(keyword in page_source.lower() for keyword in cloudflare_keywords)

            if has_cloudflare:
                logging.info(f"Worker-{worker_id}: 检测到Cloudflare，使用bypasser处理 {phone}")
                cf_bypasser = CloudflareBypasser(driver, max_retries=3, log=False)
                cf_bypasser.bypass()

            # 查找背景报告按钮
            button_found, button_url = find_background_report_instant(driver)
            if not button_found:
                logging.warning(f"Worker-{worker_id}: 未找到背景报告按钮 {phone}")
                return False, {}

            # 访问背景报告页面
            if not instant_page_load(driver, button_url, max_wait=2):
                logging.error(f"Worker-{worker_id}: 背景报告页面加载失败 {phone}")
                return False, {}

            # 背景报告页面Cloudflare处理
            bg_source = driver.html
            bg_has_cloudflare = any(keyword in bg_source.lower() for keyword in cloudflare_keywords)
            if bg_has_cloudflare:
                logging.info(f"Worker-{worker_id}: 背景报告页面Cloudflare处理 {phone}")
                cf_bypasser = CloudflareBypasser(driver, max_retries=3, log=False)
                cf_bypasser.bypass()

            # 等待页面稳定
            time.sleep(1)

            # 解析页面内容
            page_source = driver.html
            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取个人信息
            person_info = extract_person_info(soup)
            if not person_info.get('姓名'):
                logging.warning(f"Worker-{worker_id}: 未提取到个人信息 {phone}")
                return False, {}

            # 保存个人信息
            self.save_to_csv(phone, '本人', person_info)

            # 提取亲属信息
            relatives_info = extract_relatives_info(soup, min_age=40)
            logging.info(f"Worker-{worker_id}: 找到 {len(relatives_info)} 个符合条件的亲属 {phone}")

            # 处理亲属信息（简化版，只处理前10个以提高效率）
            relatives_processed = 0
            max_relatives = min(10, len(relatives_info))  # 限制处理数量以提高并发效率

            for i, relative in enumerate(relatives_info[:max_relatives]):
                try:
                    relative_name = relative.get('姓名', '')
                    if relative_name:
                        # 快速处理亲属
                        relative_details = click_relative_link_and_extract(driver, relative_name, soup)

                        combined_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': relative_details.get('手机', ''),
                            '地址': relative_details.get('地址', '')
                        }

                        self.save_to_csv(phone, '亲属', combined_info)
                        relatives_processed += 1

                        # 快速延迟
                        time.sleep(0.3)

                except Exception as e:
                    logging.warning(f"Worker-{worker_id}: 处理亲属失败 {relative_name}: {e}")
                    continue

            duration = time.time() - start_time
            logging.info(f"Worker-{worker_id}: ✅ 成功处理 {phone}, 耗时: {duration:.2f}秒, 亲属: {relatives_processed}")

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': relatives_processed,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"Worker-{worker_id}: ❌ 处理失败 {phone}, 耗时: {duration:.2f}秒, 错误: {e}")
            return False, {}

    def worker_thread(self, worker_id: int, phone_list: List[str]):
        """工作线程函数"""
        driver = None
        try:
            # 更新活跃工作线程数
            with self.stats_lock:
                self.stats['workers_active'] += 1

            logging.info(f"Worker-{worker_id}: 启动，分配 {len(phone_list)} 个电话号码")

            # 创建浏览器实例
            driver = self.create_browser_instance(worker_id)

            # 处理分配的电话号码
            for phone in phone_list:
                try:
                    # 检查是否已处理
                    if phone in self.processed_phones:
                        logging.info(f"Worker-{worker_id}: 跳过已处理的号码 {phone}")
                        continue

                    # 处理电话号码
                    success, result = self.scrape_single_phone(driver, phone, worker_id)

                    # 更新统计
                    with self.stats_lock:
                        self.stats['processed_phones'] += 1
                        if success:
                            self.stats['successful_scrapes'] += 1
                            self.save_processed_phone(phone)
                        else:
                            self.stats['failed_scrapes'] += 1

                    # 工作线程间的延迟
                    delay = random.uniform(0.5, 1.5)
                    time.sleep(delay)

                except Exception as e:
                    logging.error(f"Worker-{worker_id}: 处理电话号码 {phone} 时发生异常: {e}")
                    with self.stats_lock:
                        self.stats['failed_scrapes'] += 1

        except Exception as e:
            logging.error(f"Worker-{worker_id}: 工作线程异常: {e}")

        finally:
            # 清理浏览器实例
            if driver:
                try:
                    driver.quit()
                    logging.info(f"Worker-{worker_id}: 浏览器已关闭")
                except Exception as e:
                    logging.error(f"Worker-{worker_id}: 关闭浏览器失败: {e}")

            # 更新活跃工作线程数
            with self.stats_lock:
                self.stats['workers_active'] -= 1

            logging.info(f"Worker-{worker_id}: 工作线程结束")

    def run_concurrent_scraping(self, max_phones: Optional[int] = None) -> Dict:
        """运行并发爬取"""
        self.stats['start_time'] = datetime.now()
        logging.info("🚀 开始并发爬取任务")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 将电话号码分配给工作线程
        phones_per_worker = len(unprocessed_phones) // self.max_workers
        if phones_per_worker == 0:
            phones_per_worker = 1
            self.max_workers = len(unprocessed_phones)

        phone_batches = []
        for i in range(self.max_workers):
            start_idx = i * phones_per_worker
            if i == self.max_workers - 1:  # 最后一个工作线程处理剩余的
                end_idx = len(unprocessed_phones)
            else:
                end_idx = start_idx + phones_per_worker

            batch = unprocessed_phones[start_idx:end_idx]
            if batch:  # 只添加非空批次
                phone_batches.append(batch)

        logging.info(f"创建 {len(phone_batches)} 个工作线程，每个处理约 {phones_per_worker} 个号码")

        # 启动工作线程
        threads = []
        try:
            for i, phone_batch in enumerate(phone_batches):
                thread = threading.Thread(
                    target=self.worker_thread,
                    args=(i + 1, phone_batch),
                    name=f"Worker-{i + 1}"
                )
                thread.start()
                threads.append(thread)

                # 错开启动时间，避免同时创建浏览器
                time.sleep(2)

            # 监控进度
            self.monitor_progress()

            # 等待所有线程完成
            for thread in threads:
                thread.join()

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在停止所有工作线程...")
        except Exception as e:
            logging.error(f"并发处理过程中发生错误: {e}")

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 并发爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        success_rate = self.stats['successful_scrapes'] / max(1, self.stats['processed_phones']) * 100
        logging.info(f"成功率: {success_rate:.1f}%")

        if self.stats['successful_scrapes'] > 0:
            avg_time = duration / self.stats['successful_scrapes']
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
            logging.info(f"并发效率提升: {self.max_workers}x")

        logging.info("=" * 80)

        return self.stats

    def monitor_progress(self):
        """监控处理进度"""
        def progress_monitor():
            while True:
                with self.stats_lock:
                    if self.stats['workers_active'] == 0:
                        break

                    if self.stats['start_time']:
                        elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
                        processed = self.stats['processed_phones']
                        total = self.stats['total_phones']
                        success = self.stats['successful_scrapes']
                        failed = self.stats['failed_scrapes']
                        active = self.stats['workers_active']

                        if processed > 0:
                            progress = processed / total * 100
                            avg_time = elapsed / processed
                            eta = (total - processed) * avg_time / max(1, active)

                            logging.info(f"📊 进度: {processed}/{total} ({progress:.1f}%) | "
                                       f"成功: {success} | 失败: {failed} | "
                                       f"活跃线程: {active} | ETA: {eta/60:.1f}分钟")

                time.sleep(30)  # 每30秒报告一次进度

        monitor_thread = threading.Thread(target=progress_monitor, daemon=True)
        monitor_thread.start()


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='并发电话号码背景报告爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--workers', type=int, default=3, help='并发工作线程数')
    parser.add_argument('--batch-size', type=int, default=50, help='批处理大小')

    args = parser.parse_args()

    try:
        # 创建并发爬取器
        scraper = ConcurrentPhoneScraper(
            phone_file=args.phone_file,
            output_dir=args.output_dir,
            max_workers=args.workers,
            batch_size=args.batch_size
        )

        # 运行并发爬取
        stats = scraper.run_concurrent_scraping(args.max_phones)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
