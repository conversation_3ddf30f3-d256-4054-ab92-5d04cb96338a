#!/usr/bin/env python3
"""
LTESocks端口重置定时任务测试脚本
"""

import time
import logging
from ltesocks_port_reset_scheduler import LTESocksPortResetScheduler

def test_scheduler():
    """测试定时任务功能"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # API密钥
    API_KEY = "36bdbd05002f1aa3092512db7aca059cc5a43419b3d6abbe1f4150455b4249dd"
    
    print("=" * 60)
    print("🧪 LTESocks端口重置定时任务测试")
    print("=" * 60)
    
    try:
        # 创建调度器（测试用短间隔：30秒）
        scheduler = LTESocksPortResetScheduler(API_KEY, reset_interval=30)
        
        print("1. 测试一次性重置...")
        result = scheduler.reset_all_ports()
        print(f"   结果: {result['message']}")
        print(f"   耗时: {result['duration']:.2f}秒")
        
        print("\n2. 测试状态获取...")
        status = scheduler.get_status()
        print(f"   运行状态: {status['running']}")
        print(f"   重置间隔: {status['reset_interval']}秒")
        
        print("\n3. 启动定时任务（运行2分钟后自动停止）...")
        scheduler.start()
        
        # 运行2分钟
        for i in range(4):  # 4次，每次30秒
            time.sleep(30)
            print(f"\n   第 {i+1} 次状态检查:")
            scheduler.print_status()
        
        print("\n4. 停止定时任务...")
        scheduler.stop()
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_scheduler()
