#!/usr/bin/env python3
"""
使用BeautifulSoup快速查找并访问背景报告页面的测试脚本
"""

import time
import logging
import os
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fast_bs4_test.log', mode='w', encoding='utf-8')
    ]
)

def find_chrome_path():
    """查找Chrome浏览器路径"""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"找到Chrome浏览器: {path}")
            return path
    
    logging.warning("未找到Chrome浏览器")
    return None

def find_background_report_link_ultra_fast(driver):
    """
    超快速查找背景报告链接，使用BeautifulSoup直接解析
    """
    try:
        start_time = time.time()
        logging.info("正在超快速查找 'Open Free Background Report' 按钮...")
        
        # 立即获取页面源码，不等待任何加载
        page_source = driver.html
        parse_time = time.time() - start_time
        logging.info(f"获取页面源码耗时: {parse_time:.3f}秒，长度: {len(page_source)} 字符")
        
        # 使用BeautifulSoup解析
        soup_start = time.time()
        soup = BeautifulSoup(page_source, 'html.parser')
        soup_time = time.time() - soup_start
        logging.info(f"BeautifulSoup解析耗时: {soup_time:.3f}秒")
        
        # 方法1: 直接通过class查找 (最快方法)
        class_start = time.time()
        target_buttons = soup.find_all('a', class_='btn btn-primary btn-sm btn-block text-center')
        class_time = time.time() - class_start
        logging.info(f"Class查找耗时: {class_time:.3f}秒，找到 {len(target_buttons)} 个按钮")
        
        for i, button in enumerate(target_buttons):
            button_text = button.get_text().strip()
            href = button.get('href', '')
            
            logging.info(f"按钮 {i+1}: '{button_text}' -> {href}")
            
            if 'Open Free Background Report' in button_text:
                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                total_time = time.time() - start_time
                logging.info(f"✅ 通过class找到目标按钮，总耗时: {total_time:.3f}秒")
                logging.info(f"按钮文本: '{button_text}'")
                logging.info(f"目标链接: {href}")
                
                # 直接访问链接
                nav_start = time.time()
                driver.get(href)
                nav_time = time.time() - nav_start
                logging.info(f"页面导航耗时: {nav_time:.3f}秒")
                
                return True, href
        
        # 方法2: 如果class方法失败，使用文本搜索
        text_start = time.time()
        all_links = soup.find_all('a', href=True)
        text_time = time.time() - text_start
        logging.info(f"获取所有链接耗时: {text_time:.3f}秒，找到 {len(all_links)} 个链接")
        
        for i, link in enumerate(all_links):
            link_text = link.get_text().strip()
            href = link.get('href', '')
            title = link.get('title', '')
            
            # 检查是否包含目标文本
            if any(keyword in link_text for keyword in ['Open Free Background Report', 'Free Background Report']):
                logging.info(f"通过文本匹配找到链接 {i+1}: '{link_text}' -> {href}")
                
                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                total_time = time.time() - start_time
                logging.info(f"✅ 通过文本匹配找到目标，总耗时: {total_time:.3f}秒")
                
                # 直接访问链接
                driver.get(href)
                return True, href
            
            # 检查title属性
            if 'Free Background Report' in title:
                logging.info(f"通过title属性找到链接 {i+1}: '{title}' -> {href}")
                
                # 转换为绝对链接
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                total_time = time.time() - start_time
                logging.info(f"✅ 通过title属性找到目标，总耗时: {total_time:.3f}秒")
                
                # 直接访问链接
                driver.get(href)
                return True, href
        
        total_time = time.time() - start_time
        logging.warning(f"❌ 未找到目标按钮，总耗时: {total_time:.3f}秒")
        return False, None
        
    except Exception as e:
        total_time = time.time() - start_time
        logging.error(f"查找过程出错，耗时: {total_time:.3f}秒，错误: {e}")
        return False, None

def main():
    """主测试函数"""
    logging.info("开始超快速BeautifulSoup测试")
    logging.info("=" * 60)
    
    # 查找浏览器
    chrome_path = find_chrome_path()
    if not chrome_path:
        logging.error("无法找到Chrome浏览器")
        return
    
    # 创建浏览器选项
    try:
        options = ChromiumOptions()
        options.set_paths(browser_path=chrome_path)
        
        # 最快速度的参数配置
        fast_args = [
            "--no-first-run",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-web-security",
            "--disable-extensions",
            "--disable-images",  # 禁用图片
            "--disable-javascript",  # 禁用JS (可选)
            "--disable-plugins",
            "--disable-background-timer-throttling",
            "--aggressive-cache-discard",
            "--disable-background-networking",
            "--disable-sync",
            "--disable-translate",
            "--disable-ipc-flooding-protection",
            "--disable-client-side-phishing-detection",
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-domain-reliability",
            "--disable-features=TranslateUI,BlinkGenPropertyTrees"
        ]
        
        for arg in fast_args:
            options.set_argument(arg)

        # 设置页面加载策略为 eager (不等待所有资源加载完成)
        try:
            options.set_argument("--page-load-strategy=eager")
        except:
            pass

        logging.info("浏览器选项配置完成")
        
    except Exception as e:
        logging.error(f"配置浏览器选项失败: {e}")
        return
    
    # 启动浏览器并测试
    try:
        total_start = time.time()
        
        logging.info("正在启动浏览器...")
        browser_start = time.time()
        driver = ChromiumPage(addr_or_opts=options)
        browser_time = time.time() - browser_start
        logging.info(f"浏览器启动耗时: {browser_time:.3f}秒")
        
        # 设置页面加载超时和策略
        try:
            # 设置页面加载超时为5秒
            driver.set.timeouts(page_load=5)
            logging.info("设置页面加载超时为5秒")
        except Exception as e:
            logging.warning(f"设置超时失败: {e}")

        # 访问目标页面
        target_url = "https://www.smartbackgroundchecks.com/phone/5619324217"
        logging.info(f"正在访问: {target_url}")

        page_start = time.time()
        try:
            driver.get(target_url)
            page_time = time.time() - page_start
            logging.info(f"页面加载完成，耗时: {page_time:.3f}秒")
        except Exception as e:
            page_time = time.time() - page_start
            logging.info(f"页面加载超时或中断，耗时: {page_time:.3f}秒，继续处理...")
            # 即使超时也继续处理，因为DOM可能已经加载了
        
        # 检查Cloudflare（快速检查）
        cf_start = time.time()
        page_source = driver.html
        has_cloudflare = any(keyword in page_source.lower() for keyword in ["cloudflare", "just a moment", "checking your browser"])
        cf_time = time.time() - cf_start
        logging.info(f"Cloudflare检测耗时: {cf_time:.3f}秒")
        
        if has_cloudflare:
            logging.info("检测到Cloudflare保护，开始绕过...")
            bypass_start = time.time()
            cf_bypasser = CloudflareBypasser(driver)
            cf_bypasser.bypass()
            bypass_time = time.time() - bypass_start
            logging.info(f"Cloudflare绕过耗时: {bypass_time:.3f}秒")
        
        # 超快速查找并访问背景报告
        logging.info("=" * 50)
        button_found, button_url = find_background_report_link_ultra_fast(driver)
        
        if button_found:
            logging.info(f"✅ 成功找到并访问背景报告页面!")
            logging.info(f"当前页面URL: {driver.url}")
            logging.info(f"目标链接: {button_url}")
            
            # 获取新页面信息
            time.sleep(1)  # 短暂等待页面稳定
            new_page_source = driver.html
            soup = BeautifulSoup(new_page_source, 'html.parser')
            new_title = soup.title.string if soup.title else "无标题"
            
            logging.info(f"新页面标题: {new_title}")
            logging.info(f"新页面源码长度: {len(new_page_source)} 字符")
            
            # 保存新页面源码
            with open('fast_background_report_page.html', 'w', encoding='utf-8') as f:
                f.write(new_page_source)
            logging.info("背景报告页面源码已保存到 fast_background_report_page.html")
            
        else:
            logging.error("❌ 未找到背景报告按钮")
        
        total_time = time.time() - total_start
        logging.info("=" * 60)
        logging.info(f"🎯 总执行时间: {total_time:.3f}秒")
        
    except Exception as e:
        total_time = time.time() - total_start
        logging.error(f"测试过程中出错，总耗时: {total_time:.3f}秒，错误: {e}")
    finally:
        try:
            driver.quit()
            logging.info("浏览器已关闭")
        except:
            pass
    
    logging.info("测试完成")

if __name__ == '__main__':
    main()
