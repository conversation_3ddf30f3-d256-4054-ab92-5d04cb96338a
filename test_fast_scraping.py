#!/usr/bin/env python3
"""
快速网页抓取测试脚本
测试优化后的Cloudflare检测和页面源码获取功能
"""

import time
import logging
import os
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fast_scraping_test.log', mode='w')
    ]
)

def get_fast_chromium_options():
    """
    获取优化的Chromium配置，专注于快速加载
    """
    options = ChromiumOptions().auto_port()
    
    # 快速加载参数
    fast_arguments = [
        "--no-first-run",
        "--disable-gpu",
        "--disable-images",  # 禁用图片
        "--disable-plugins",
        "--disable-extensions",
        "--disable-dev-shm-usage",
        "--no-sandbox",
        "--disable-web-security",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--aggressive-cache-discard",
        "--page-load-strategy=eager",  # 急切加载策略
    ]
    
    for arg in fast_arguments:
        options.set_argument(arg)
    
    return options

def quick_cloudflare_check(driver):
    """
    超快速Cloudflare检测
    """
    try:
        # 只等待0.3秒
        time.sleep(0.3)
        
        # 立即获取页面源码
        page_source = driver.html
        
        # 快速检查标题
        title = driver.title.lower() if driver.title else ""
        if "just a moment" in title:
            return True, page_source
        
        # 检查源码中的关键词
        source_lower = page_source.lower()
        if any(keyword in source_lower for keyword in ["cloudflare", "just a moment", "checking your browser"]):
            return True, page_source
            
        return False, page_source
        
    except Exception as e:
        logging.warning(f"检测出错: {e}")
        try:
            return False, driver.html
        except:
            return False, ""

def test_url(url):
    """
    测试单个URL的抓取速度
    """
    logging.info(f"开始测试URL: {url}")
    start_time = time.time()
    
    options = get_fast_chromium_options()
    driver = ChromiumPage(addr_or_opts=options)
    
    try:
        # 访问页面
        driver.get(url)
        
        # 快速检测Cloudflare
        has_cf, page_source = quick_cloudflare_check(driver)
        
        if has_cf:
            logging.info("检测到Cloudflare，开始绕过...")
            cf_bypasser = CloudflareBypasser(driver)
            cf_bypasser.bypass()
            page_source = driver.html
        
        # 计算耗时
        elapsed_time = time.time() - start_time
        
        # 简单分析
        soup = BeautifulSoup(page_source, 'html.parser')
        title = soup.title.string if soup.title else "无标题"
        
        logging.info(f"完成! 耗时: {elapsed_time:.2f}秒")
        logging.info(f"页面标题: {title}")
        logging.info(f"源码长度: {len(page_source)} 字符")
        
        return {
            'url': url,
            'success': True,
            'time': elapsed_time,
            'title': title,
            'source_length': len(page_source),
            'has_cloudflare': has_cf
        }
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        logging.error(f"测试失败: {e}, 耗时: {elapsed_time:.2f}秒")
        return {
            'url': url,
            'success': False,
            'time': elapsed_time,
            'error': str(e)
        }
    finally:
        driver.quit()

def main():
    """
    主测试函数
    """
    test_urls = [
        'https://www.smartbackgroundchecks.com/phone/5619324217',
        'https://httpbin.org/html',  # 简单测试页面
        'https://example.com',  # 基础测试
    ]
    
    results = []
    
    for url in test_urls:
        result = test_url(url)
        results.append(result)
        time.sleep(1)  # 短暂间隔
    
    # 输出总结
    logging.info("=" * 50)
    logging.info("测试总结:")
    for result in results:
        if result['success']:
            logging.info(f"✓ {result['url']} - {result['time']:.2f}秒")
        else:
            logging.info(f"✗ {result['url']} - 失败: {result.get('error', '未知错误')}")

if __name__ == '__main__':
    main()
