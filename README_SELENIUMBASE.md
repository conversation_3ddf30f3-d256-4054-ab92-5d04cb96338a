# SeleniumBase批量电话号码爬取器

基于SeleniumBase UC Mode + CDP Mode的高级反机器人检测批量数据抓取脚本。

## 🌟 主要特性

- **🤖 反机器人检测**: 使用SeleniumBase UC Mode绕过Cloudflare、reCAPTCHA等反机器人系统
- **🐙 CDP Mode**: 使用Chrome DevTools Protocol进行隐蔽操作
- **🧠 智能延迟**: 自适应延迟算法，根据成功率动态调整请求间隔
- **📊 实时保存**: 数据实时保存到CSV文件，避免数据丢失
- **🔄 断点续传**: 自动跳过已处理的电话号码
- **📈 详细统计**: 实时显示处理进度和成功率

## 🚀 快速开始

### 1. 安装依赖

```bash
# 运行自动安装脚本
python install_seleniumbase.py
```

或手动安装：

```bash
# 安装SeleniumBase
pip install seleniumbase

# 安装其他依赖
pip install beautifulsoup4 lxml requests pyautogui

# 安装浏览器驱动
seleniumbase install chromedriver
```

### 2. 准备电话号码文件

创建 `KK1000.txt` 文件，每行一个电话号码：

```
15551234567
15559876543
...
```

### 3. 运行爬取器

```bash
# 基本用法
python seleniumbase_batch_scraper.py

# 限制处理数量
python seleniumbase_batch_scraper.py --max-phones 10

# 自定义延迟时间
python seleniumbase_batch_scraper.py --delay 3.0

# 自定义文件路径
python seleniumbase_batch_scraper.py --phone-file my_phones.txt --output-dir my_data
```

## 📋 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--phone-file` | `KK1000.txt` | 电话号码文件路径 |
| `--output-dir` | `scraped_data` | 输出目录 |
| `--max-phones` | 无限制 | 最大处理电话号码数量 |
| `--delay` | `2.0` | 基础延迟时间（秒） |

## 🔧 技术特性

### SeleniumBase UC Mode
- 自动修改chromedriver特征，避免被检测
- 随机User-Agent和浏览器指纹
- 自动处理Cloudflare Turnstile和reCAPTCHA

### CDP Mode (Chrome DevTools Protocol)
- 在WebDriver断开连接时继续操作
- 使用PyAutoGUI进行人类模拟点击
- 更高级的反检测能力

### 智能延迟算法
```python
# 成功时减少延迟
if success_count >= 3:
    adaptive_delay *= 0.9

# 失败时增加延迟  
if fail_count > 0:
    adaptive_delay *= 1.5
```

## 📁 输出文件

### CSV数据文件
`scraped_data/all_phone_data.csv` 包含以下字段：
- 查询电话
- 类型（本人/亲属）
- 姓名
- 年龄
- 手机
- 地址
- 关系
- 链接

### 处理记录
`scraped_data/processed_phones.txt` 记录已处理的电话号码

### 日志文件
`seleniumbase_batch_scraper.log` 详细的运行日志

## 🛡️ 反检测机制

### 1. UC Mode特性
- 修改WebDriver特征码
- 断开连接策略
- 随机化浏览器行为

### 2. CDP Mode特性
- 使用Chrome DevTools Protocol
- PyAutoGUI人类模拟
- 绕过JavaScript检测

### 3. 智能行为模拟
- 自适应延迟
- 随机等待时间
- 人类化操作模式

## 📊 性能优化

### 资源过滤
- 禁用图片加载
- 禁用插件和扩展
- 启用广告拦截

### 页面加载优化
- 不等待完整页面加载
- 快速DOM解析
- 即时数据提取

## 🔍 故障排除

### 常见问题

1. **CAPTCHA无法自动绕过**
   ```python
   # 手动处理CAPTCHA
   sb.uc_gui_click_captcha()
   ```

2. **页面加载失败**
   ```python
   # 增加延迟时间
   python seleniumbase_batch_scraper.py --delay 5.0
   ```

3. **Chrome驱动问题**
   ```bash
   # 重新安装驱动
   seleniumbase install chromedriver --latest
   ```

### 调试模式

```python
# 启用详细日志
logging.getLogger().setLevel(logging.DEBUG)
```

## 🚨 注意事项

### 法律合规
- 仅爬取公开数据
- 遵守网站服务条款
- 尊重robots.txt

### 使用限制
- 合理控制请求频率
- 避免对目标网站造成压力
- 建议在非高峰时段运行

### 数据隐私
- 妥善保管爬取的数据
- 遵守数据保护法规
- 不要泄露个人信息

## 📈 性能指标

### 典型性能
- 处理速度: 10-30秒/个电话号码
- 成功率: 80-95%（取决于网站状态）
- 内存使用: 200-500MB
- CPU使用: 中等

### 优化建议
- 使用SSD硬盘提高I/O性能
- 确保稳定的网络连接
- 定期清理浏览器缓存

## 🔄 更新日志

### v1.0.0
- 初始版本
- 基于SeleniumBase UC Mode
- 支持CDP Mode
- 智能延迟算法
- 实时数据保存

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用。请遵守相关法律法规。
