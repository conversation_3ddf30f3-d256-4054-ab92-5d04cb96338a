"""
LTESocks API客户端
提供完整的LTESocks API接口实现
"""

import requests
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel


class LTESocksAPIError(Exception):
    """LTESocks API异常"""
    pass


# Pydantic模型定义
class GeoIP(BaseModel):
    continentCode: Optional[str] = None
    continentName: Optional[str] = None
    countryCode2: Optional[str] = None
    countryCode3: Optional[str] = None
    stateProv: Optional[str] = None
    district: Optional[str] = None
    city: Optional[str] = None
    zipcode: Optional[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    isp: Optional[str] = None


class PlanTarification(BaseModel):
    time: int  # 计划持续时间（秒）
    traffic: int  # 流量（MB）
    price: int  # 价格（分）


class PortPlan(BaseModel):
    id: str
    name: str
    countryCode: str
    enabled: bool
    autoRenew: bool
    activatedAt: str
    expiresAt: str
    trafficRemains: int
    tarification: PlanTarification


class PortCredentials(BaseModel):
    ip: List[str]
    password: List[Dict[str, str]]


class Port(BaseModel):
    port: str
    ip: str
    geoip: Optional[GeoIP] = None
    status: str  # active, reset, suspended, disconnected, outoftraffic
    resetToken: str
    signature: str
    vpnAccess: bool
    autoResetInterval: int
    tags: List[str]
    plan: PortPlan
    credentials: PortCredentials


class PortsResponse(BaseModel):
    data: List[Port]
    page: int
    pageSize: int
    total: int


class PortLog(BaseModel):
    action: str
    cause: str
    source: str
    extra: str
    createdAt: str


class PortLogResponse(BaseModel):
    data: List[PortLog]
    page: int
    pageSize: int
    total: int


class UserPreferences(BaseModel):
    vpnServer: Optional[str] = None
    proxyServer: Optional[str] = None
    proxyProtocol: Optional[str] = None


class User(BaseModel):
    login: str
    email: str
    balance: int
    portsCount: int
    portsLimit: int
    preferences: UserPreferences


class Plan(BaseModel):
    id: str
    name: str
    available: bool
    description: str
    countryCode: str
    vpnAccess: bool
    tarifications: List[PlanTarification]


class PlansResponse(BaseModel):
    plans: List[Plan]


class Server(BaseModel):
    host: str
    countryCode: str


class ServersResponse(BaseModel):
    vpnServers: List[Server]
    proxyServers: List[Server]
    proxyProtocols: List[str]


class PaymentHistoryRecord(BaseModel):
    date: str
    status: str  # success, failed
    port: str
    amount: int
    balance: int
    description: str


class PaymentHistoryResponse(BaseModel):
    data: List[PaymentHistoryRecord]
    page: int
    pageSize: int
    total: int


class LTESocksClient:
    """LTESocks API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.ltesocks.io/v2"):
        """
        初始化客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": api_key,
            "Content-Type": "application/json"
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            响应数据
            
        Raises:
            LTESocksAPIError: API请求失败
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            if response.content:
                return response.json()
            return {}
            
        except requests.exceptions.RequestException as e:
            raise LTESocksAPIError(f"API请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise LTESocksAPIError(f"响应解析失败: {str(e)}")
    
    # 用户相关接口
    def get_user(self) -> User:
        """获取用户信息"""
        data = self._make_request("GET", "/user")
        return User(**data)
    
    def update_user_preferences(self, preferences: UserPreferences) -> User:
        """更新用户偏好设置"""
        data = self._make_request("POST", "/user/preferences", json=preferences.dict())
        return User(**data)
    
    # 代理端口相关接口
    def get_ports(self) -> PortsResponse:
        """获取所有端口列表"""
        data = self._make_request("GET", "/ports")
        return PortsResponse(**data)
    
    def get_ports_with_filter(self, 
                             plan: Optional[str] = None,
                             country_code: Optional[str] = None,
                             tags: Optional[List[str]] = None,
                             page: int = 1,
                             page_size: int = 20) -> PortsResponse:
        """
        获取端口列表（带过滤和分页）
        
        Args:
            plan: 计划ID或名称
            country_code: 国家代码
            tags: 标签列表
            page: 页码
            page_size: 每页大小
        """
        payload = {
            "page": page,
            "pageSize": page_size
        }
        
        if plan:
            payload["plan"] = plan
        if country_code:
            payload["countryCode"] = country_code
        if tags:
            payload["tags"] = tags
            
        data = self._make_request("POST", "/ports", json=payload)
        return PortsResponse(**data)
    
    def get_port(self, port_id: str) -> Port:
        """获取特定端口详情"""
        data = self._make_request("GET", f"/ports/{port_id}")
        return Port(**data)
    
    def delete_port(self, port_id: str) -> bool:
        """删除端口"""
        try:
            self._make_request("DELETE", f"/ports/{port_id}")
            return True
        except LTESocksAPIError:
            return False
    
    def delete_port_alt(self, port_id: str) -> bool:
        """删除端口（备用方法）"""
        try:
            self._make_request("POST", f"/ports/{port_id}/delete")
            return True
        except LTESocksAPIError:
            return False

    def reset_port(self, port_id: str) -> Port:
        """
        重置端口

        Args:
            port_id: 端口ID

        Returns:
            更新后的端口信息
        """
        data = self._make_request("POST", f"/ports/{port_id}/reset")
        return Port(**data)

    def get_port_log(self, port_id: str) -> PortLogResponse:
        """获取端口日志"""
        data = self._make_request("GET", f"/ports/{port_id}/log")
        return PortLogResponse(**data)

    def get_port_log_with_filter(self,
                                port_id: str,
                                from_date: Optional[str] = None,
                                to_date: Optional[str] = None,
                                page: int = 1,
                                page_size: int = 20) -> PortLogResponse:
        """
        获取端口日志（带过滤和分页）

        Args:
            port_id: 端口ID
            from_date: 开始日期 (RFC3339格式)
            to_date: 结束日期 (RFC3339格式)
            page: 页码
            page_size: 每页大小
        """
        payload = {
            "page": page,
            "pageSize": page_size
        }

        if from_date:
            payload["from"] = from_date
        if to_date:
            payload["to"] = to_date

        data = self._make_request("POST", f"/ports/{port_id}/log", json=payload)
        return PortLogResponse(**data)

    def get_vpn_config(self, port_id: str) -> bytes:
        """获取VPN配置文件"""
        url = f"{self.base_url}/ports/{port_id}/vpn"
        response = self.session.get(url)
        response.raise_for_status()
        return response.content

    def get_vpn_config_with_credentials(self, port_id: str, login: str, password: str) -> bytes:
        """
        获取VPN配置文件（使用指定凭据）

        Args:
            port_id: 端口ID
            login: 登录名
            password: 密码
        """
        payload = {
            "login": login,
            "password": password
        }
        url = f"{self.base_url}/ports/{port_id}/vpn"
        response = self.session.post(url, json=payload)
        response.raise_for_status()
        return response.content

    def order_port(self, plan_id: str, tarification: PlanTarification) -> Port:
        """
        订购端口

        Args:
            plan_id: 计划ID
            tarification: 计费信息
        """
        payload = {
            "plan": plan_id,
            "tarification": tarification.dict()
        }
        data = self._make_request("POST", "/ports/order", json=payload)
        return Port(**data)

    def update_port_plan(self, port_id: str, plan_id: str, tarification: PlanTarification) -> Port:
        """
        更新端口计划

        Args:
            port_id: 端口ID
            plan_id: 新计划ID
            tarification: 计费信息
        """
        payload = {
            "plan": plan_id,
            "tarification": tarification.dict()
        }
        data = self._make_request("POST", f"/ports/{port_id}/plan", json=payload)
        return Port(**data)

    def update_port_tags(self, port_id: str, tags: List[str]) -> Port:
        """
        更新端口标签

        Args:
            port_id: 端口ID
            tags: 标签列表
        """
        payload = {"tags": tags}
        data = self._make_request("POST", f"/ports/{port_id}/tags", json=payload)
        return Port(**data)

    def update_port_autorenew(self, port_id: str, auto_renew: bool) -> Port:
        """
        更新端口自动续费设置

        Args:
            port_id: 端口ID
            auto_renew: 是否自动续费
        """
        payload = {"autoRenew": auto_renew}
        data = self._make_request("POST", f"/ports/{port_id}/autorenew", json=payload)
        return Port(**data)

    def update_port_signature(self, port_id: str, signature: str) -> Port:
        """
        更新端口签名

        Args:
            port_id: 端口ID
            signature: 签名
        """
        payload = {"signature": signature}
        data = self._make_request("POST", f"/ports/{port_id}/signature", json=payload)
        return Port(**data)

    def update_port_credentials(self, port_id: str, credentials: PortCredentials) -> Port:
        """
        更新端口凭据

        Args:
            port_id: 端口ID
            credentials: 凭据信息
        """
        data = self._make_request("POST", f"/ports/{port_id}/credentials", json=credentials.dict())
        return Port(**data)

    def update_port_autoreset(self, port_id: str, auto_reset_interval: int) -> Port:
        """
        更新端口自动重置间隔

        Args:
            port_id: 端口ID
            auto_reset_interval: 自动重置间隔（秒），0表示禁用
        """
        payload = {"autoResetInterval": auto_reset_interval}
        data = self._make_request("POST", f"/ports/{port_id}/autoreset", json=payload)
        return Port(**data)

    # 支付相关接口
    def get_payments_history(self) -> PaymentHistoryResponse:
        """获取支付历史"""
        data = self._make_request("GET", "/payments")
        return PaymentHistoryResponse(**data)

    def get_payments_history_with_filter(self,
                                       from_date: Optional[str] = None,
                                       to_date: Optional[str] = None,
                                       page: int = 1,
                                       page_size: int = 20) -> PaymentHistoryResponse:
        """
        获取支付历史（带过滤和分页）

        Args:
            from_date: 开始日期 (RFC3339格式)
            to_date: 结束日期 (RFC3339格式)
            page: 页码
            page_size: 每页大小
        """
        payload = {
            "page": page,
            "pageSize": page_size
        }

        if from_date:
            payload["from"] = from_date
        if to_date:
            payload["to"] = to_date

        data = self._make_request("POST", "/payments", json=payload)
        return PaymentHistoryResponse(**data)

    def get_currencies(self) -> List[str]:
        """获取支持的货币列表"""
        data = self._make_request("GET", "/payments/currencies")
        return data.get("currencies", [])

    def request_payment(self, amount: int, currency: str) -> str:
        """
        请求支付

        Args:
            amount: 支付金额（分，总是USD）
            currency: 支付货币

        Returns:
            支付页面URL
        """
        payload = {
            "amount": amount,
            "currency": currency
        }
        data = self._make_request("POST", "/payments/request", json=payload)
        return data.get("url", "")

    # 计划相关接口
    def get_plans(self) -> PlansResponse:
        """获取所有计划"""
        data = self._make_request("GET", "/plans")
        return PlansResponse(**data)

    # 服务器相关接口
    def get_servers(self) -> ServersResponse:
        """获取服务器列表"""
        data = self._make_request("GET", "/servers")
        return ServersResponse(**data)

    # 签名相关接口
    def get_signatures(self) -> List[str]:
        """获取可用签名列表"""
        data = self._make_request("GET", "/signatures")
        return data.get("signatures", [])

    # 便捷方法
    def get_active_ports(self) -> List[Port]:
        """获取所有活跃的端口"""
        response = self.get_ports()
        return [port for port in response.data if port.status == "active"]

    def get_ports_by_country(self, country_code: str) -> List[Port]:
        """
        根据国家代码获取端口

        Args:
            country_code: 国家代码（如：US, GB, CN等）
        """
        response = self.get_ports_with_filter(country_code=country_code)
        return response.data

    def get_ports_by_plan(self, plan_name: str) -> List[Port]:
        """
        根据计划名称获取端口

        Args:
            plan_name: 计划名称
        """
        response = self.get_ports_with_filter(plan=plan_name)
        return response.data

    def reset_all_ports(self) -> List[Port]:
        """
        重置所有端口

        Returns:
            重置后的端口列表
        """
        ports = self.get_active_ports()
        reset_ports = []

        for port in ports:
            try:
                reset_port = self.reset_port(port.port)
                reset_ports.append(reset_port)
            except LTESocksAPIError as e:
                print(f"重置端口 {port.port} 失败: {e}")

        return reset_ports

    def get_port_proxy_info(self, port_id: str) -> Dict[str, Any]:
        """
        获取端口的代理信息

        Args:
            port_id: 端口ID

        Returns:
            包含代理信息的字典
        """
        port = self.get_port(port_id)
        servers = self.get_servers()

        # 获取默认代理服务器
        proxy_server = servers.proxyServers[0].host if servers.proxyServers else "unknown"

        return {
            "host": proxy_server,
            "port": port.port,
            "ip": port.ip,
            "status": port.status,
            "credentials": port.credentials.dict(),
            "country": port.plan.countryCode,
            "protocols": servers.proxyProtocols
        }
