#!/usr/bin/env python3
"""
批量电话号码背景报告爬取器
从KK1000.txt文件中读取电话号码，去掉前缀1，批量爬取背景报告
具有完善的异常处理和持续运行能力
"""

import os
import sys
import time
import logging
import traceback
import re
import random
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import csv
from pathlib import Path

# 导入现有的模块
from test_instant_load import (
    find_chrome_path, instant_page_load, find_background_report_instant,
    extract_person_info, extract_relatives_info, click_relative_link_and_extract
)
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
from CloudflareBypasser import CloudflareBypasser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class BatchPhoneScraper:
    """批量电话号码背景报告爬取器"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data"):
        """
        初始化爬取器

        Args:
            phone_file: 电话号码文件路径
            output_dir: 输出目录
        """
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 统计信息
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'skipped_phones': 0,
            'start_time': None,
            'current_phone': None
        }

        # 浏览器实例
        self.driver = None
        self.chrome_path = None

        # 已处理的电话号码记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()

        # 统一的CSV文件
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_initialized = False

        logging.info(f"批量爬取器初始化完成")
        logging.info(f"电话号码文件: {phone_file}")
        logging.info(f"输出目录: {output_dir}")
        logging.info(f"统一CSV文件: {self.unified_csv_file}")
        logging.info(f"已处理电话号码: {len(self.processed_phones)}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")

    def init_unified_csv(self):
        """初始化统一的CSV文件"""
        if not self.csv_initialized:
            try:
                # 检查文件是否存在，如果不存在则创建并写入表头
                if not self.unified_csv_file.exists():
                    with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                    logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                else:
                    logging.info(f"使用现有CSV文件: {self.unified_csv_file}")

                self.csv_initialized = True
            except Exception as e:
                logging.error(f"初始化CSV文件失败: {e}")

    def save_person_to_csv(self, phone: str, person_info: Dict):
        """立即保存个人信息到CSV文件"""
        try:
            self.init_unified_csv()

            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    '本人',
                    person_info.get('姓名', ''),
                    person_info.get('年龄', ''),
                    person_info.get('手机', ''),
                    person_info.get('地址', ''),
                    '本人',
                    ''
                ])
            logging.info(f"✅ 已保存本人信息: {person_info.get('姓名', 'N/A')}")
            return True
        except Exception as e:
            logging.error(f"保存本人信息失败: {e}")
            return False

    def save_relative_to_csv(self, phone: str, relative_info: Dict):
        """立即保存单个亲属信息到CSV文件"""
        try:
            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    '亲属',
                    relative_info.get('姓名', ''),
                    relative_info.get('年龄', ''),
                    relative_info.get('手机', ''),
                    relative_info.get('地址', ''),
                    relative_info.get('关系', '亲属'),
                    relative_info.get('链接', '')
                ])
            logging.info(f"✅ 已保存亲属: {relative_info.get('姓名', 'N/A')}, {relative_info.get('年龄', 'N/A')}岁, 手机: {relative_info.get('手机', 'N/A')}")
            return True
        except Exception as e:
            logging.error(f"保存亲属信息失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """从文件中加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                            logging.debug(f"去掉前缀1: {line.strip()} -> {phone}")
                        
                        # 验证电话号码格式（10位数字）
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def init_browser(self) -> bool:
        """初始化浏览器"""
        try:
            # 查找Chrome浏览器
            self.chrome_path = find_chrome_path()
            if not self.chrome_path:
                logging.error("无法找到Chrome浏览器")
                return False
            
            # 创建浏览器配置
            options = ChromiumOptions()
            options.set_paths(browser_path=self.chrome_path)
            
            # 极速文本加载参数配置 - 只加载文字内容
            instant_args = [
                "--no-first-run",
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-web-security",
                "--disable-extensions",
                "--disable-images",  # 禁用图片
                "--disable-javascript",  # 禁用JS（保留，因为我们需要执行点击）
                "--disable-plugins",
                "--disable-background-timer-throttling",
                "--aggressive-cache-discard",
                "--disable-background-networking",
                "--disable-sync",
                "--disable-translate",
                "--disable-ipc-flooding-protection",
                "--disable-client-side-phishing-detection",
                "--disable-component-update",
                "--disable-default-apps",
                "--disable-domain-reliability",
                "--disable-features=TranslateUI,BlinkGenPropertyTrees,VizDisplayCompositor",
                "--page-load-strategy=none",
                "--disable-blink-features=AutomationControlled",
                "--disable-infobars",
                "--disable-notifications",
                "--disable-popup-blocking",
                # 新增资源过滤参数
                "--disable-background-mode",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=VizDisplayCompositor",
                "--disable-ipc-flooding-protection",
                "--disable-default-apps",
                "--disable-sync",
                "--no-default-browser-check",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-back-forward-cache",
                "--disable-features=TranslateUI,BlinkGenPropertyTrees"
            ]

            # 设置资源过滤偏好
            prefs = {
                "profile.default_content_setting_values": {
                    "images": 2,  # 禁用图片
                    "plugins": 2,  # 禁用插件
                    "popups": 2,  # 禁用弹窗
                    "geolocation": 2,  # 禁用地理位置
                    "notifications": 2,  # 禁用通知
                    "media_stream": 2,  # 禁用媒体流
                },
                "profile.managed_default_content_settings": {
                    "images": 2
                },
                "profile.default_content_settings": {
                    "images": 2
                }
            }
            options.set_pref("prefs", prefs)
            
            for arg in instant_args:
                options.set_argument(arg)
            
            # 启动浏览器
            logging.info("正在启动浏览器...")
            self.driver = ChromiumPage(addr_or_opts=options)

            # 设置超时
            try:
                self.driver.set.timeouts(page_load=1)
                logging.info("浏览器初始化成功")
            except Exception as e:
                logging.warning(f"设置超时失败: {e}")

            # 设置资源拦截 - 只加载HTML和文本
            try:
                self.driver.set.load_mode.none()  # 不等待任何资源加载完成
                logging.info("已设置极速加载模式")
            except Exception as e:
                logging.warning(f"设置加载模式失败: {e}")

            return True
            
        except Exception as e:
            logging.error(f"初始化浏览器失败: {e}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("浏览器已关闭")
            except Exception as e:
                logging.error(f"关闭浏览器失败: {e}")
            finally:
                self.driver = None
    
    def scrape_phone_data(self, phone: str) -> Tuple[bool, Dict]:
        """
        爬取单个电话号码的背景报告数据
        
        Args:
            phone: 10位电话号码
            
        Returns:
            (成功标志, 数据字典)
        """
        start_time = time.time()
        logging.info("=" * 80)
        logging.info(f"🔍 开始处理电话号码: {phone}")
        
        try:
            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"目标URL: {target_url}")
            
            # 页面加载 - 极速加载
            if not instant_page_load(self.driver, target_url, max_wait=1):
                logging.error(f"页面加载失败: {phone}")
                return False, {}
            
            # 智能Cloudflare处理和页面验证
            try:
                page_source = self.driver.html
                page_length = len(page_source)

                cloudflare_keywords = ["cloudflare", "just a moment", "checking your browser", "please wait"]
                has_cloudflare = any(keyword in page_source.lower() for keyword in cloudflare_keywords)

                # 只有在真正检测到Cloudflare或页面内容极少时才刷新
                needs_refresh = has_cloudflare or page_length < 10000

                if needs_refresh:
                    if has_cloudflare:
                        logging.info(f"检测到Cloudflare保护，页面长度: {page_length}字符，使用CloudflareBypasser...")
                        # 使用CloudflareBypasser处理
                        cf_bypasser = CloudflareBypasser(self.driver, max_retries=3, log=True)
                        cf_bypasser.bypass()

                        # 检查绕过结果
                        final_source = self.driver.html
                        final_length = len(final_source)
                        final_has_cloudflare = any(keyword in final_source.lower() for keyword in cloudflare_keywords)

                        if not final_has_cloudflare:
                            logging.info(f"✅ CloudflareBypasser成功，页面长度: {final_length}")
                        else:
                            logging.warning(f"⚠️ CloudflareBypasser未完全成功，但继续处理，页面长度: {final_length}")
                    else:
                        logging.info(f"页面内容过少({page_length}字符)，尝试简单刷新...")
                        self.driver.refresh()
                        time.sleep(2)
                        new_source = self.driver.html
                        logging.info(f"刷新后页面长度: {len(new_source)}")
                else:
                    logging.info(f"页面状态良好，内容长度: {page_length}字符，直接处理")

            except Exception as e:
                logging.warning(f"Cloudflare检查失败: {e}")
                # 继续处理，不中断流程
            
            # 查找背景报告按钮
            button_found, button_url = find_background_report_instant(self.driver)
            
            if not button_found:
                logging.warning(f"未找到背景报告按钮: {phone}")
                return False, {}
            
            logging.info(f"找到背景报告链接: {button_url}")
            
            # 访问背景报告页面 - 极速加载
            if not instant_page_load(self.driver, button_url, max_wait=1):
                logging.error(f"背景报告页面加载失败: {phone}")
                return False, {}

            # 使用CloudflareBypasser处理背景报告页面
            try:
                bg_page_source = self.driver.html
                cloudflare_keywords = ["cloudflare", "just a moment", "checking your browser", "please wait"]
                has_cloudflare = any(keyword in bg_page_source.lower() for keyword in cloudflare_keywords)

                if has_cloudflare:
                    logging.info("背景报告页面检测到Cloudflare，使用CloudflareBypasser...")
                    # 使用CloudflareBypasser处理
                    cf_bypasser = CloudflareBypasser(self.driver, max_retries=5, log=True)
                    cf_bypasser.bypass()

                    # 检查绕过结果
                    final_source = self.driver.html
                    final_has_cloudflare = any(keyword in final_source.lower() for keyword in cloudflare_keywords)

                    if not final_has_cloudflare:
                        logging.info("✅ 背景报告页面CloudflareBypasser成功")
                    else:
                        logging.warning("⚠️ 背景报告页面Cloudflare仍然存在，但继续处理...")
                else:
                    logging.info("背景报告页面无Cloudflare，直接处理")

            except Exception as e:
                logging.warning(f"背景报告页面Cloudflare处理失败: {e}")
                # 继续处理
            
            # 解析页面内容 - 极速处理
            logging.info("等待页面完全加载...")
            time.sleep(0.3)  # 极速等待时间

            # 极速随机延迟
            random_delay = random.uniform(0.1, 0.2)
            time.sleep(random_delay)

            new_page_source = self.driver.html
            soup = BeautifulSoup(new_page_source, 'html.parser')
            
            # 提取个人信息
            person_info = extract_person_info(soup)
            if not person_info.get('姓名'):
                logging.warning(f"未提取到个人信息: {phone}")
                return False, {}

            # 立即保存个人信息
            self.save_person_to_csv(phone, person_info)

            # 提取亲属信息
            relatives_info = extract_relatives_info(soup, min_age=40)
            logging.info(f"找到 {len(relatives_info)} 个符合条件的亲属")

            # 访问亲属页面获取详细信息并实时保存
            detailed_relatives_count = 0
            for i, relative in enumerate(relatives_info):
                relative_name = relative.get('姓名', '')
                if relative_name:
                    # 智能重试机制平衡速度和完整性
                    max_retries = 2
                    success = False
                    for retry in range(max_retries):
                        try:
                            logging.info(f"处理亲属 {i+1}/{len(relatives_info)}: {relative_name} (尝试 {retry+1}/{max_retries})")

                            # 如果是重试，快速等待让页面稳定
                            if retry > 0:
                                logging.info(f"重试处理亲属 {relative_name}...")
                                time.sleep(1)

                            relative_details = click_relative_link_and_extract(self.driver, relative_name, soup)

                            combined_info = {
                                '姓名': relative.get('姓名', ''),
                                '年龄': relative.get('年龄', ''),
                                '关系': relative.get('关系', '亲属'),
                                '链接': relative.get('链接', ''),
                                '手机': relative_details.get('手机', ''),
                                '地址': relative_details.get('地址', '')
                            }

                            # 立即保存每个亲属信息
                            if self.save_relative_to_csv(phone, combined_info):
                                detailed_relatives_count += 1

                            # 检查是否获取到有效数据
                            has_contact_info = combined_info.get('手机') or combined_info.get('地址')

                            if has_contact_info:
                                # 获取到联系信息，正常延迟
                                time.sleep(0.6)
                                random_delay = random.uniform(0.2, 0.5)
                                time.sleep(random_delay)
                            else:
                                # 没有联系信息，快速处理下一个
                                time.sleep(0.3)

                            success = True
                            # 成功处理，跳出重试循环
                            break

                        except Exception as e:
                            error_msg = str(e).lower()
                            logging.error(f"处理亲属 {relative_name} 失败 (尝试 {retry+1}/{max_retries}): {e}")

                            # 智能错误判断：某些错误直接跳过，不重试
                            if any(keyword in error_msg for keyword in ['页面已被刷新', 'context lost', 'page refresh']):
                                logging.warning(f"检测到页面刷新错误，直接跳过亲属 {relative_name}")
                                break

                            if retry == max_retries - 1:
                                logging.warning(f"亲属 {relative_name} 处理失败，保存基本信息")
                                # 保存基本信息，即使没有详细联系方式
                                basic_info = {
                                    '姓名': relative.get('姓名', ''),
                                    '年龄': relative.get('年龄', ''),
                                    '关系': relative.get('关系', '亲属'),
                                    '链接': relative.get('链接', ''),
                                    '手机': '',
                                    '地址': ''
                                }
                                self.save_relative_to_csv(phone, basic_info)
                            else:
                                time.sleep(0.5)  # 减少重试前等待时间

            duration = time.time() - start_time
            logging.info(f"✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"成功保存亲属数量: {detailed_relatives_count}")

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }
                
        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"错误详情: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return False, {}
    
    def run_batch_scraping(self, max_phones: Optional[int] = None, 
                          delay_between_phones: int = 5) -> Dict:
        """
        运行批量爬取
        
        Args:
            max_phones: 最大处理电话号码数量，None表示处理所有
            delay_between_phones: 电话号码之间的延迟（秒）
            
        Returns:
            统计结果字典
        """
        self.stats['start_time'] = datetime.now()
        logging.info("🚀 开始批量爬取任务")
        
        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats
        
        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")
        
        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats
        
        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")
        
        self.stats['total_phones'] = len(unprocessed_phones)
        
        # 初始化浏览器
        if not self.init_browser():
            logging.error("浏览器初始化失败，无法继续")
            return self.stats
        
        try:
            # 处理每个电话号码
            for i, phone in enumerate(unprocessed_phones, 1):
                self.stats['current_phone'] = phone
                self.stats['processed_phones'] = i
                
                logging.info(f"\n📞 处理进度: {i}/{len(unprocessed_phones)} ({i/len(unprocessed_phones)*100:.1f}%)")
                
                try:
                    success, result_data = self.scrape_phone_data(phone)

                    if success:
                        self.stats['successful_scrapes'] += 1
                        self.save_processed_phone(phone)
                        logging.info(f"✅ 成功处理: {phone}")
                    else:
                        self.stats['failed_scrapes'] += 1
                        logging.error(f"❌ 处理失败: {phone}")
                    
                except Exception as e:
                    self.stats['failed_scrapes'] += 1
                    logging.error(f"❌ 处理电话号码 {phone} 时发生异常: {e}")
                    logging.error(f"异常堆栈: {traceback.format_exc()}")
                
                # 电话号码之间的极速延迟，模拟人的行为
                if i < len(unprocessed_phones):
                    # 极速延迟时间
                    base_delay = max(delay_between_phones, 0.2)  # 最少0.2秒
                    random_delay = random.uniform(0.1, 0.3)   # 极速随机延迟
                    total_delay = base_delay + random_delay
                    logging.info(f"⏰ 等待 {total_delay:.1f} 秒后处理下一个...")
                    time.sleep(total_delay)
                
                # 打印当前统计
                self.print_stats()
        
        except KeyboardInterrupt:
            logging.info("收到中断信号，正在优雅退出...")
        except Exception as e:
            logging.error(f"批量处理过程中发生严重错误: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
        finally:
            self.close_browser()
        
        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()
        
        logging.info("=" * 80)
        logging.info("🎯 批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        logging.info(f"成功率: {self.stats['successful_scrapes']/max(1, self.stats['processed_phones'])*100:.1f}%")
        logging.info("=" * 80)
        
        return self.stats
    
    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"耗时: {elapsed:.0f}秒")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量电话号码背景报告爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=float, default=0.5, help='电话号码之间的延迟（秒）')
    
    args = parser.parse_args()
    
    try:
        # 创建爬取器
        scraper = BatchPhoneScraper(args.phone_file, args.output_dir)
        
        # 运行批量爬取
        stats = scraper.run_batch_scraping(args.max_phones, args.delay)
        
        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()
