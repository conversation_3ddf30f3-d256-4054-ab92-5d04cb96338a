<!DOCTYPE html><html lang="en"><head><script src="https://pagead2.googlesyndication.com/pagead/managed/js/adsense/m202505290101/slotcar_library_fy2021.js"></script><script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202505290101/pubads_impl_page_level_ads.js?cb=31092794"></script><script async="" src="https://www.clarity.ms/s/0.8.9/clarity.js"></script><script async="" src="https://a.ad.gt/api/v1/u/matches/788?url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;ref="></script><script async="" src="https://cdn.hadronid.net/hadron.js?url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;ref=&amp;_it=tag&amp;partner_id=788&amp;ha=ha"></script><script src="https://cdn.aggle.net/oir/oir.min.js" async="" oirtyp="6311ae17" oirid="P44794M33"></script><script type="text/javascript" async="" src="https://static.criteo.net/js/ld/publishertag.prebid.144.js"></script><script src="https://rules.quantcount.com/rules-p-WFJsXCa9VD158.js" async=""></script><script async="" src="https://secure.cdn.fastclick.net/js/cnvr-launcher/latest/launcher.min.js"></script><script type="text/javascript" async="" src="https://static.anonymised.io/light/loader.js"></script><script type="text/javascript" async="" src="https://secure.quantserve.com/quant.js"></script><script async="" type="text/javascript" src="https://p.gcprivacy.com/t/gcid_s.min.js"></script><script src="https://cdn.hadronid.net/hadron.js?url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;ref=&amp;_it=amazon&amp;partner_id=405"></script><script type="text/javascript" async="" src="https://script.4dex.io/localstore.js"></script>
<meta charset="utf-8">
<title>(************* - Reverse Phone Search</title>
<meta name="description" content="Reverse phone search for (************* to find the owner's name, address, relatives, neighbors, emails and more.">
    
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="application-name" content="smartbackgroundchecks.com">
<meta name="msapplication-TileColor" content="#00aba9">
<meta name="msapplication-TileImage" content="/mstile-144x144.png">
<meta name="theme-color" content="#000000">
<meta name="apple-mobile-web-app-title" content="SmartBackgroundChecks">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="mobile-web-app-capable" content="yes">
<meta property="og:type" content="website">
<meta property="og:image" content="https://www.smartbackgroundchecks.com/og-image.jpg">
<meta property="og:url" content="https://www.smartbackgroundchecks.com/phone/5619324217">
<meta property="og:image:type" content="image/jpg">
<meta property="og:image:width" content="882">
<meta property="og:image:height" content="462">
<meta property="og:title" content="(************* - Reverse Phone Search">
<meta property="og:description" content="Reverse phone search for (************* to find the owner's name, address, relatives, neighbors, emails and more.">
<meta property="og:locale" content="en_US">
<meta property="og:site_name" content="SmartBackgroundchecks">
<meta property="fb:app_id" content="2246755615540353">
<meta property="article:publisher" content="https://www.smartBackgroundchecks.com"> 
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="@smartbackground">
<meta name="twitter:site:id" content="@smartbackground">
<meta name="twitter:creator" content="@smartbackground">
<meta name="twitter:title" content="(************* - Reverse Phone Search"> 
<meta name="twitter:description" content="Reverse phone search for (************* to find the owner's name, address, relatives, neighbors, emails and more.">
<meta property="twitter:image" content="https://www.smartbackgroundchecks.com/og-image.jpg">
<meta property="twitter:image:secure_url" content="https://www.smartbackgroundchecks.com/og-image.jpg">
<meta property="twitter:image:type" content="image/jpeg">
<meta property="twitter:image:width" content="882">
<meta property="twitter:image:height" content="462">
<meta name="yandex-verification" content="4a8dc585e9cd5f58">
<meta name="google-site-verification" content="r2Pi89hoaOF1dh2Fm6cdWxEzxcsTXgi6tFWY2OWpcpk">
<meta name="msvalidate.01" content="DF6BDEDDEDD61F15E512EB9BFB950913">
<meta name="miscvalidate" content="">
<meta name="format-detection" content="telephone=no">
<link rel="manifest" href="/manifest.json">
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
<link rel="preconnect" href="https://www.google.com">
<link rel="preconnect" href="https://www.googletagmanager.com">
<link rel="preconnect" href="https://www.google-analytics.com">
<link rel="preconnect" href="https://www.youtube.com">
<link rel="preconnect" href="https://s.ytimg.com">
<link rel="preconnect" href="https://www.googletagservices.com">
<link rel="preconnect" href="https://adservice.google.com">
<link rel="preconnect" href="https://securepubads.g.doubleclick.net">
<link rel="preconnect" href="https://ad.doubleclick.net">
<link rel="preconnect" href="https://pagead2.googlesyndication.com">	
	
<link rel="canonical" href="https://www.smartbackgroundchecks.com/phone/5619324217">
<link rel="alternate" hreflang="es" href="https://www.smartbackgroundchecks.com/es/phone/5619324217">

<link rel="preload" as="style" href="/vendor/bootstrap/css/optimized-bootstrap421-min.css">
<link rel="preload" as="style" href="/css/optimized-business-frontpage-min.css">
<link rel="preload" as="style" href="/css/sbc.css">
<link rel="stylesheet" href="/vendor/bootstrap/css/optimized-bootstrap421-min.css">
<link rel="stylesheet" href="/css/optimized-business-frontpage-min.css">
<link rel="stylesheet" href="/css/sbc.css">
<script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-KXJCD57"></script><script async="" type="text/javascript" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script><script type="text/javascript" async="" referrerpolicy="unsafe-url" src="https://ws.zoominfo.com/pixel/633ef9125a797886caf7797d"></script><script src="//www.youtube.com/iframe_api"></script><script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-RJBZJBFL94&amp;cx=c&amp;gtm=45He55u1v810625888za200&amp;tag_exp=101509157~102015665~103103155~103103157~103116026~103200004~103233427~103351869~103351871"></script><script src="https://pagead2.googlesyndication.com/pagead/managed/js/adsense/m202505290101/show_ads_impl_fy2021.js"></script><script async="" src="https://www.clarity.ms/tag/45wgqybilp"></script><script async="" src="//c.amazon-adsystem.com/aax2/apstag.js"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-K4TD499"></script><script>
//Google Page Layer
var dataLayer = dataLayer || [];
dataLayer.push({ 'page_type':'phoneResults' });
</script>
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-K4TD499');</script>
<script src="//client.px-cloud.net/PXDc2Zuqea/main.min.js" async=""></script>
<script type="text/javascript">
	(function(){
		var bsa_optimize=document.createElement('script');
		bsa_optimize.type='text/javascript';
		bsa_optimize.async=true;
		bsa_optimize.src='https://cdn4.buysellads.net/pub/smartbackgroundchecks.js?'+(new Date()-new Date()%600000);
		(document.getElementsByTagName('head')[0]||document.getElementsByTagName('body')[0]).appendChild(bsa_optimize);
	})();
</script><script type="text/javascript" async="" src="https://cdn4.buysellads.net/pub/smartbackgroundchecks.js?1748958600000"></script>	
<script async="" src="https://btloader.com/tag?o=5102648370397184&amp;upapi=true" dropped-by="bsaoptimize"></script><script async="" type="text/javascript" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js" dropped-by="bsaoptimize"></script><style id="bsa_extra-css"></style><style>.bsa_fixed-leaderboard {position: fixed;bottom: 0;left: 0;right: 0;display: flex;justify-content: center;align-items: center;height: 100px;width: 100%;background: rgba(0,0,0,.8);z-index: 9999;padding: 5px 0;}.bsa_fixed-leaderboard > a {display: block;position: absolute;right: 5px;top: 5px;background: rgba(255, 255, 255, .4);color: #000;border-radius: 20px;padding: 2px 8px 4px;font-family: Arial;font-size: 14px;text-decoration: none;}</style><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202505290101/pubads_impl.js?cb=31092794" async=""></script><link href="https://securepubads.g.doubleclick.net/pagead/managed/dict/m202505290101/gpt" rel="compression-dictionary"><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script src="https://config.aps.amazon-adsystem.com/configs/747b8b51-ec47-4dee-9823-b2b73124b71f" type="text/javascript" async="async"></script><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><link rel="preload" as="script" href="https://securepubads.g.doubleclick.net/tag/js/gpt.js"><link rel="preload" as="script" href="https://c.amazon-adsystem.com/aax2/apstag.js"><script src="https://config.aps.amazon-adsystem.com/configs/1ad7261b-91ea-4b6f-b9e9-b83522205b75" type="text/javascript" async="async"></script><meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><script async="" src="https://fundingchoicesmessages.google.com/i/22247219933?ers=3"></script><script src="//secure.cdn.fastclick.net/js/pubcid/latest/pubcid.min.js"></script><script src="https://tags.crwdcntrl.net/lt/c/16576/sync.min.js"></script><script src="https://cdn-ima.33across.com/ima.js"></script><script src="//cdn.id5-sync.com/api/1.0/id5-api.js"></script><script src="//secure.cdn.fastclick.net/js/pubcid/latest/pubcid.min.js"></script><script src="https://cdn-ima.33across.com/ima.js"></script><script src="//cdn.id5-sync.com/api/1.0/id5-api.js"></script><script async="" id="browsi-tag" data-sitekey="d_mapping" data-pubkey="adapex" src="https://cdn.browsiprod.com/bootstrap/bootstrap.js"></script><script type="text/javascript" async="" src="https://secure.cdn.fastclick.net/js/cnvr-launcher/latest/launcher-stub.min.js"></script><meta name="pbstck_context:pbstck_ab_test" content="true"><script type="text/javascript" async="" src="https://static.anonymised.io/light/bundle.js?v=0.3.27"></script><script src="https://p.ad.gt/api/v1/p/405" async=""></script><script src="https://www.googletagmanager.com/gtag/js?id=G-FVWZ0RM4DH&amp;l=audDataLayer" async=""></script><img src="https://ib.adnxs.com/getuid?https%3A%2F%2Fp2.gcprivacy.com%2Fv3%2Fid%2Fxandr%3Fpid%3D6CP1D%26id%3D%24UID%26gcid%3D77f6d217-44c3-4da0-a9d9-5ccffa8e2438"><script src="https://p.ad.gt/api/v1/p/788" async=""></script><script src="https://www.googletagmanager.com/gtag/js?id=G-FVWZ0RM4DH&amp;l=audDataLayer" async=""></script><script type="text/javascript">(function() { var load = function() {!function(e){"use strict";var t,i=500,n="user-agent",o="",r="function",a="undefined",s="object",c="string",u="browser",d="cpu",l="device",w="engine",p="os",m="result",f="name",b="type",h="vendor",g="version",v="architecture",y="major",k="model",T="console",S="mobile",x="tablet",E="smarttv",_="wearable",C="xr",I="embedded",A="inapp",N="brands",O="formFactors",D="fullVersionList",L="platform",P="platformVersion",R="bitness",U="sec-ch-ua",M=U+"-full-version-list",$=U+"-arch",q=U+"-"+R,H=U+"-form-factors",B=U+"-"+S,V=U+"-"+k,z=U+"-"+L,F=z+"-version",j=[N,D,S,k,L,P,v,O,R],G="Amazon",K="Apple",W="ASUS",J="BlackBerry",X="Google",Y="Huawei",Z="Lenovo",Q="Honor",ee="LG",te="Microsoft",ie="Motorola",ne="Nvidia",oe="OnePlus",re="OPPO",ae="Samsung",se="Sharp",ce="Sony",ue="Xiaomi",de="Zebra",le="Chrome",we="Chromium",pe="Chromecast",me="Firefox",fe="Opera",be="Facebook",he="Sogou",ge="Mobile ",ve=" Browser",ye="Windows",ke=typeof window!==a&&window.navigator?window.navigator:void 0,Te=ke&&ke.userAgentData?ke.userAgentData:void 0,Se=function(e,t){var i={},n=t;if(!_e(t))for(var o in n={},t)for(var r in t[o])n[r]=t[o][r].concat(n[r]?n[r]:[]);for(var a in e)i[a]=n[a]&&n[a].length%2==0?n[a].concat(e[a]):e[a];return i},xe=function(e){for(var t={},i=0;i<e.length;i++)t[e[i].toUpperCase()]=e[i];return t},Ee=function(e,t){if(typeof e===s&&e.length>0){for(var i in e)if(Ae(e[i])==Ae(t))return!0;return!1}return!!Ce(e)&&-1!==Ae(t).indexOf(Ae(e))},_e=function(e,t){for(var i in e)return/^(browser|cpu|device|engine|os)$/.test(i)||!!t&&_e(e[i])},Ce=function(e){return typeof e===c},Ie=function(e){if(e){for(var t=[],i=De(/\\?\"/g,e).split(","),n=0;n<i.length;n++)if(i[n].indexOf(";")>-1){var o=Pe(i[n]).split(";v=");t[n]={brand:o[0],version:o[1]}}else t[n]=Pe(i[n]);return t}},Ae=function(e){return Ce(e)?e.toLowerCase():e},Ne=function(e){return Ce(e)?De(/[^\d\.]/g,e).split(".")[0]:void 0},Oe=function(e){for(var t in e){var i=e[t];typeof i==s&&2==i.length?this[i[0]]=i[1]:this[i]=void 0}return this},De=function(e,t){return Ce(t)?t.replace(e,o):t},Le=function(e){return De(/\\?\"/g,e)},Pe=function(e,t){if(Ce(e))return e=De(/^\s\s*/,e),typeof t===a?e:e.substring(0,i)},Re=function(e,t){if(e&&t)for(var i,n,o,a,c,u,d=0;d<t.length&&!c;){var l=t[d],w=t[d+1];for(i=n=0;i<l.length&&!c&&l[i];)if(c=l[i++].exec(e))for(o=0;o<w.length;o++)u=c[++n],typeof(a=w[o])===s&&a.length>0?2===a.length?typeof a[1]==r?this[a[0]]=a[1].call(this,u):this[a[0]]=a[1]:3===a.length?typeof a[1]!==r||a[1].exec&&a[1].test?this[a[0]]=u?u.replace(a[1],a[2]):void 0:this[a[0]]=u?a[1].call(this,u,a[2]):void 0:4===a.length&&(this[a[0]]=u?a[3].call(this,u.replace(a[1],a[2])):void 0):this[a]=u||void 0;d+=2}},Ue=function(e,t){for(var i in t)if(typeof t[i]===s&&t[i].length>0){for(var n=0;n<t[i].length;n++)if(Ee(t[i][n],e))return"?"===i?void 0:i}else if(Ee(t[i],e))return"?"===i?void 0:i;return t.hasOwnProperty("*")?t["*"]:e},Me={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},$e={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},qe={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[f,ge+"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,g],[/opios[\/ ]+([\w\.]+)/i],[g,[f,fe+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[f,fe+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[f,fe]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[f,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[g,[f,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,g],[/quark(?:pc)?\/([-\w\.]+)/i],[g,[f,"Quark"]],[/\bddg\/([\w\.]+)/i],[g,[f,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[f,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[f,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[f,"Smart "+Z+ve]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure"+ve],g],[/\bfocus\/([\w\.]+)/i],[g,[f,me+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[f,fe+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[f,fe+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[f,"MIUI"+ve]],[/fxios\/([\w\.-]+)/i],[g,[f,ge+me]],[/\bqihoobrowser\/?([\w\.]*)/i],[g,[f,"360"]],[/\b(qq)\/([\w\.]+)/i],[[f,/(.+)/,"$1Browser"],g],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1"+ve],g],[/samsungbrowser\/([\w\.]+)/i],[g,[f,ae+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[g,[f,he+" Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[f,he+" Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[f,g],[/(lbbrowser|rekonq)/i],[f],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[g,f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,be],g,[b,A]],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat)[\/ ]([-\w\.]+)/i],[f,g,[b,A]],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[f,"GSA"],[b,A]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[f,"TikTok"],[b,A]],[/\[(linkedin)app\]/i],[f,[b,A]],[/(chromium)[\/ ]([-\w\.]+)/i],[f,g],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[f,le+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,le+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[f,"Android"+ve]],[/chrome\/([\w\.]+) mobile/i],[g,[f,ge+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,g],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[g,[f,ge+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[f,ge+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[g,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[g,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[f,g],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[f,ge+me],g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],g],[/(wolvic|librewolf)\/([\w\.]+)/i],[f,g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[f,me+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[f,[g,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[f,[g,/[^\d\.]+./,o]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[v,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[v,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[v,"arm"]],[/((ppc|powerpc)(64)?)( mac|;|\))/i],[[v,/ower/,o,Ae]],[/ sun4\w[;\)]/i],[[v,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i],[[v,Ae]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[k,[h,ae],[b,x]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[k,[h,ae],[b,S]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[k,[h,K],[b,S]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[k,[h,K],[b,x]],[/(macintosh);/i],[k,[h,K]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[k,[h,se],[b,S]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[k,[h,Q],[b,x]],[/honor([-\w ]+)[;\)]/i],[k,[h,Q],[b,S]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[k,[h,Y],[b,x]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[k,[h,Y],[b,S]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[k,/_/g," "],[h,ue],[b,x]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[k,/_/g," "],[h,ue],[b,S]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[k,[h,re],[b,S]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[k,[h,Ue,{OnePlus:["304","403","203"],"*":re}],[b,x]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[k,[h,"Vivo"],[b,S]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[k,[h,"Realme"],[b,S]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto(?! 360)[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[k,[h,ie],[b,S]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[k,[h,ie],[b,x]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[k,[h,ee],[b,x]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv|watch)\w+)/i,/\blg-?([\d\w]+) bui/i],[k,[h,ee],[b,S]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[k,[h,Z],[b,x]],[/(nokia) (t[12][01])/i],[h,k,[b,x]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[k,/_/g," "],[b,S],[h,"Nokia"]],[/(pixel (c|tablet))\b/i],[k,[h,X],[b,x]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[k,[h,X],[b,S]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[k,[h,ce],[b,S]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[k,"Xperia Tablet"],[h,ce],[b,x]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[k,[h,oe],[b,S]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[k,[h,G],[b,x]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[k,/(.+)/g,"Fire Phone $1"],[h,G],[b,S]],[/(playbook);[-\w\),; ]+(rim)/i],[k,h,[b,x]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[k,[h,J],[b,S]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[k,[h,W],[b,x]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[k,[h,W],[b,S]],[/(nexus 9)/i],[k,[h,"HTC"],[b,x]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[k,/_/g," "],[b,S]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[k,[h,"TCL"],[b,x]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[k,[h,"TCL"],[b,S]],[/(itel) ((\w+))/i],[[h,Ae],k,[b,Ue,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[k,[h,"Acer"],[b,x]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[k,[h,"Meizu"],[b,S]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[k,[h,"Ulefone"],[b,S]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[k,[h,"Energizer"],[b,S]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[k,[h,"Cat"],[b,S]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[k,[h,"Smartfren"],[b,S]],[/droid.+; (a(?:015|06[35]|142p?))/i],[k,[h,"Nothing"],[b,S]],[/(imo) (tab \w+)/i,/(infinix) (x1101b?)/i],[h,k,[b,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (hmd|imo) ([\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i,/(oppo) ?([\w ]+) bui/i],[h,k,[b,S]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[h,k,[b,x]],[/(surface duo)/i],[k,[h,te],[b,x]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[k,[h,"Fairphone"],[b,S]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[k,[h,ne],[b,x]],[/(sprint) (\w+)/i],[h,k,[b,S]],[/(kin\.[onetw]{3})/i],[[k,/\./g," "],[h,te],[b,S]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[k,[h,de],[b,x]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[k,[h,de],[b,S]],[/smart-tv.+(samsung)/i],[h,[b,E]],[/hbbtv.+maple;(\d+)/i],[[k,/^/,"SmartTV"],[h,ae],[b,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,ee],[b,E]],[/(apple) ?tv/i],[h,[k,K+" TV"],[b,E]],[/crkey.*devicetype\/chromecast/i],[[k,pe+" Third Generation"],[h,X],[b,E]],[/crkey.*devicetype\/([^/]*)/i],[[k,/^/,"Chromecast "],[h,X],[b,E]],[/fuchsia.*crkey/i],[[k,pe+" Nest Hub"],[h,X],[b,E]],[/crkey/i],[[k,pe],[h,X],[b,E]],[/droid.+aft(\w+)( bui|\))/i],[k,[h,G],[b,E]],[/(shield \w+ tv)/i],[k,[h,ne],[b,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[k,[h,se],[b,E]],[/(bravia[\w ]+)( bui|\))/i],[k,[h,ce],[b,E]],[/(mi(tv|box)-?\w+) bui/i],[k,[h,ue],[b,E]],[/Hbbtv.*(technisat) (.*);/i],[h,k,[b,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,Pe],[k,Pe],[b,E]],[/droid.+; ([\w- ]+) (?:android tv|smart[- ]?tv)/i],[k,[b,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[b,E]],[/(ouya)/i,/(nintendo) (\w+)/i],[h,k,[b,T]],[/droid.+; (shield)( bui|\))/i],[k,[h,ne],[b,T]],[/(playstation \w+)/i],[k,[h,ce],[b,T]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[k,[h,te],[b,T]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[k,[h,ae],[b,_]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[h,k,[b,_]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[k,[h,re],[b,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[k,[h,K],[b,_]],[/(opwwe\d{3})/i],[k,[h,oe],[b,_]],[/(moto 360)/i],[k,[h,ie],[b,_]],[/(smartwatch 3)/i],[k,[h,ce],[b,_]],[/(g watch r)/i],[k,[h,ee],[b,_]],[/droid.+; (wt63?0{2,3})\)/i],[k,[h,de],[b,_]],[/droid.+; (glass) \d/i],[k,[h,X],[b,C]],[/(pico) (4|neo3(?: link|pro)?)/i],[h,k,[b,C]],[/; (quest( \d| pro)?)/i],[k,[h,be],[b,C]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[b,I]],[/(aeobc)\b/i],[k,[h,G],[b,I]],[/(homepod).+mac os/i],[k,[h,K],[b,I]],[/windows iot/i],[[b,I]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[k,[b,Ue,{mobile:"Mobile",xr:"VR","*":x}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[b,x]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[b,S]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[k,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[f,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[f,g],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,g],[/ladybird\//i],[[f,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,g],[/(windows (?:phone(?: os)?|mobile|iot))[\/ ]?([\d\.\w ]*)/i],[f,[g,Ue,Me]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,Ue,Me],[f,ye]],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,"macOS"],[g,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[g,[f,pe+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[g,[f,pe+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[g,[f,pe+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[g,[f,pe+" Linux"]],[/crkey\/([\d\.]+)/i],[g,[f,pe]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,f],[/(ubuntu) ([\w\.]+) like android/i],[[f,/(.+)/,"$1 Touch"],g],[/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen|webos)\w*[-\/; ]?([\d\.]*)/i],[f,g],[/\(bb(10);/i],[g,[f,J]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[g,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[f,me+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[f,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,"Chrome OS"],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux)(?: arm\w*| x86\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,g],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,g]]},He=(t={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}},Oe.call(t.init,[[u,[f,g,y,b]],[d,[v]],[l,[b,k,h]],[w,[f,g]],[p,[f,g]]]),Oe.call(t.isIgnore,[[u,[g,y]],[w,[g]],[p,[g]]]),Oe.call(t.isIgnoreRgx,[[u,/ ?browser$/i],[p,/ ?os$/i]]),Oe.call(t.toString,[[u,[f,g]],[d,[v]],[l,[h,k]],[w,[f,g]],[p,[f,g]]]),t),Be=function(e,t){var i=He.init[t],n=He.isIgnore[t]||0,r=He.isIgnoreRgx[t]||0,s=He.toString[t]||0;function c(){Oe.call(this,i)}return c.prototype.getItem=function(){return e},c.prototype.withClientHints=function(){return Te?Te.getHighEntropyValues(j).then((function(t){return e.setCH(new Ve(t,!1)).parseCH().get()})):e.parseCH().get()},c.prototype.withFeatureCheck=function(){return e.detectFeature().get()},t!=m&&(c.prototype.is=function(e){var t=!1;for(var i in this)if(this.hasOwnProperty(i)&&!Ee(n,i)&&Ae(r?De(r,this[i]):this[i])==Ae(r?De(r,e):e)){if(t=!0,e!=a)break}else if(e==a&&t){t=!t;break}return t},c.prototype.toString=function(){var e=o;for(var t in s)typeof this[s[t]]!==a&&(e+=(e?" ":o)+this[s[t]]);return e||a}),Te||(c.prototype.then=function(e){var t=this,i=function(){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e])};i.prototype={is:c.prototype.is,toString:c.prototype.toString};var n=new i;return e(n),n}),new c};function Ve(e,t){if(e=e||{},Oe.call(this,j),t)Oe.call(this,[[N,Ie(e[U])],[D,Ie(e[M])],[S,/\?1/.test(e[B])],[k,Le(e[V])],[L,Le(e[z])],[P,Le(e[F])],[v,Le(e[$])],[O,Ie(e[H])],[R,Le(e[q])]]);else for(var i in e)this.hasOwnProperty(i)&&typeof e[i]!==a&&(this[i]=e[i])}function ze(e,t,i,n){return this.get=function(e){return e?this.data.hasOwnProperty(e)?this.data[e]:void 0:this.data},this.set=function(e,t){return this.data[e]=t,this},this.setCH=function(e){return this.uaCH=e,this},this.detectFeature=function(){if(ke&&ke.userAgent==this.ua)switch(this.itemType){case u:ke.brave&&typeof ke.brave.isBrave==r&&this.set(f,"Brave");break;case l:!this.get(b)&&Te&&Te[S]&&this.set(b,S),"Macintosh"==this.get(k)&&ke&&typeof ke.standalone!==a&&ke.maxTouchPoints&&ke.maxTouchPoints>2&&this.set(k,"iPad").set(b,x);break;case p:!this.get(f)&&Te&&Te[L]&&this.set(f,Te[L]);break;case m:var e=this.data,t=function(t){return e[t].getItem().detectFeature().get()};this.set(u,t(u)).set(d,t(d)).set(l,t(l)).set(w,t(w)).set(p,t(p))}return this},this.parseUA=function(){return this.itemType!=m&&Re.call(this.data,this.ua,this.rgxMap),this.itemType==u&&this.set(y,Ne(this.get(g))),this},this.parseCH=function(){var e=this.uaCH,t=this.rgxMap;switch(this.itemType){case u:case w:var i,n=e[D]||e[N];if(n)for(var o in n){var r=n[o].brand||n[o],a=n[o].version;this.itemType!=u||/not.a.brand/i.test(r)||i&&(!/chrom/i.test(i)||r==we)||(r=Ue(r,{Chrome:"Google Chrome",Edge:"Microsoft Edge","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome"}),this.set(f,r).set(g,a).set(y,Ne(a)),i=r),this.itemType==w&&r==we&&this.set(g,a)}break;case d:var s=e[v];s&&(s&&"64"==e[R]&&(s+="64"),Re.call(this.data,s+";",t));break;case l:if(e[S]&&this.set(b,S),e[k]&&(this.set(k,e[k]),!this.get(b)||!this.get(h))){var c={};Re.call(c,"droid 9; "+e[k]+")",t),!this.get(b)&&c.type&&this.set(b,c.type),!this.get(h)&&c.vendor&&this.set(h,c.vendor)}if(e[O]){var T;if("string"!=typeof e[O])for(var x=0;!T&&x<e[O].length;)T=Ue(e[O][x++],$e);else T=Ue(e[O],$e);this.set(b,T)}break;case p:var E=e[L];if(E){var _=e[P];E==ye&&(_=parseInt(Ne(_),10)>=13?"11":"10"),this.set(f,E).set(g,_)}this.get(f)==ye&&"Xbox"==e[k]&&this.set(f,"Xbox").set(g,void 0);break;case m:var C=this.data,I=function(t){return C[t].getItem().setCH(e).parseCH().get()};this.set(u,I(u)).set(d,I(d)).set(l,I(l)).set(w,I(w)).set(p,I(p))}return this},Oe.call(this,[["itemType",e],["ua",t],["uaCH",n],["rgxMap",i],["data",Be(this,e)]]),this}function Fe(e,t,a){if(typeof e===s?(_e(e,!0)?(typeof t===s&&(a=t),t=e):(a=e,t=void 0),e=void 0):typeof e!==c||_e(t,!0)||(a=t,t=void 0),a&&typeof a.append===r){var f={};a.forEach((function(e,t){f[t]=e})),a=f}if(!(this instanceof Fe))return new Fe(e,t,a).getResult();var b=typeof e===c?e:a&&a[n]?a[n]:ke&&ke.userAgent?ke.userAgent:o,h=new Ve(a,!0),g=t?Se(qe,t):qe,v=function(e){return e==m?function(){return new ze(e,b,g,h).set("ua",b).set(u,this.getBrowser()).set(d,this.getCPU()).set(l,this.getDevice()).set(w,this.getEngine()).set(p,this.getOS()).get()}:function(){return new ze(e,b,g[e],h).parseUA().get()}};return Oe.call(this,[["getBrowser",v(u)],["getCPU",v(d)],["getDevice",v(l)],["getEngine",v(w)],["getOS",v(p)],["getResult",v(m)],["getUA",function(){return b}],["setUA",function(e){return Ce(e)&&(b=e.length>i?Pe(e,i):e),this}]]).setUA(b),this}Fe.VERSION="2.0.2",Fe.BROWSER=xe([f,g,y,b]),Fe.CPU=xe([v]),Fe.DEVICE=xe([k,h,b,T,S,E,x,_,I]),Fe.ENGINE=Fe.OS=xe([f,g]);const je=/pbstck:debug/.test(window.location.href),Ge=!!window.localStorage.getItem("pbstck"),Ke=(e,t,...i)=>{(je||Ge)&&console[e](`[pbstckUserSessions-71fca4c] [${performance.now().toFixed(2)}] ${t}`,...i.length?i:"")},We=(e,...t)=>{Ke("warn",e,...t)},Je=(e,...t)=>{Ke("log",e,...t)},Xe=(e,...t)=>{Ke("error",e,...t)},Ye=["pbstck:","pbstck_context:"],Ze=()=>{const e=document.getElementsByTagName("meta"),t=Array.from(e).filter((e=>et(Ye,e.name))),i=new Map;t.forEach((e=>{const t=Qe(e.name);i.has(t)&&We(`Custom dim ${t} is present many times`),i.size<20?i.set(t,e.content):We(`Skipping custom dim ${t} with ${e.content}: limit of 20 keys exceeded`)}));const n=Object.assign({},...Array.from(i.entries()).map((([e,t])=>({[e]:t}))));return i.size>0&&Je("Custom dim found :",n),n},Qe=e=>e.replace(/^\w+:/,""),et=(e,t)=>e.some((e=>t.startsWith(e))),tt=()=>{const e=navigator.userAgent;return/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(e)?"tablet":/Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(e)?"mobile":"desktop"};var it,nt;!function(e){e.HISTORY_MUTATION="_pbstck_historyMutation",e.NEW_PAGE="_pbstck_pageView",e.SESSION_TRACKING_AUTHORIZED="_pbstck_sessionTrackingAuthorized",e.SESSION_TRACKING_DECLINED="_pbstck_sessionTrackingDeclined"}(it||(it={})),function(e){e.REPLACE_STATE="replaceState",e.PUSH_STATE="pushState"}(nt||(nt={}));const ot=e=>{window.history[e]=new Proxy(window.history[e],{apply(t,i,n){const o=window.location.href,r=t.apply(i,n),a=new CustomEvent(it.HISTORY_MUTATION,{detail:{referrer:o,stateObj:n[0],title:n[1],url:n[2],type:e}});return dispatchEvent(a),r}})},rt=[];for(let e=0;e<256;++e)rt.push((e+256).toString(16).slice(1));let at;const st=new Uint8Array(16);var ct={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function ut(e,t,i){if(ct.randomUUID&&!e)return ct.randomUUID();const n=(e=e||{}).random??e.rng?.()??function(){if(!at){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");at=crypto.getRandomValues.bind(crypto)}return at(st)}();if(n.length<16)throw new Error("Random bytes length must be >= 16");return n[6]=15&n[6]|64,n[8]=63&n[8]|128,function(e,t=0){return(rt[e[t+0]]+rt[e[t+1]]+rt[e[t+2]]+rt[e[t+3]]+"-"+rt[e[t+4]]+rt[e[t+5]]+"-"+rt[e[t+6]]+rt[e[t+7]]+"-"+rt[e[t+8]]+rt[e[t+9]]+"-"+rt[e[t+10]]+rt[e[t+11]]+rt[e[t+12]]+rt[e[t+13]]+rt[e[t+14]]+rt[e[t+15]]).toLowerCase()}(n)}const dt=e=>{window.__pbstck_consent=e},lt=e=>{window.__pbstck_session_tracking=e},wt=()=>window.__pbstck_consent,pt=()=>window.__pbstck_session_tracking,mt=e=>{if("string"==typeof e){const t=e.split(/:\/\/(www.)?/g);return t.length<=1?null:t[t.length-1].split("/")[0]}const t=e.hostname;return t.startsWith("www.")?t.substring(4):t};var ft;!function(e){e.DEV="dev",e.BETA="beta",e.PROD="prod"}(ft||(ft={}));class bt extends Error{message="unknown session error"}class ht extends bt{message="session init error"}class gt extends bt{message="session parse error"}class vt extends bt{message="session not found error"}class yt extends bt{message="session obsolete error"}const kt=Array(),Tt=(e,t)=>{const i=e.env===ft.PROD?"":`_${e.env}`;return`_pbstck_session_${t.tagId.substring(0,8)}${i}`},St=e=>Date.now()-e>18e5,xt=(e,t)=>{try{const n=localStorage.getItem(Tt(e,t));if(n){const e=JSON.parse(atob(n));if(i=e,kt.every((e=>e in i)))return e;throw new gt}throw new vt}catch(e){if(e instanceof bt)throw e;throw new gt}var i},Et=(e,t)=>{try{const i=xt(e,t);return i.pageCount++,localStorage.setItem(Tt(e,t),btoa(JSON.stringify(i))),i.pageCount}catch(e){if(e instanceof bt)throw e;throw new bt}},_t=(e,t)=>{const i=new URL(window.location.href),n={id:ut(),lastUpdateTimeMs:Date.now(),pageCount:0,lastUrlVisited:window.location.href,utmSource:i.searchParams.get("utm_source")||null,utmCampaign:i.searchParams.get("utm_campaign")||null,utmContent:i.searchParams.get("utm_content")||null,utmTerm:i.searchParams.get("utm_term")||null,utmMedium:i.searchParams.get("utm_medium")||null};try{localStorage.setItem(Tt(e,t),btoa(JSON.stringify(n)))}catch(e){throw new ht}},Ct=[],It=(e,t)=>{const i=Ct.map((i=>Dt(i,e,t)));if(i.length){const n=JSON.stringify(i),o=`${e.gateway}/page?${(()=>{const e=document.querySelector('meta[name="pbstck:kleanads-version"]')?.getAttribute("content")??"none",i=document.querySelector('meta[name="pbstck:config-version"]')?.getAttribute("content")??"none";return`tId=${t.tagId}&v=${e}&s=${i}&c=1`})()}`;navigator.sendBeacon&&navigator.sendBeacon(o,n)||fetch(o,{body:n,method:"POST",keepalive:!0}),Je("[page] event queue dispatched",JSON.stringify(i)),Ct.length=0}},At=(e,t,i)=>{try{const o=Ot(t,i);try{const i=xt(e,t);St(i.lastUpdateTimeMs)&&It(e,t)}catch(e){Je("[page] session was not found or invalid, adding the new page to the queue anyway")}(n=o,Ct.push(n),Je("[page] event queued",n),Ct.length)>=20&&It(e,t)}catch(e){e instanceof bt?Xe(`[page] new page : ${e.message}`):Xe("[page] unknown error",e)}var n},Nt=(e,t)=>{At(e,t),window.addEventListener(it.SESSION_TRACKING_AUTHORIZED,(i=>{Je(`[page] ${it.SESSION_TRACKING_AUTHORIZED}`,i);try{((e,t)=>{try{const i=xt(e,t);if(St(i.lastUpdateTimeMs))throw new yt;i.lastUpdateTimeMs=Date.now(),i.lastUrlVisited=window.location.href,localStorage.setItem(Tt(e,t),btoa(JSON.stringify(i)))}catch(i){if(i instanceof vt||i instanceof gt)return void _t(e,t);if(i instanceof bt)throw i;throw new bt}})(e,t),It(e,t)}catch(i){i instanceof yt&&(_t(e,t),It(e,t))}})),window.addEventListener(it.SESSION_TRACKING_DECLINED,(i=>{Je(`[page] ${it.SESSION_TRACKING_DECLINED}`,i),((e,t)=>{try{localStorage.removeItem(Tt(e,t))}catch(e){}})(e,t),It(e,t)})),window.addEventListener(it.HISTORY_MUTATION,(i=>{Je(`[page] ${it.HISTORY_MUTATION}`,i),i.detail?.referrer.href!==window.location.href&&At(e,t,i.detail?.referrer)})),window.addEventListener("popstate",(i=>{At(e,t)})),window.document.addEventListener("visibilitychange",(()=>{Je(`[page] visibility changed to ${document.visibilityState}`),"visible"!==document.visibilityState&&It(e,t)})),window.addEventListener("pagehide",(()=>{It(e,t)})),window.addEventListener("beforeunload",(()=>{It(e,t)}))},Ot=(e,t)=>{const i=new URL(window.location.href);return{...e,pageId:Lt(),pageCount:1,domain:mt(window.location)??"",href:(n=window.location,n&&n.protocol&&n.host&&n.pathname?`${n.protocol}//${n.host}${n.pathname}`:"unknown"),referrer:mt(t??document.referrer),consent:wt(),userSessionId:null,sessionTracking:pt(),utmSource:i.searchParams.get("utm_source"),utmCampaign:i.searchParams.get("utm_campaign"),utmContent:i.searchParams.get("utm_content"),utmTerm:i.searchParams.get("utm_term"),utmMedium:i.searchParams.get("utm_medium")};var n},Dt=(e,t,i)=>{try{const n=pt();return{...e,pageCount:n?Et(t,i):e.pageCount,userSessionId:n?xt(t,i).id:null,consent:wt(),sessionTracking:n,utmSource:n?xt(t,i).utmSource:e.utmSource,utmCampaign:n?xt(t,i).utmCampaign:e.utmCampaign,utmContent:n?xt(t,i).utmContent:e.utmContent,utmTerm:n?xt(t,i).utmTerm:e.utmTerm,utmMedium:n?xt(t,i).utmMedium:e.utmMedium}}catch(t){if(t instanceof vt)return e;t instanceof bt?Xe(`[session] ${t.message}`):Xe("[session] unknown error",t)}return e},Lt=()=>{const e=ut();return window.__pbstck_page_id=e,e};var Pt;!function(e){e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED",e[e.UNAVAILABLE=2]="UNAVAILABLE"}(Pt||(Pt={}));const Rt=async(e,t)=>{dt(Pt.UNAVAILABLE),lt(!1);let i=0;try{(await Mt(e))("addEventListener",2,(n=>{if(n){if("tcloaded"===n.eventStatus||"useractioncomplete"===n.eventStatus){dt($t(n));const e=qt(n)&&!t.sessionTrackingDisabled;lt(e),e?dispatchEvent(new CustomEvent(it.SESSION_TRACKING_AUTHORIZED)):dispatchEvent(new CustomEvent(it.SESSION_TRACKING_DECLINED))}}else Je(`[consent] wrong tcdata ${n}, waiting 200ms`),setTimeout((()=>{i++,100===i&&We("[consent] unable to retrieve cmp after 100 tries"),Rt(e,t)}),200)}))}catch(e){Xe("[consent] Error while loading tcf api")}},Ut=(e,t,i)=>{if(e.__tcfapi){const n=e.__tcfapi;(e=>"function"==typeof e)(e.__tcfapi)?t(n):i("__tcfapi is not a function")}else setTimeout((()=>Ut(e,t,i)),100)},Mt=e=>new Promise(((t,i)=>Ut(e,t,i))),$t=e=>e.purpose.consents&&e.purpose.consents[1]&&e.purpose.consents[2]&&e.purpose.consents[3]&&e.purpose.consents[4]&&e.purpose.consents[7]?Pt.GRANTED:Pt.DENIED,qt=e=>e.purpose.consents[1]&&e.purpose.consents[7]&&e.purpose.consents[8]?(Je("[consent] SessionTracking obtained"),!0):(Je("[consent] SessionTracking declined"),!1);var Ht,Bt,Vt,zt,Ft,jt=-1,Gt=function(e){addEventListener("pageshow",(function(t){t.persisted&&(jt=t.timeStamp,e(t))}),!0)},Kt=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},Wt=function(){var e=Kt();return e&&e.activationStart||0},Jt=function(e,t){var i=Kt(),n="navigate";return jt>=0?n="back-forward-cache":i&&(document.prerendering||Wt()>0?n="prerender":document.wasDiscarded?n="restore":i.type&&(n=i.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},Xt=function(e,t,i){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var n=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return n.observe(Object.assign({type:e,buffered:!0},i||{})),n}}catch(e){}},Yt=function(e,t,i,n){var o,r;return function(a){t.value>=0&&(a||n)&&((r=t.value-(o||0))||void 0===o)&&(o=t.value,t.delta=r,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,i),e(t))}},Zt=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},Qt=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},ei=function(e){var t=!1;return function(){t||(e(),t=!0)}},ti=-1,ii=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},ni=function(e){"hidden"===document.visibilityState&&ti>-1&&(ti="visibilitychange"===e.type?e.timeStamp:0,ri())},oi=function(){addEventListener("visibilitychange",ni,!0),addEventListener("prerenderingchange",ni,!0)},ri=function(){removeEventListener("visibilitychange",ni,!0),removeEventListener("prerenderingchange",ni,!0)},ai=function(){return ti<0&&(ti=ii(),oi(),Gt((function(){setTimeout((function(){ti=ii(),oi()}),0)}))),{get firstHiddenTime(){return ti}}},si=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},ci=[1800,3e3],ui=function(e,t){t=t||{},si((function(){var i,n=ai(),o=Jt("FCP"),r=Xt("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(r.disconnect(),e.startTime<n.firstHiddenTime&&(o.value=Math.max(e.startTime-Wt(),0),o.entries.push(e),i(!0)))}))}));r&&(i=Yt(e,o,ci,t.reportAllChanges),Gt((function(n){o=Jt("FCP"),i=Yt(e,o,ci,t.reportAllChanges),Zt((function(){o.value=performance.now()-n.timeStamp,i(!0)}))})))}))},di=[.1,.25],li=0,wi=1/0,pi=0,mi=function(e){e.forEach((function(e){e.interactionId&&(wi=Math.min(wi,e.interactionId),pi=Math.max(pi,e.interactionId),li=pi?(pi-wi)/7+1:0)}))},fi=function(){return Ht?li:performance.interactionCount||0},bi=function(){"interactionCount"in performance||Ht||(Ht=Xt("event",mi,{type:"event",buffered:!0,durationThreshold:0}))},hi=[],gi=new Map,vi=0,yi=[],ki=function(e){if(yi.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=hi[hi.length-1],i=gi.get(e.interactionId);if(i||hi.length<10||e.duration>t.latency){if(i)e.duration>i.latency?(i.entries=[e],i.latency=e.duration):e.duration===i.latency&&e.startTime===i.entries[0].startTime&&i.entries.push(e);else{var n={id:e.interactionId,latency:e.duration,entries:[e]};gi.set(n.id,n),hi.push(n)}hi.sort((function(e,t){return t.latency-e.latency})),hi.length>10&&hi.splice(10).forEach((function(e){return gi.delete(e.id)}))}}},Ti=function(e){var t=self.requestIdleCallback||self.setTimeout,i=-1;return e=ei(e),"hidden"===document.visibilityState?e():(i=t(e),Qt(e)),i},Si=[200,500],xi=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},si((function(){var i;bi();var n,o=Jt("INP"),r=function(e){Ti((function(){e.forEach(ki);var t=function(){var e=Math.min(hi.length-1,Math.floor((fi()-vi)/50));return hi[e]}();t&&t.latency!==o.value&&(o.value=t.latency,o.entries=t.entries,n())}))},a=Xt("event",r,{durationThreshold:null!==(i=t.durationThreshold)&&void 0!==i?i:40});n=Yt(e,o,Si,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),Qt((function(){r(a.takeRecords()),n(!0)})),Gt((function(){vi=fi(),hi.length=0,gi.clear(),o=Jt("INP"),n=Yt(e,o,Si,t.reportAllChanges)})))})))},Ei=[2500,4e3],_i={},Ci=[800,1800],Ii=function e(t){document.prerendering?si((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},Ai=function(e,t){t=t||{};var i=Jt("TTFB"),n=Yt(e,i,Ci,t.reportAllChanges);Ii((function(){var o=Kt();o&&(i.value=Math.max(o.responseStart-Wt(),0),i.entries=[o],n(!0),Gt((function(){i=Jt("TTFB",0),(n=Yt(e,i,Ci,t.reportAllChanges))(!0)})))}))},Ni={passive:!0,capture:!0},Oi=new Date,Di=function(e,t){Bt||(Bt=t,Vt=e,zt=new Date,Ri(removeEventListener),Li())},Li=function(){if(Vt>=0&&Vt<zt-Oi){var e={entryType:"first-input",name:Bt.type,target:Bt.target,cancelable:Bt.cancelable,startTime:Bt.timeStamp,processingStart:Bt.timeStamp+Vt};Ft.forEach((function(t){t(e)})),Ft=[]}},Pi=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var i=function(){Di(e,t),o()},n=function(){o()},o=function(){removeEventListener("pointerup",i,Ni),removeEventListener("pointercancel",n,Ni)};addEventListener("pointerup",i,Ni),addEventListener("pointercancel",n,Ni)}(t,e):Di(t,e)}},Ri=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,Pi,Ni)}))},Ui=[100,300];function Mi(e,t,i,n){const o=()=>{const n=document.querySelector('meta[name="pbstck:kleanads-version"]')?.getAttribute("content")??"none",o=document.querySelector('meta[name="pbstck:config-version"]')?.getAttribute("content")??"none";return`${e.toLocaleLowerCase()}=${t.toFixed(3)}&tId=${i.tagId}&v=${n}&s=${o}&c=1`},r=pt(),a=JSON.stringify([{...i,href:window.location.href,name:e,value:t,customFields:{...i.customFields,pageId:window.__pbstck_page_id,pageCount:String(r?xt(n,i).pageCount:1),userSessionId:r?xt(n,i).id:null,sessionTracking:String(r)}}]);navigator.sendBeacon&&navigator.sendBeacon(`${n.gateway}/web-vitals?${o()}`,a)||fetch(`${n.gateway}/web-vitals?${o()}`,{body:a,method:"POST",keepalive:!0})}const $i=(e,t)=>{!function(e,t){t=t||{},ui(ei((function(){var i,n=Jt("CLS",0),o=0,r=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=r[0],i=r[r.length-1];o&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(o+=e.value,r.push(e)):(o=e.value,r=[e])}})),o>n.value&&(n.value=o,n.entries=r,i())},s=Xt("layout-shift",a);s&&(i=Yt(e,n,di,t.reportAllChanges),Qt((function(){a(s.takeRecords()),i(!0)})),Gt((function(){o=0,n=Jt("CLS",0),i=Yt(e,n,di,t.reportAllChanges),Zt((function(){return i()}))})),setTimeout(i,0))})))}((i=>Mi("CLS",i.value,t,e))),ui((i=>Mi("FCP",i.value,t,e))),function(e,t){t=t||{},si((function(){var i,n=ai(),o=Jt("LCP"),r=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<n.firstHiddenTime&&(o.value=Math.max(e.startTime-Wt(),0),o.entries=[e],i())}))},a=Xt("largest-contentful-paint",r);if(a){i=Yt(e,o,Ei,t.reportAllChanges);var s=ei((function(){_i[o.id]||(r(a.takeRecords()),a.disconnect(),_i[o.id]=!0,i(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return Ti(s)}),{once:!0,capture:!0})})),Qt(s),Gt((function(n){o=Jt("LCP"),i=Yt(e,o,Ei,t.reportAllChanges),Zt((function(){o.value=performance.now()-n.timeStamp,_i[o.id]=!0,i(!0)}))}))}}))}((i=>Mi("LCP",i.value,t,e))),function(e,t){t=t||{},si((function(){var i,n=ai(),o=Jt("FID"),r=function(e){e.startTime<n.firstHiddenTime&&(o.value=e.processingStart-e.startTime,o.entries.push(e),i(!0))},a=function(e){e.forEach(r)},s=Xt("first-input",a);i=Yt(e,o,Ui,t.reportAllChanges),s&&(Qt(ei((function(){a(s.takeRecords()),s.disconnect()}))),Gt((function(){var n;o=Jt("FID"),i=Yt(e,o,Ui,t.reportAllChanges),Ft=[],Vt=-1,Bt=null,Ri(addEventListener),n=r,Ft.push(n),Li()})))}))}((i=>Mi("FID",i.value,t,e))),xi((i=>Mi("INP",i.value,t,e))),Ai((i=>Mi("TTFB",i.value,t,e)))};e.pubstackAutoconfig=async function(e){if(void 0===e.endpoint.gateway)return void Xe("[pbstckAutoconfig] no gateway url found in config");const t={gateway:e.endpoint.gateway,env:(i=e.endpoint.gateway,i.includes(ft.DEV)?ft.DEV:i.includes(ft.BETA)?ft.BETA:ft.PROD),sessionTrackingDisabled:e.sessionTrackingDisabled??!1};var i;try{const i=window.top||window;i.pbstck=i.pbstck||{lock:{}},i.pbstck.lock=i.pbstck.lock||{};const n=`${e.tagId}@${t.env}@user-sessions`;if(i.pbstck.lock[n])return;i.pbstck.lock[n]=!0}catch(e){Xe("[pbstckAutoconfig] error while locking the integration",e)}const n=new Fe(navigator.userAgent),o=n.getOS(),r=n.getBrowser(),a={tagId:e.tagId,scopeId:e.scopeId,country:e.country,device:tt(),browserName:r.name||"unknown",browserVersion:r.major||"unknown",osName:o.name||"unknown",osVersion:o.version||"unknown",pbstckVersion:"71fca4c",customFields:Ze()},s=new Promise((e=>{setTimeout((()=>{e()}),300)})),c=(async()=>{try{return await(navigator?.cookieDeprecationLabel?.getValue())}catch(e){Je("Error while getting cookie depreciation label",e)}})();await Promise.all([c,s]).then((e=>{const t=e[0]??"";t&&(a.customFields.cdep=t)})),a.tagId&&a.scopeId?(ot(nt.REPLACE_STATE),ot(nt.PUSH_STATE),(e=>{Rt(window,e)})(t),$i(t,a),Nt(t,a)):Xe("[pbstckAutoconfig] no tagId or scopeId found in context")}}(this.userSessions=this.userSessions||{});
;
 return this;}.bind({}); var _ = load();_.userSessions.pubstackAutoconfig({"endpoint":{"gateway":"https://intake.pbstck.com/v1/intake"},"scopeId":"bb520c1b-ea12-40be-9a16-8de34e0e8435","tagId":"621169b6-0529-464c-8712-1e799d75704d","sessionTrackingDisabled":false}); })()</script><script type="text/javascript">(function() { var load = function() {!function(e){"use strict";function t(e,t,i,s){return new(i||(i=Promise))((function(n,o){function r(e){try{d(s.next(e))}catch(e){o(e)}}function a(e){try{d(s.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(r,a)}d((s=s.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;const i=e=>void 0!==e,s=[0,0,0,0,1,0,0,0,0,0,0,0,0,0,1,59],n=e=>{if(!e)throw new Error("IllegalArgumentException");const t={_value:[108,98,39,46,7,187,1,66,98,184,33,117,98,149,197,141],_scratch:new Array(16)};function i(){let e,i;for(i=0;i<16;i++)t._scratch[i]=0;for(i=0;i<16;i++)for(let n=0;n<16-i;n++)e=t._value[15-i]*s[15-n]+(t._scratch[15-(i+n)]||0),e>255&&(i+n+1<16&&(t._scratch[15-(i+n+1)]+=e>>>8),e-=e>>>8<<8),t._scratch[15-(i+n)]=e;const n=t._scratch;t._scratch=t._value,t._value=n}return function(e){let s;if("string"==typeof e){const t=e.replace(/\r\n/g,"\n"),i=[];let n=0;for(s=0;s<t.length;s++){const e=t.charCodeAt(s);e<128?i[n++]=e:e<2048?(i[n++]=e>>6|192,i[n++]=63&e|128):(i[n++]=e>>12|224,i[n++]=e>>6&63|128,i[n++]=63&e|128)}e=i}for(s=0;s<e.length;s++)t._value[15]^=e[s],i()}(e),t._value.reduce(((e,t)=>e+("00"+t.toString(16)).slice(-2)),"")},o=(e,...t)=>{if(0===t.length||""===t.join(""))throw new Error("Failed to create hash");return n(t.join("")).substr(0,e)},r=(...e)=>{try{return o(14,...e)}catch(e){throw new Error("Failed to create an auction Id")}},a=(...e)=>{try{return o(8,...e)}catch(e){throw new Error("Failed to create a bid Id")}},d=()=>n(`${Math.random().toString(36)}${(new Date).getTime()}`);class c{constructor(e){this.subscriptions=[],this.children=[],this.processingChain=e?[...e]:[]}subscribe(e,t){this.subscriptions.push({onEvent:e,onError:t})}unsubscribe(e,t){this.subscriptions=this.subscriptions.filter((i=>!(i.onEvent===e&&i.onError===t)))}pipe(...e){const t=new c([...this.processingChain,...e]);return this.children.push(t),t}next(e){this.subscriptions.forEach((t=>{try{const i=this.processingChain.reduce(((e,t)=>{if(void 0!==e)return t(e)}),e);void 0!==i&&t.onEvent(i)}catch(e){t.onError&&t.onError(e)}})),this.children.forEach((t=>t.next(e)))}}const u=e=>{return[(t=([t])=>e.test(t),e=>{if(t(e))return e}),([,[e,...t]])=>[e,t]];var t};class l extends Error{constructor(e){super(e)}}function b(e,t){if(!Array.isArray(e))throw new l(null!=t?t:"Expected value to be an array, but received "+typeof e)}function p(e){return"number"==typeof e&&!isNaN(e)}function h(e){return"string"==typeof e}function m(e,t){if(!p(e))throw new l(null!=t?t:"Expected value to be a number, but received "+typeof e)}function v(e,t){if(null!=e&&"string"!=typeof e)throw new l("Expected value to be a string, undefined or null, but received "+typeof e)}function f(e,t){if(null!=e&&!function(e){return"boolean"==typeof e}(e))throw new l("Expected value to be a boolean, but received "+typeof e)}function g(e,t){if("string"!=typeof e)throw new l(null!=t?t:"Expected value to be a string, but received "+typeof e)}function w(e,t){if(null==e)throw new l(null!=t?t:`Expected value to be defined, but received ${e}`)}function y(e,t){if(!Array.isArray(e)||0===e.filter((e=>void 0!==e)).length)throw new l(null!=t?t:"Expected array to be not empty")}const I=e=>"object"==typeof e&&null!==e&&!Array.isArray(e);function k(e,t){if(!I(e))throw new l(null!=t?t:`Expected value to be record, but received '${typeof e}'`)}const C=(e,t)=>I(e)&&t in e;const A=[],R=new c;function S(e,t){let i=0;A.push((s=>{i>=t||(i+=1,e(s))}))}function T(e){A.forEach((t=>t({error:e})))}function x(e){var t;T({context:null!==(t=e.context)&&void 0!==t?t:{},message:e.message})}var E,j;const U=/pbstck:debug/.test(window.location.href),O=!!(null===(E=window.localStorage)||void 0===E?void 0:E.getItem)&&null!==window.localStorage.getItem("pbstck"),B=`[pbstck-${null!==(j="cfcddc4")?j:"unknown"}]`;function N(){return U||O}function V(...e){N()&&console.log(B,...e)}function q(...e){N()&&console.warn(B,...e)}function $(...e){N()&&console.error(B,...e)}const z=["39216077","6943","8456","1021524","1026508","1030155","2165149","2444258","4708965","5624990","7321515","7687385","17085479","22181265","27416722","46481815","49313688","91083230","127208727","22247219933","22815767462","22815767462,20542308","22181265,20542308","22702991301","22665197336","22022010600","21866864457","21823883819","21794835430","21734370771","21722279357"],D=e=>{var t,i,s,n,o,r,a,d,c,u;if(!(null===(t=e.pubstack)||void 0===t?void 0:t.adUnitName)&&e.ortb2Imp)for(const t of z){if(null===(o=null===(n=null===(s=null===(i=e.ortb2Imp)||void 0===i?void 0:i.ext)||void 0===s?void 0:s.data)||void 0===n?void 0:n.pbadslot)||void 0===o?void 0:o.startsWith(`/${t}/`)){const t=e.ortb2Imp.ext.data.pbadslot.replace(/\/$/,"").split("/").pop();return t||e.code}if(null===(d=null===(a=null===(r=e.ortb2Imp)||void 0===r?void 0:r.ext)||void 0===a?void 0:a.gpid)||void 0===d?void 0:d.startsWith(`/${t}/`)){const t=e.ortb2Imp.ext.gpid.replace(/\/$/,"").split("/").pop();return t||e.code}}return null!==(u=null===(c=e.pubstack)||void 0===c?void 0:c.adUnitName)&&void 0!==u?u:e.code},M=e=>{var t;if(null===(t=e.pubstack)||void 0===t?void 0:t.adUnitPath)return L(e.pubstack.adUnitPath);if(e.ortb2Imp){if(e.ortb2Imp.ext.data.pbadslot)return L(e.ortb2Imp.ext.data.pbadslot);if(e.ortb2Imp.ext.gpid)return L(e.ortb2Imp.ext.gpid)}},F=e=>{const t=[];return e.forEach((e=>{X(e).bids.forEach((e=>{t.some((t=>t.bidder===e.bidder))||t.push(e)}))})),t},_=e=>{const t={};return e.forEach((e=>{const i=X(e);void 0!==i.mediaTypes.native&&(t.native=i.mediaTypes.native),void 0!==i.mediaTypes.video&&i.mediaTypes.video.playerSize&&(t.video?t.video.playerSize=[...t.video.playerSize,...i.mediaTypes.video.playerSize]:t.video=i.mediaTypes.video),void 0!==i.mediaTypes.banner&&(t.banner?(t.banner.sizes=[...t.banner.sizes,...i.mediaTypes.banner.sizes],i.mediaTypes.banner.sizeConfig&&(t.banner.sizeConfig=i.mediaTypes.banner.sizeConfig)):t.banner=i.mediaTypes.banner)})),t},P=e=>{var t,i,s;const n=e=>"string"==typeof e?e:Array.isArray(e)&&2===e.length?`${e[0]}x${e[1]}`:"unknown",o=new Set;return(e=>{var t,i;return(void 0===(null===(t=e.mediaTypes.banner)||void 0===t?void 0:t.sizes)||0===(null===(i=e.mediaTypes.banner)||void 0===i?void 0:i.sizes.length))&&void 0===e.mediaTypes.native&&void 0===e.mediaTypes.video})(e)?[]:((null===(t=e.mediaTypes.banner)||void 0===t?void 0:t.sizes)&&(Array.isArray(e.mediaTypes.banner.sizes[0])?e.mediaTypes.banner.sizes.forEach((e=>o.add(n(e)))):o.add(n(e.mediaTypes.banner.sizes))),(null===(i=e.mediaTypes.video)||void 0===i?void 0:i.playerSize)&&(null===(s=e.mediaTypes.video)||void 0===s||s.playerSize.forEach((e=>o.add((e=>{const t=n(e);return"unknown"===t?"video":`video-${t}`})(e))))),e.mediaTypes.native&&o.add("native"),Array.from(o))},L=e=>e.startsWith("/")?e:`/${e}`,W=e=>{var t,i;const s=/^(adUnitPath)/;return(null!==(i=null===(t=e.pubstack)||void 0===t?void 0:t.tags)&&void 0!==i?i:[]).filter((e=>"string"==typeof e)).filter((e=>e.length>0&&e.length<256||s.test(e)))},G=e=>{const t=(e=>e.placementId||e.zoneId||e.siteId||void 0)(e);if(t)return`slot:${t}`},H=e=>{const t={hasUserId:"notAvailable",userIdProviderList:[]};if(0===e.length)return t;let i=!0;const s=e[0].bids[0];return e.forEach((e=>{e.bids.forEach((e=>{const n=Object.entries(e.userId||{}).flatMap((([e,t])=>{if(Array.isArray(t)){const i=t.filter((e=>Object.prototype.hasOwnProperty.call(e,"source"))).map((t=>`${e}:${t.source}`));return i.length?i:e}return e}));if(t.userIdProviderList=t.userIdProviderList.concat(n),t.userIdProviderList=t.userIdProviderList.concat(Object.keys(e.crumbs||{})),i=i&&typeof s.crumbs==typeof e.crumbs,s.crumbs&&e.crumbs){const t=Object.keys(s.crumbs),n=Object.keys(e.crumbs);i=i&&t.length===n.length&&t.every((e=>n.includes(e)))}if(i=i&&typeof s.userId==typeof e.userId,s.userId&&e.userId){const t=Object.keys(s.userId),n=Object.keys(e.userId);i=i&&t.length===n.length&&t.every((e=>n.includes(e)))}}))})),t.userIdProviderList.length>0&&i?t.hasUserId="available":t.userIdProviderList.length>0&&!i&&(t.hasUserId="notConsistent"),t.userIdProviderList=Array.from(new Set(t.userIdProviderList)),t},J=e=>{let t=e.map((e=>e.gdprConsent)).filter((e=>void 0!==e));return e.length!==t.length&&(t=[]),t},Q=e=>{const t={userConsentState:"notAvailable",userConsentVersion:"notAvailable"};try{if(0===e.length)return t;const i=e.every(((e,t,i)=>e.apiVersion===i[0].apiVersion)),s=e.every(((e,t,i)=>e.consentString===i[0].consentString));if(!i||!s)throw new Error("API version and Consent string must be unique within a bid request array");return(e=>{const t={userConsentState:"notAvailable",userConsentVersion:"notAvailable"};if(void 0===e)return t;let i=!1,s=!1;if(e.apiVersion&&1!==e.apiVersion){if(2!==e.apiVersion)throw e.apiVersion>2?new Error(`API version is not yet supported: ${e.apiVersion}`):new Error(`An issue occured while identifying TCF version: ${e.apiVersion}`);if(t.userConsentVersion="tcf-v2","boolean"==typeof e.gdprApplies&&!e.gdprApplies)return Object.assign(Object.assign({},t),{userConsentState:"notApplicable"});if((e=>{const t=e;return!!(t&&t.purpose&&t.purpose.consents&&t.vendor&&t.vendor.consents)})(e.vendorData)){const n=Object.values(e.vendorData.purpose.consents),o=Object.values(e.vendorData.vendor.consents);if(i=n.filter((e=>e)).length>0,s=o.filter((e=>e)).length>0,0===n.length||0===o.length)return Object.assign(Object.assign({},t),{userConsentState:"notAvailable"})}}else{if(t.userConsentVersion="tcf-v1","boolean"==typeof e.gdprApplies&&!e.gdprApplies)return Object.assign(Object.assign({},t),{userConsentState:"notApplicable"});if((e=>{const t=e;return!(!t||!t.purposeConsents||!t.vendorConsents)})(e.vendorData)){const n=Object.values(e.vendorData.purposeConsents),o=Object.values(e.vendorData.vendorConsents);if(i=n.filter((e=>e)).length>0,s=o.filter((e=>e)).length>0,0===n.length||0===o.length)return Object.assign(Object.assign({},t),{userConsentState:"notAvailable"})}}return t.userConsentState=i&&s?"accepted":"refused",t})(e[0])}catch(e){return e.context=e.context||{},e.context.pbjs={source:"pbjs:helpers"},x(e),t}},X=e=>JSON.parse(JSON.stringify(e));var Y,K;!function(e){e[e.LOADED=0]="LOADED",e[e.FAILED=1]="FAILED",e[e.NOT_READY=2]="NOT_READY"}(Y||(Y={})),function(e){e.RUNNING="running",e.NO_BID="noBid",e.BID="bidResponse",e.TIMEOUT="bidTimeout"}(K||(K={}));const Z=e=>e.state===K.BID,ee=e=>Z(e)?e.bidResponseId:e.bidId;var te,ie;!function(e){e[e.ON_DONE=0]="ON_DONE",e[e.ON_SMART_MERGED=1]="ON_SMART_MERGED",e[e.NEVER=2]="NEVER"}(te||(te={})),function(e){e[e.PBJS=0]="PBJS",e[e.SMART_RTB=1]="SMART_RTB",e[e.AMAZON=2]="AMAZON",e[e.GAM=3]="GAM"}(ie||(ie={}));const se=400,ne="unknown",oe=new Map;class re{constructor(e,t=!1){this.onBidResponseStream=new c,this.onAuctionEndStream=new c,this.onBidWonStream=new c,this.onBidWonFromSdkStream=new c,e&&(this.pbjsConfig=e),this.admOnboarding=t}getAdServerCurrency(){var e;return null===(e=this.pbjsConfig)||void 0===e?void 0:e.adServerCurrency}onBidResponse(e){const t=r(e.adUnitCode,e.auctionId),i=a(e.requestId),s=a(i,e.adId);oe.set(e.adId,i),this.onBidResponseStream.next({adId:e.adId,auctionId:t,dealId:e.dealId||void 0,bidId:i,bidResponseId:s,cpm:e.cpm,currency:e.currency,size:e.size,mediaType:e.mediaType,tags:[],bidderCode:e.bidderCode,customFields:{},timeToRespond:e.timeToRespond,adapterCode:e.adapterCode,advertiserDomains:e.advertiserDomains})}onAuctionEnd(e,t="prebid"){var s,n;const o=(null===(n=window[(null===(s=this.pbjsConfig)||void 0===s?void 0:s.pbjsVariableName)||"pbjs"])||void 0===n?void 0:n.aliasRegistry)||{};!function(e,t){const i=[];if(e.forEach((e=>{try{t(e)}catch(e){i.push(e)}})),0!==i.length){const e=`forEach: Unexpected (${i.length}) errors\n${i.reduce(((e,t)=>`${e}\t- ${t.message}\n`),"")}`;throw new Error(e)}}(e.adUnits.filter((t=>void 0===e.adUnitCodes||e.adUnitCodes.includes(t.code))).reduce(((e,t)=>(e.find((e=>t.code===e.code))||e.push(t),e)),[]),(s=>{var n,c,u,l;const p=function(e,t){const i=e.adUnits.filter((e=>e.code===t)),s={code:t,bids:F(i),mediaTypes:_(i)},n=(e=>{let t;return e.forEach((e=>{const i=X(e);i.pubstack&&0!=Object.keys(i.pubstack).length?t=i.pubstack:JSON.stringify(i.pubstack)!==JSON.stringify(t)&&q(`Two different pubstack declaration found for a adUnitCode ${i.code}`,i.pubstack,t)})),t})(i);n&&(s.pubstack=n);const o=(e=>{let t;return e.forEach((e=>{const i=X(e);i.ortb2Imp?t=i.ortb2Imp:JSON.stringify(i.ortb2Imp)!==JSON.stringify(t)&&q(`Two different ortb2imp declaration found for a adUnitCode ${i.code}`,i.ortb2Imp,t)})),t})(i);o&&(s.ortb2Imp=o);return s}(e,s.code),h=(e=>({code:e.code,name:D(e),path:M(e)}))(p),m=r(p.code,e.auctionId),v=e.labels||[],f=(b(g=e.bidderRequests),g.length>0&&g.every((e=>w(e.bidderRequestId))),g);var g;const y=e.bidderRequests.flatMap((t=>{var n;const c=r(s.code,e.auctionId),u=t.gdprConsent,l=t.bidderCode,b=o[l],m=null===(n=e.seatNonBids)||void 0===n?void 0:n.find((e=>e.seat===t.bidderCode)),v=null==m?void 0:m.nonbid.find((e=>e.impid===s.code)),f=t.bids.filter((e=>e.adUnitCode===s.code)).map((t=>{const s=a(t.bidId),n=e.bidsReceived.find((e=>e.requestId===t.bidId)),o=e.noBids.find((e=>e.bidId===t.bidId)),r=e.bidsRejected.find((e=>e.requestId===t.bidId));let d={state:K.TIMEOUT};if(n){const e=a(s,n.adId);d={adId:n.adId,bidResponseId:e,cpm:n.cpm,currency:n.currency,size:n.size,mediaType:n.mediaType,bidNetRevenue:n.netRevenue,state:K.BID,timeToRespond:n.timeToRespond,dealId:n.dealId||void 0,advertiserDomains:n.advertiserDomains}}else o?d={state:K.NO_BID}:r&&(d={state:K.NO_BID,rejectionReason:r.rejectionReason});return Object.assign({auctionId:c,bidId:s,gdprConsent:u,bidderCode:l,adapterCode:b,source:t.src,tags:[G(t.params)].filter(i),customFields:{},admMapping:{adUnitCode:h.code,adUnitName:h.name,adUnitPath:h.path,bidderCode:b||l,bidderParams:JSON.stringify(t.params),requestedSizes:P(p)}},d)}));return v&&f.push({bidId:a(d()),auctionId:c,gdprConsent:u,bidderCode:l,adapterCode:o[l],source:"s2s",tags:[],customFields:{source:"s2s"},state:101===v.statuscode?K.TIMEOUT:K.NO_BID}),f})),I={source:t};let k;if(this.admOnboarding){const t=window[this.pbjsConfig.pbjsVariableName],i=t.installedModules,{userSync:s,fledgeForGpt:n,floors:o,paapi:r,schain:a,realTimeData:d}=t.getConfig()||{};k={adUnitCode:h.code,fledgeForGpt:JSON.stringify(n),floors:JSON.stringify(o),installedModules:JSON.stringify(i),paapi:JSON.stringify(r),realTimeData:JSON.stringify(d),schain:JSON.stringify(a),timeout:e.timeout,userSync:JSON.stringify(s)}}const C={auctionId:m,adUnit:h,refreshIndex:0,sizes:P(p),userId:H(f),pbjsVersion:null!==(c=null===(n=this.pbjsConfig)||void 0===n?void 0:n.version)&&void 0!==c?c:ne,tags:[...W(p)],labels:v,gracePeriod:null!==(l=null===(u=this.pbjsConfig)||void 0===u?void 0:u.gracePeriod)&&void 0!==l?l:se,duration:e.auctionEnd-e.timestamp,bidRequests:y,timeout:e.timeout,customFields:I,admConfig:k};this.onAuctionEndStream.next(C)}))}onBidWon(e){var t;const i=(null===(t=window[this.pbjsConfig.pbjsVariableName])||void 0===t?void 0:t.aliasRegistry)||{},s=Object.assign(Object.assign({},e),{pbstckAdapterCode:i[e.bidderCode],bidNetRevenue:e.netRevenue,dealId:e.dealId||void 0,auctionId:r(e.adUnitCode,e.auctionId),tags:[],customFields:{source:"prebid"}});this.onBidWonStream.next(s)}onBidWonFromSdk(e){const t=Object.assign(Object.assign({},e),{customFields:{source:"sdk"}});this.onBidWonFromSdkStream.next(t)}}const ae=(e,t,i)=>{const s=e;s[i]=s[i]||[];const n=s[i];e.pbstck=e.pbstck||{},e.pbstck.sdk=e.pbstck.sdk||{},e.pbstck.sdk[t]=e.pbstck.sdk[t]||{p:[],q:n},e.pbstck.sdk[t].p=e.pbstck.sdk[t].p||[],e.pbstck.sdk[t].q=e.pbstck.sdk[t].q||n,e.pbstck.sdk[t].q!==n&&(e.pbstck.sdk[t].q=e.pbstck.sdk[t].q.concat(n));const o={cmd:(...i)=>{const s=["cmd",i];(e.pbstck.sdk[t].q||[]).push(s),(e.pbstck.sdk[t].p||[]).forEach((e=>e(s)))}};return e.Pubstack=o,o},de=e=>{const t=[];return JSON.parse(JSON.stringify(e,((e,i)=>{if("object"==typeof i&&null!==i){if(t.includes(i))return;t.push(i)}return i})))},ce={AUCTION_INIT:"auctionInit",AUCTION_END:"auctionEnd",BID_TIMEOUT:"bidTimeout",BID_REQUESTED:"bidRequested",BID_RESPONSE:"bidResponse",BID_WON:"bidWon",NO_BID:"noBid"},ue=new c;function le(e,t,i="prebid"){return{on(s,n){V(`[pbjsIntegration] pbjs.dispatcher (${i}) ${s}`,n),"sdk"===i&&function(e){const t=window.pbstck.scopeId,i=window.pbstck.tagId,s=Object.assign(Object.assign({},e),{source:"collector",type:"log",tagId:i,scopeId:t});ue.next(s)}({id:"sdk-usage",level:"info",message:"sdk usage",eventName:s}),s===ce.AUCTION_END&&t.onAuctionEnd(e.toAuctionEnd(n),i),s===ce.BID_RESPONSE&&t.onBidResponse(e.toBidResponse(n)),s===ce.BID_WON&&"prebid"===i&&t.onBidWon(e.toBidWon(n)),s===ce.BID_WON&&"sdk"===i&&t.onBidWonFromSdk(e.toBidWonFromSdk(n))}}}function be(e){let t;if(void 0!==e)if(p(e))t=e;else if(h(e)){const i=Number(e);isNaN(i)||(t=i)}return void 0!==t?Math.trunc(t):t}function pe(e){try{return b(t=e,i),t.every((e=>g(e))),e}catch(e){return}var t,i}function he(e){try{return k(e),e}catch(e){return{}}}function me(e){try{return v(e),e}catch(e){return void V("Error on validator but not throwing since not mandatory",e.message)}}function ve(){const e=e=>{var t,i;k(e,"Auction event's adUnits should all be objects"),g(e.code,'Auction event\'s adUnits should all have a key "code" as a string'),b(e.bids,'Auction event\'s adUnits should all have a key "bids" as an array');const s=e.bids.map((t=>{try{return(e=>{var t;k(e,"Auction event's adUnits bidders should all be objects"),g(e.bidder,'Auction event\'s adUnits bidders should all have a key "bidder" as a string');const i=null!==(t=e.params)&&void 0!==t?t:{};return k(i,'Auction event\'s adUnits bidders should all have a key "params" as an object'),{bidder:e.bidder,params:i}})(t)}catch(t){return void q(`[pbjsIntegration] Discarding bidder from ${e.code}`,t)}})).filter((e=>void 0!==e)),n={};if(e.mediaTypes){if(k(e.mediaTypes,'Auction event\'s adUnits should all have a key "mediaTypes" as an object'),e.mediaTypes.banner){k(e.mediaTypes.banner,'Auction event\'s adUnits mediaTypes can all have a key "banner" that should be an object');const i=null!==(t=e.mediaTypes.banner.sizes)&&void 0!==t?t:[];b(i,'Auction event\'s adUnits mediaTypes banner should all have a key "sizes" that should be an array');const s=i.filter((e=>Array.isArray(e)&&2===e.length)).map((e=>{try{return b(e),[parseInt(e[0]),parseInt(e[1])]}catch(e){return[0,0]}}));n.banner={sizes:s,sizeConfig:e.mediaTypes.banner.sizeConfig}}if(e.mediaTypes.native&&(n.native={sizes:"native"}),e.mediaTypes.video&&(k(e.mediaTypes.video,'Auction event\'s adUnits mediaTypes can all have a key "video" that should be an object'),e.mediaTypes.video.playerSize)){b(e.mediaTypes.video.playerSize,'Auction event\'s adUnits mediaTypes video should all have a key "playerSize" that should be an array');const t=(null!==(i=e.mediaTypes.video.playerSize)&&void 0!==i?i:[]).filter((e=>Array.isArray(e)&&2===e.length));n.video={playerSize:t}}}const o={bids:s,code:e.code,mediaTypes:n},r=e.pubstack;void 0!==r&&(k(r),o.pubstack=r);const a=(e=>{if(!C(e,"ortb2Imp"))return;const t=e.ortb2Imp;if(!C(t,"ext"))return;const i=t.ext;if(!C(i,"data")&&!C(i,"gpid"))return;const s=i.data;let n,o;return C(s,"pbadslot")&&h(s.pbadslot)&&(n=s.pbadslot),C(i,"gpid")&&h(i.gpid)&&(o=i.gpid),n||o?{ext:{data:{pbadslot:n},gpid:o}}:void 0})(e);return a&&(o.ortb2Imp=a),o},t=e=>{k(e,"Auction event's bidderRequests should all be objects"),g(e.bidderRequestId,'Auction event\'s bidderRequests should all have a key "bidderRequestId" as a string'),g(e.bidderCode,'Auction event\'s bidderRequests should all have a key "bidderCode" as a string'),b(e.bids,'Auction event\'s bidderRequests should all have a key "bids" as an array');const t=e.bids.map((t=>{try{return i(t,"Auction event's bidderRequests")}catch(t){return void q(`[pbjsIntegration] Discarding bid request from ${e.bidderRequestId}`,t)}})).filter((e=>void 0!==e)),s={bidderRequestId:e.bidderRequestId,bids:t,bidderCode:e.bidderCode};return e.gdprConsent&&(s.gdprConsent=e.gdprConsent),s},i=(e,t)=>{var i,s,n;k(e,t+"'s bids should all be objects"),g(e.adUnitCode,t+' bids should all have a key "adUnitCode" as a string'),g(e.bidId,t+' bids should all have a key "bidId" as a string'),g(e.bidder,t+' bids should all have a key "bidder" as a string');const o=null!==(i=e.params)&&void 0!==i?i:{};k(o,t+' bids can all have a key "params" that should be an object');const r=null!==(s=e.userId)&&void 0!==s?s:{};k(r,t+' bids can all have a key "userId" that should be an object');const a=null!==(n=e.crumbs)&&void 0!==n?n:{};return k(a,t+' bids can all have a key "crumbs" that should be an object'),v(e.src),{adUnitCode:e.adUnitCode,bidId:e.bidId,bidder:e.bidder,params:o,userId:r,crumbs:a,src:e.src}};return{toBidRejected:e=>(k(e,"BidRejected event should be an object"),g(e.requestId,'BidRequested event should have a "requestId" key as a string'),g(e.rejectionReason,'BidRejected event should have a "rejectionReason" key as a string'),{requestId:e.requestId,rejectionReason:e.rejectionReason}),toSeatNonBid:function(e){return k(e,"SeatNonBid event should be an object"),g(e.seat,'SeatNonBid event should have a "seat" key as a string'),b(e.nonbid,'SeatNonBid event should have a "seat" key as a string'),e.nonbid.map((t=>{try{return k(t,"Nonbid should be an object"),g(t.impid,'Nonbid should have a "impid" key as a string'),m(t.statuscode,'Nonbid should have a "statuscode" key as a number'),{impid:t.impid,statuscode:t.statuscode}}catch(t){return void q(`[pbjsIntegration] Discarding Nonbid from auction event ${e.auctionId}`,t)}})).filter((e=>void 0!==e)),{seat:e.seat,nonbid:e.nonbid}},toAuctionEnd:function(i){let s,n,o=[];k(i,"Auction event should be an object"),g(i.auctionId,'Auction event should have a "auctionId" key as a string'),b(i.adUnits,'Auction event should have a "adUnits" key as a non-empty array'),y(i.adUnits,'Auction event should have a "adUnits" key as a non-empty array'),void 0!==i.labels&&(b(i.labels,'Auction event can have a "labels" key that should be an array'),n=i.labels),b(i.bidderRequests,'Auction event should have a "bidderRequests" key as a non-empty array'),y(i.bidderRequests,'Auction event should have a "bidderRequests" key as a non-empty array');const r=be(i.timeout);i.timeout&&!r&&q(`[pbjsIntegration] unable to read timeout from auction event ${i.auctionId}`);const a=i.adUnits.map((t=>{try{return e(t)}catch(e){return void q(`[pbjsIntegration] Discarding adUnit from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e)),d=i.bidderRequests.map((e=>{try{return t(e)}catch(e){return void q(`[pbjsIntegration] Discarding bidderRequest from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e));b(i.bidsReceived,'Auction event should have a "bidsReceived" key as a non-empty array');const c=i.bidsReceived.map((e=>{try{return this.toBidResponse(e)}catch(e){return void q(`[pbjsIntegration] Discarding bidReceived from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e));b(i.adUnitCodes,'Auction event should have a "adUnitCodes" key as a non-empty array'),y(i.adUnitCodes,'Auction event should have a "adUnitCodes" key as a non-empty array');try{b(i.bidsRejected,'Auction event should have a "bidsRejected" key as an array'),o=i.bidsRejected.map((e=>{try{return this.toBidRejected(e)}catch(e){return void q(`[pbjsIntegration] Discarding bidRejected from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e))}catch(e){V("Error on validator but not throwing since not mandatory",e.message)}b(i.noBids,'Auction event should have a "noBids" key as an array');const u=i.noBids.map((e=>{try{return this.toNoBid(e)}catch(e){return void q(`[pbjsIntegration] Discarding noBid from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e));try{b(i.seatNonBids,'Auction event should have a "noBids" key as an array'),s=i.seatNonBids.map((e=>{try{return this.toSeatNonBid(e)}catch(e){return void q(`[pbjsIntegration] Discarding SeatNonBids from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e))}catch(e){V("Error on validator but not throwing since not mandatory",e.message)}return function(e,t,i){if(!t.includes(e))throw new l(`Expected values to be one of '${t}', but received ${e}`)}(i.auctionStatus,["completed","inProgress","started"]),m(i.auctionEnd),m(i.timestamp),{auctionId:i.auctionId,bidderRequests:d,adUnits:a,labels:n,timeout:r,auctionEnd:i.auctionEnd,auctionStatus:i.auctionStatus,noBids:u,adUnitCodes:i.adUnitCodes,bidsRejected:o,bidsReceived:c,timestamp:i.timestamp,winningBids:[],seatNonBids:s}},toAuction:function(i){let s;k(i,"Auction event should be an object"),g(i.auctionId,'Auction event should have a "auctionId" key as a string'),b(i.adUnits,'Auction event should have a "adUnits" key as a non-empty array'),y(i.adUnits,'Auction event should have a "adUnits" key as a non-empty array'),void 0!==i.labels&&(b(i.labels,'Auction event can have a "labels" key that should be an array'),s=i.labels),b(i.bidderRequests,'Auction event should have a "bidderRequests" key as a non-empty array'),y(i.bidderRequests,'Auction event should have a "bidderRequests" key as a non-empty array');const n=be(i.timeout);i.timeout&&!n&&q(`[pbjsIntegration] unable to read timeout from auction event ${i.auctionId}`);const o=i.adUnits.map((t=>{try{return e(t)}catch(e){return void q(`[pbjsIntegration] Discarding adUnit from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e)),r=i.bidderRequests.map((e=>{try{return t(e)}catch(e){return void q(`[pbjsIntegration] Discarding bidderRequest from auction event ${i.auctionId}`,e)}})).filter((e=>void 0!==e));return{auctionId:i.auctionId,bidderRequests:r,adUnits:o,labels:s,timeout:n}},toBidRequested(e){k(e,"BidRequested event should be an object"),g(e.auctionId,'BidRequested event should have a "auctionId" key as a string'),b(e.bids,'BidRequested event should have a "bids" key as an array');const t=e.bids.map((t=>{try{return i(t,"BidRequested event")}catch(t){return void q(`[pbjsIntegration] Discarding bid request from bid requested event ${e.auctionId}`,t)}})).filter((e=>void 0!==e)),s={auctionId:e.auctionId,bids:t};return e.gdprConsent&&(s.gdprConsent=e.gdprConsent),s},toBidResponse(e){var t;k(e,"BidResponse event should be an object"),g(e.auctionId,'BidRequested event should have a "auctionId" key as a string'),g(e.adUnitCode,'BidRequested event should have a "adUnitCode" key as a string'),g(e.adId,'BidRequested event should have a "adId" key as a string'),g(e.requestId,'BidRequested event should have a "requestId" key as a string');const i=h(e.cpm)?Number.parseFloat(e.cpm):e.cpm;let s;const n=me(e.dealId);m(i,'BidRequested event should have a "cpm" key as a number');let o,r=e.size;"string"!=typeof r&&(r=e.width&&e.height?`${e.width}x${e.height}`:"unknown"),g(e.mediaType,'BidRequested event should have a "mediaType" key as a string'),g(r,'BidRequested event should have a "size" key as a string'),h(e.currency)&&(o=e.currency),g(e.bidderCode,'BidResponse event should have a "bidderCode" key as a string'),function(e,t){if(null!=e&&!p(e))throw new l("Expected value to be a number, but received "+typeof e)}(e.timeToRespond),f(e.netRevenue);const a=pe(he(e.meta).advertiserDomains);try{g(e.adapterCode,'BidWon event should have a "bidderCode" key as a string'),s=e.adapterCode}catch(e){V("Error on validator but not throwing since not mandatory",e.message)}return{adId:e.adId,adUnitCode:e.adUnitCode,auctionId:e.auctionId,cpm:i,currency:o,requestId:e.requestId,size:r,bidderCode:e.bidderCode,mediaType:null!==(t=e.mediaType)&&void 0!==t?t:"banner",timeToRespond:e.timeToRespond,adapterCode:s,netRevenue:e.netRevenue,dealId:n,advertiserDomains:a}},toBidTimeout(e){b(e,"BidTimeout event should be an array");const t=[];return e.forEach((e=>{try{k(e,"BidTimeout events should all be objects"),g(e.adUnitCode,'BidTimeout events should all have a key "adUnitCode" as a string'),g(e.auctionId,'BidTimeout events should all have a key "auctionId" as a string'),g(e.bidId,'BidTimeout events should all have a key "bidId" as a string'),t.push({adUnitCode:e.adUnitCode,auctionId:e.auctionId,bidId:e.bidId})}catch(t){V("Discarding bid timeout event because ",t.message,e)}})),t},toNoBid:e=>(k(e,"NoBid event should be an object"),g(e.auctionId,'NoBid event should have a "auctionId" key as a string'),g(e.bidId,'NoBid event should have a "bidId" key as a string'),g(e.adUnitCode,'NoBid event should have a "adUnitCode" key as a string'),{bidId:e.bidId,adUnitCode:e.adUnitCode,auctionId:e.auctionId}),toBidWon(e){var t;let i,s,n,o,r,a;k(e,"BidWon event should be an object"),g(e.adId,'BidWon event should have a "adId" key as a string'),g(e.requestId,'BidWon event should have a "requestId" key as a string');const d=me(e.dealId);h(e.currency)&&(a=e.currency);const c=h(e.cpm)?Number.parseFloat(e.cpm):e.cpm;m(c,'BidRequested event should have a "cpm" key as a number'),g(e.mediaType,'BidRequested event should have a "mediaType" key as a string'),g(e.size,'BidWon event should have a "size" key as a string'),r=e.size,g(e.auctionId,'BidWon event should have a "auctionId" key as a string'),i=e.auctionId,g(e.adUnitCode,'BidWon event should have a "adUnitCode" key as a string'),s=e.adUnitCode;try{g(e.bidderCode,'BidWon event should have a "bidderCode" key as a string'),n=e.bidderCode}catch(e){V("Error on validator but not throwing since not mandatory for monitoring (only for refresh)",e.message)}try{g(e.adapterCode,'BidWon event should have a "bidderCode" key as a string'),o=e.adapterCode}catch(e){V("Error on validator but not throwing since not mandatory",e.message)}f(e.netRevenue),v(e.source);const u=pe(he(e.meta).advertiserDomains);return{adId:e.adId,adUnitCode:s,auctionId:i,bidderCode:n,adapterCode:o,size:r,requestId:e.requestId,currency:a,cpm:c,mediaType:null!==(t=e.mediaType)&&void 0!==t?t:"banner",dealId:d,netRevenue:e.netRevenue,source:e.source,advertiserDomains:u}},toBidWonFromSdk:e=>(k(e,"BidWon event should be an object"),g(e.adId,'BidWon event should have a "adId" key as a string'),{adId:e.adId})}}const fe=(e,t)=>{const i=ve(),s=new re(void 0),n=le(i,s,"sdk");t.bindIntegration(s),e.subscribe((([e,[t]])=>{try{n.on(e,t)}catch(e){e.context=e.context||{},e.context.pbjs={source:"sdk:pbjs"},x(e)}}))};function ge(e,t,i){const s=new c,n=[];ae(e,i.tagId,i.globalQueue);const o=e[i.globalQueue],r=t=>{!function(e,t){if(void 0!==e)throw new l(null!=t?t:`Expected value to be undefined, but received ${e}`)}(Object.values(e.pbstck.sdk).find((t=>t!==e.pbstck.sdk[i.tagId]&&t.q===o)),`Concurrency on '${i.globalQueue}' globalQueue (more than 1 destination configured)`),s.next([t[0],de(Object.values(t[1]))])};return s.subscribe(((...e)=>n.push(e))),fe(s.pipe(...u(/cmd/)).pipe(...u(/pbjs|prebid/)),t),{debug:()=>({events:n}),dispatchEvents:()=>{e.pbstck.sdk[i.tagId].q.forEach(r),e.pbstck.sdk[i.tagId].p.push(r)}}}const we=(e,t)=>{if(!e)throw new Error("IllegalArgumentException");return`${e}_${t}`};var ye,Ie=500,ke="user-agent",Ce="",Ae="function",Re="undefined",Se="object",Te="string",xe="browser",Ee="cpu",je="device",Ue="engine",Oe="os",Be="result",Ne="name",Ve="type",qe="vendor",$e="version",ze="architecture",De="major",Me="model",Fe="console",_e="mobile",Pe="tablet",Le="smarttv",We="wearable",Ge="xr",He="embedded",Je="inapp",Qe="brands",Xe="formFactors",Ye="fullVersionList",Ke="platform",Ze="platformVersion",et="bitness",tt="sec-ch-ua",it=tt+"-full-version-list",st=tt+"-arch",nt=tt+"-"+et,ot=tt+"-form-factors",rt=tt+"-"+_e,at=tt+"-"+Me,dt=tt+"-"+Ke,ct=dt+"-version",ut=[Qe,Ye,_e,Me,Ke,Ze,ze,Xe,et],lt="Amazon",bt="Apple",pt="ASUS",ht="BlackBerry",mt="Google",vt="Huawei",ft="Lenovo",gt="Honor",wt="LG",yt="Microsoft",It="Motorola",kt="Nvidia",Ct="OnePlus",At="OPPO",Rt="Samsung",St="Sharp",Tt="Sony",xt="Xiaomi",Et="Zebra",jt="Chrome",Ut="Chromium",Ot="Chromecast",Bt="Firefox",Nt="Opera",Vt="Facebook",qt="Sogou",$t="Mobile ",zt=" Browser",Dt="Windows",Mt=typeof window!==Re&&window.navigator?window.navigator:void 0,Ft=Mt&&Mt.userAgentData?Mt.userAgentData:void 0,_t=function(e,t){var i={},s=t;if(!Wt(t))for(var n in s={},t)for(var o in t[n])s[o]=t[n][o].concat(s[o]?s[o]:[]);for(var r in e)i[r]=s[r]&&s[r].length%2==0?s[r].concat(e[r]):e[r];return i},Pt=function(e){for(var t={},i=0;i<e.length;i++)t[e[i].toUpperCase()]=e[i];return t},Lt=function(e,t){if(typeof e===Se&&e.length>0){for(var i in e)if(Jt(e[i])==Jt(t))return!0;return!1}return!!Gt(e)&&-1!==Jt(t).indexOf(Jt(e))},Wt=function(e,t){for(var i in e)return/^(browser|cpu|device|engine|os)$/.test(i)||!!t&&Wt(e[i])},Gt=function(e){return typeof e===Te},Ht=function(e){if(e){for(var t=[],i=Yt(/\\?\"/g,e).split(","),s=0;s<i.length;s++)if(i[s].indexOf(";")>-1){var n=Zt(i[s]).split(";v=");t[s]={brand:n[0],version:n[1]}}else t[s]=Zt(i[s]);return t}},Jt=function(e){return Gt(e)?e.toLowerCase():e},Qt=function(e){return Gt(e)?Yt(/[^\d\.]/g,e).split(".")[0]:void 0},Xt=function(e){for(var t in e){var i=e[t];typeof i==Se&&2==i.length?this[i[0]]=i[1]:this[i]=void 0}return this},Yt=function(e,t){return Gt(t)?t.replace(e,Ce):t},Kt=function(e){return Yt(/\\?\"/g,e)},Zt=function(e,t){if(Gt(e))return e=Yt(/^\s\s*/,e),typeof t===Re?e:e.substring(0,Ie)},ei=function(e,t){if(e&&t)for(var i,s,n,o,r,a,d=0;d<t.length&&!r;){var c=t[d],u=t[d+1];for(i=s=0;i<c.length&&!r&&c[i];)if(r=c[i++].exec(e))for(n=0;n<u.length;n++)a=r[++s],typeof(o=u[n])===Se&&o.length>0?2===o.length?typeof o[1]==Ae?this[o[0]]=o[1].call(this,a):this[o[0]]=o[1]:3===o.length?typeof o[1]!==Ae||o[1].exec&&o[1].test?this[o[0]]=a?a.replace(o[1],o[2]):void 0:this[o[0]]=a?o[1].call(this,a,o[2]):void 0:4===o.length&&(this[o[0]]=a?o[3].call(this,a.replace(o[1],o[2])):void 0):this[o]=a||void 0;d+=2}},ti=function(e,t){for(var i in t)if(typeof t[i]===Se&&t[i].length>0){for(var s=0;s<t[i].length;s++)if(Lt(t[i][s],e))return"?"===i?void 0:i}else if(Lt(t[i],e))return"?"===i?void 0:i;return t.hasOwnProperty("*")?t["*"]:e},ii={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},si={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},ni={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[$e,[Ne,$t+"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[$e,[Ne,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[Ne,$e],[/opios[\/ ]+([\w\.]+)/i],[$e,[Ne,Nt+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[$e,[Ne,Nt+" GX"]],[/\bopr\/([\w\.]+)/i],[$e,[Ne,Nt]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[$e,[Ne,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[$e,[Ne,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[Ne,$e],[/quark(?:pc)?\/([-\w\.]+)/i],[$e,[Ne,"Quark"]],[/\bddg\/([\w\.]+)/i],[$e,[Ne,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[$e,[Ne,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[$e,[Ne,"WeChat"]],[/konqueror\/([\w\.]+)/i],[$e,[Ne,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[$e,[Ne,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[$e,[Ne,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[$e,[Ne,"Smart "+ft+zt]],[/(avast|avg)\/([\w\.]+)/i],[[Ne,/(.+)/,"$1 Secure"+zt],$e],[/\bfocus\/([\w\.]+)/i],[$e,[Ne,Bt+" Focus"]],[/\bopt\/([\w\.]+)/i],[$e,[Ne,Nt+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[$e,[Ne,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[$e,[Ne,"Dolphin"]],[/coast\/([\w\.]+)/i],[$e,[Ne,Nt+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[$e,[Ne,"MIUI"+zt]],[/fxios\/([\w\.-]+)/i],[$e,[Ne,$t+Bt]],[/\bqihoobrowser\/?([\w\.]*)/i],[$e,[Ne,"360"]],[/\b(qq)\/([\w\.]+)/i],[[Ne,/(.+)/,"$1Browser"],$e],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[Ne,/(.+)/,"$1"+zt],$e],[/samsungbrowser\/([\w\.]+)/i],[$e,[Ne,Rt+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[$e,[Ne,qt+" Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[Ne,qt+" Mobile"],$e],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[Ne,$e],[/(lbbrowser|rekonq)/i],[Ne],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[$e,Ne],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[Ne,Vt],$e,[Ve,Je]],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat)[\/ ]([-\w\.]+)/i],[Ne,$e,[Ve,Je]],[/\bgsa\/([\w\.]+) .*safari\//i],[$e,[Ne,"GSA"],[Ve,Je]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[$e,[Ne,"TikTok"],[Ve,Je]],[/\[(linkedin)app\]/i],[Ne,[Ve,Je]],[/(chromium)[\/ ]([-\w\.]+)/i],[Ne,$e],[/headlesschrome(?:\/([\w\.]+)| )/i],[$e,[Ne,jt+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[Ne,jt+" WebView"],$e],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[$e,[Ne,"Android"+zt]],[/chrome\/([\w\.]+) mobile/i],[$e,[Ne,$t+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[Ne,$e],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[$e,[Ne,$t+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[Ne,$t+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[$e,Ne],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[Ne,[$e,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[Ne,$e],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[Ne,$t+Bt],$e],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[Ne,"Netscape"],$e],[/(wolvic|librewolf)\/([\w\.]+)/i],[Ne,$e],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[$e,[Ne,Bt+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[Ne,[$e,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[Ne,[$e,/[^\d\.]+./,Ce]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[ze,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[ze,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[ze,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[ze,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[ze,"arm"]],[/((ppc|powerpc)(64)?)( mac|;|\))/i],[[ze,/ower/,Ce,Jt]],[/ sun4\w[;\)]/i],[[ze,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i],[[ze,Jt]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[Me,[qe,Rt],[Ve,Pe]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[Me,[qe,Rt],[Ve,_e]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[Me,[qe,bt],[Ve,_e]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[Me,[qe,bt],[Ve,Pe]],[/(macintosh);/i],[Me,[qe,bt]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[Me,[qe,St],[Ve,_e]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[Me,[qe,gt],[Ve,Pe]],[/honor([-\w ]+)[;\)]/i],[Me,[qe,gt],[Ve,_e]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[Me,[qe,vt],[Ve,Pe]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[Me,[qe,vt],[Ve,_e]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[Me,/_/g," "],[qe,xt],[Ve,Pe]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[Me,/_/g," "],[qe,xt],[Ve,_e]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[Me,[qe,At],[Ve,_e]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[Me,[qe,ti,{OnePlus:["304","403","203"],"*":At}],[Ve,Pe]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[Me,[qe,"BLU"],[Ve,_e]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[Me,[qe,"Vivo"],[Ve,_e]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[Me,[qe,"Realme"],[Ve,_e]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto(?! 360)[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[Me,[qe,It],[Ve,_e]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[Me,[qe,It],[Ve,Pe]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[Me,[qe,wt],[Ve,Pe]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch))(\w+)/i,/\blg-?([\d\w]+) bui/i],[Me,[qe,wt],[Ve,_e]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[Me,[qe,ft],[Ve,Pe]],[/(nokia) (t[12][01])/i],[qe,Me,[Ve,Pe]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[Me,/_/g," "],[Ve,_e],[qe,"Nokia"]],[/(pixel (c|tablet))\b/i],[Me,[qe,mt],[Ve,Pe]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[Me,[qe,mt],[Ve,_e]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[Me,[qe,Tt],[Ve,_e]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[Me,"Xperia Tablet"],[qe,Tt],[Ve,Pe]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[Me,[qe,Ct],[Ve,_e]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[Me,[qe,lt],[Ve,Pe]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[Me,/(.+)/g,"Fire Phone $1"],[qe,lt],[Ve,_e]],[/(playbook);[-\w\),; ]+(rim)/i],[Me,qe,[Ve,Pe]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[Me,[qe,ht],[Ve,_e]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[Me,[qe,pt],[Ve,Pe]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[Me,[qe,pt],[Ve,_e]],[/(nexus 9)/i],[Me,[qe,"HTC"],[Ve,Pe]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[qe,[Me,/_/g," "],[Ve,_e]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[Me,[qe,"TCL"],[Ve,Pe]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[Me,[qe,"TCL"],[Ve,_e]],[/(itel) ((\w+))/i],[[qe,Jt],Me,[Ve,ti,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[Me,[qe,"Acer"],[Ve,Pe]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[Me,[qe,"Meizu"],[Ve,_e]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[Me,[qe,"Ulefone"],[Ve,_e]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[Me,[qe,"Energizer"],[Ve,_e]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[Me,[qe,"Cat"],[Ve,_e]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[Me,[qe,"Smartfren"],[Ve,_e]],[/droid.+; (a(?:015|06[35]|142p?))/i],[Me,[qe,"Nothing"],[Ve,_e]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[Me,[qe,"Archos"],[Ve,Pe]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[Me,[qe,"Archos"],[Ve,_e]],[/(imo) (tab \w+)/i,/(infinix) (x1101b?)/i],[qe,Me,[Ve,Pe]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i,/(oppo) ?([\w ]+) bui/i],[qe,Me,[Ve,_e]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[qe,Me,[Ve,Pe]],[/(surface duo)/i],[Me,[qe,yt],[Ve,Pe]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[Me,[qe,"Fairphone"],[Ve,_e]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[Me,[qe,kt],[Ve,Pe]],[/(sprint) (\w+)/i],[qe,Me,[Ve,_e]],[/(kin\.[onetw]{3})/i],[[Me,/\./g," "],[qe,yt],[Ve,_e]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[Me,[qe,Et],[Ve,Pe]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[Me,[qe,Et],[Ve,_e]],[/smart-tv.+(samsung)/i],[qe,[Ve,Le]],[/hbbtv.+maple;(\d+)/i],[[Me,/^/,"SmartTV"],[qe,Rt],[Ve,Le]],[/tcast.+(lg)e?. ([-\w]+)/i],[qe,Me,[Ve,Le]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[qe,wt],[Ve,Le]],[/(apple) ?tv/i],[qe,[Me,bt+" TV"],[Ve,Le]],[/crkey.*devicetype\/chromecast/i],[[Me,Ot+" Third Generation"],[qe,mt],[Ve,Le]],[/crkey.*devicetype\/([^/]*)/i],[[Me,/^/,"Chromecast "],[qe,mt],[Ve,Le]],[/fuchsia.*crkey/i],[[Me,Ot+" Nest Hub"],[qe,mt],[Ve,Le]],[/crkey/i],[[Me,Ot],[qe,mt],[Ve,Le]],[/(portaltv)/i],[Me,[qe,Vt],[Ve,Le]],[/droid.+aft(\w+)( bui|\))/i],[Me,[qe,lt],[Ve,Le]],[/(shield \w+ tv)/i],[Me,[qe,kt],[Ve,Le]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[Me,[qe,St],[Ve,Le]],[/(bravia[\w ]+)( bui|\))/i],[Me,[qe,Tt],[Ve,Le]],[/(mi(tv|box)-?\w+) bui/i],[Me,[qe,xt],[Ve,Le]],[/Hbbtv.*(technisat) (.*);/i],[qe,Me,[Ve,Le]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[qe,Zt],[Me,Zt],[Ve,Le]],[/droid.+; ([\w- ]+) (?:android tv|smart[- ]?tv)/i],[Me,[Ve,Le]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[Ve,Le]],[/(ouya)/i,/(nintendo) (\w+)/i],[qe,Me,[Ve,Fe]],[/droid.+; (shield)( bui|\))/i],[Me,[qe,kt],[Ve,Fe]],[/(playstation \w+)/i],[Me,[qe,Tt],[Ve,Fe]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[Me,[qe,yt],[Ve,Fe]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[Me,[qe,Rt],[Ve,We]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[qe,Me,[Ve,We]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[Me,[qe,At],[Ve,We]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[Me,[qe,bt],[Ve,We]],[/(opwwe\d{3})/i],[Me,[qe,Ct],[Ve,We]],[/(moto 360)/i],[Me,[qe,It],[Ve,We]],[/(smartwatch 3)/i],[Me,[qe,Tt],[Ve,We]],[/(g watch r)/i],[Me,[qe,wt],[Ve,We]],[/droid.+; (wt63?0{2,3})\)/i],[Me,[qe,Et],[Ve,We]],[/droid.+; (glass) \d/i],[Me,[qe,mt],[Ve,Ge]],[/(pico) (4|neo3(?: link|pro)?)/i],[qe,Me,[Ve,Ge]],[/(quest( \d| pro)?s?).+vr/i],[Me,[qe,Vt],[Ve,Ge]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[qe,[Ve,He]],[/(aeobc)\b/i],[Me,[qe,lt],[Ve,He]],[/(homepod).+mac os/i],[Me,[qe,bt],[Ve,He]],[/windows iot/i],[[Ve,He]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[Me,[Ve,ti,{mobile:"Mobile",xr:"VR","*":Pe}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[Ve,Pe]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[Ve,_e]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[Me,[qe,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[$e,[Ne,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[Ne,$e],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[$e,[Ne,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[Ne,$e],[/ladybird\//i],[[Ne,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[$e,Ne]],os:[[/microsoft (windows) (vista|xp)/i],[Ne,$e],[/(windows (?:phone(?: os)?|mobile|iot))[\/ ]?([\d\.\w ]*)/i],[Ne,[$e,ti,ii]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[$e,ti,ii],[Ne,Dt]],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[$e,/_/g,"."],[Ne,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[Ne,"macOS"],[$e,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[$e,[Ne,Ot+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[$e,[Ne,Ot+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[$e,[Ne,Ot+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[$e,[Ne,Ot+" Linux"]],[/crkey\/([\d\.]+)/i],[$e,[Ne,Ot]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[$e,Ne],[/(ubuntu) ([\w\.]+) like android/i],[[Ne,/(.+)/,"$1 Touch"],$e],[/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen|webos)\w*[-\/\.; ]?([\d\.]*)/i],[Ne,$e],[/\(bb(10);/i],[$e,[Ne,ht]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[$e,[Ne,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[$e,[Ne,Bt+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[$e,[Ne,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[$e,[Ne,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[Ne,"Chrome OS"],$e],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux)(?: arm\w*| x86\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[Ne,$e],[/(sunos) ?([\w\.\d]*)/i],[[Ne,"Solaris"],$e],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[Ne,$e]]},oi=(ye={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}},Xt.call(ye.init,[[xe,[Ne,$e,De,Ve]],[Ee,[ze]],[je,[Ve,Me,qe]],[Ue,[Ne,$e]],[Oe,[Ne,$e]]]),Xt.call(ye.isIgnore,[[xe,[$e,De]],[Ue,[$e]],[Oe,[$e]]]),Xt.call(ye.isIgnoreRgx,[[xe,/ ?browser$/i],[Oe,/ ?os$/i]]),Xt.call(ye.toString,[[xe,[Ne,$e]],[Ee,[ze]],[je,[qe,Me]],[Ue,[Ne,$e]],[Oe,[Ne,$e]]]),ye),ri=function(e,t){var i=oi.init[t],s=oi.isIgnore[t]||0,n=oi.isIgnoreRgx[t]||0,o=oi.toString[t]||0;function r(){Xt.call(this,i)}return r.prototype.getItem=function(){return e},r.prototype.withClientHints=function(){return Ft?Ft.getHighEntropyValues(ut).then((function(t){return e.setCH(new ai(t,!1)).parseCH().get()})):e.parseCH().get()},r.prototype.withFeatureCheck=function(){return e.detectFeature().get()},t!=Be&&(r.prototype.is=function(e){var t=!1;for(var i in this)if(this.hasOwnProperty(i)&&!Lt(s,i)&&Jt(n?Yt(n,this[i]):this[i])==Jt(n?Yt(n,e):e)){if(t=!0,e!=Re)break}else if(e==Re&&t){t=!t;break}return t},r.prototype.toString=function(){var e=Ce;for(var t in o)typeof this[o[t]]!==Re&&(e+=(e?" ":Ce)+this[o[t]]);return e||Re}),Ft||(r.prototype.then=function(e){var t=this,i=function(){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e])};i.prototype={is:r.prototype.is,toString:r.prototype.toString};var s=new i;return e(s),s}),new r};function ai(e,t){if(e=e||{},Xt.call(this,ut),t)Xt.call(this,[[Qe,Ht(e[tt])],[Ye,Ht(e[it])],[_e,/\?1/.test(e[rt])],[Me,Kt(e[at])],[Ke,Kt(e[dt])],[Ze,Kt(e[ct])],[ze,Kt(e[st])],[Xe,Ht(e[ot])],[et,Kt(e[nt])]]);else for(var i in e)this.hasOwnProperty(i)&&typeof e[i]!==Re&&(this[i]=e[i])}function di(e,t,i,s){return this.get=function(e){return e?this.data.hasOwnProperty(e)?this.data[e]:void 0:this.data},this.set=function(e,t){return this.data[e]=t,this},this.setCH=function(e){return this.uaCH=e,this},this.detectFeature=function(){if(Mt&&Mt.userAgent==this.ua)switch(this.itemType){case xe:Mt.brave&&typeof Mt.brave.isBrave==Ae&&this.set(Ne,"Brave");break;case je:!this.get(Ve)&&Ft&&Ft[_e]&&this.set(Ve,_e),"Macintosh"==this.get(Me)&&Mt&&typeof Mt.standalone!==Re&&Mt.maxTouchPoints&&Mt.maxTouchPoints>2&&this.set(Me,"iPad").set(Ve,Pe);break;case Oe:!this.get(Ne)&&Ft&&Ft[Ke]&&this.set(Ne,Ft[Ke]);break;case Be:var e=this.data,t=function(t){return e[t].getItem().detectFeature().get()};this.set(xe,t(xe)).set(Ee,t(Ee)).set(je,t(je)).set(Ue,t(Ue)).set(Oe,t(Oe))}return this},this.parseUA=function(){return this.itemType!=Be&&ei.call(this.data,this.ua,this.rgxMap),this.itemType==xe&&this.set(De,Qt(this.get($e))),this},this.parseCH=function(){var e=this.uaCH,t=this.rgxMap;switch(this.itemType){case xe:case Ue:var i,s=e[Ye]||e[Qe];if(s)for(var n in s){var o=s[n].brand||s[n],r=s[n].version;this.itemType!=xe||/not.a.brand/i.test(o)||i&&(!/chrom/i.test(i)||o==Ut)||(o=ti(o,{Chrome:"Google Chrome",Edge:"Microsoft Edge","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"}),this.set(Ne,o).set($e,r).set(De,Qt(r)),i=o),this.itemType==Ue&&o==Ut&&this.set($e,r)}break;case Ee:var a=e[ze];a&&(a&&"64"==e[et]&&(a+="64"),ei.call(this.data,a+";",t));break;case je:if(e[_e]&&this.set(Ve,_e),e[Me]&&(this.set(Me,e[Me]),!this.get(Ve)||!this.get(qe))){var d={};ei.call(d,"droid 9; "+e[Me]+")",t),!this.get(Ve)&&d.type&&this.set(Ve,d.type),!this.get(qe)&&d.vendor&&this.set(qe,d.vendor)}if(e[Xe]){var c;if("string"!=typeof e[Xe])for(var u=0;!c&&u<e[Xe].length;)c=ti(e[Xe][u++],si);else c=ti(e[Xe],si);this.set(Ve,c)}break;case Oe:var l=e[Ke];if(l){var b=e[Ze];l==Dt&&(b=parseInt(Qt(b),10)>=13?"11":"10"),this.set(Ne,l).set($e,b)}this.get(Ne)==Dt&&"Xbox"==e[Me]&&this.set(Ne,"Xbox").set($e,void 0);break;case Be:var p=this.data,h=function(t){return p[t].getItem().setCH(e).parseCH().get()};this.set(xe,h(xe)).set(Ee,h(Ee)).set(je,h(je)).set(Ue,h(Ue)).set(Oe,h(Oe))}return this},Xt.call(this,[["itemType",e],["ua",t],["uaCH",s],["rgxMap",i],["data",ri(this,e)]]),this}function ci(e,t,i){if(typeof e===Se?(Wt(e,!0)?(typeof t===Se&&(i=t),t=e):(i=e,t=void 0),e=void 0):typeof e!==Te||Wt(t,!0)||(i=t,t=void 0),i&&typeof i.append===Ae){var s={};i.forEach((function(e,t){s[t]=e})),i=s}if(!(this instanceof ci))return new ci(e,t,i).getResult();var n=typeof e===Te?e:i&&i[ke]?i[ke]:Mt&&Mt.userAgent?Mt.userAgent:Ce,o=new ai(i,!0),r=t?_t(ni,t):ni,a=function(e){return e==Be?function(){return new di(e,n,r,o).set("ua",n).set(xe,this.getBrowser()).set(Ee,this.getCPU()).set(je,this.getDevice()).set(Ue,this.getEngine()).set(Oe,this.getOS()).get()}:function(){return new di(e,n,r[e],o).parseUA().get()}};return Xt.call(this,[["getBrowser",a(xe)],["getCPU",a(Ee)],["getDevice",a(je)],["getEngine",a(Ue)],["getOS",a(Oe)],["getResult",a(Be)],["getUA",function(){return n}],["setUA",function(e){return Gt(e)&&(n=e.length>Ie?Zt(e,Ie):e),this}]]).setUA(n),this}ci.VERSION="2.0.3",ci.BROWSER=Pt([Ne,$e,De,Ve]),ci.CPU=Pt([ze]),ci.DEVICE=Pt([Me,qe,Ve,Fe,_e,Le,Pe,We,He]),ci.ENGINE=ci.OS=Pt([Ne,$e]);class ui{constructor(){this.onAdStream=new c}onAd(e){const t=`/${e.formatId}`,i=e.formatId,s={bidderCode:"smart-rtb+",cpm:e.cpm,size:e.size,adUnitName:i,adUnitPathSuffix:t,formatId:e.formatId,customFields:{}};this.onAdStream.next(s)}}function li(e){if(e.includes("pubstackRefresh")){const t=e.find((e=>e.startsWith("pubstackRefreshRank")));if(void 0!==t&&t.includes(":")){const e=parseInt(t.split(":")[1])||0;return e>0?e:0}}return 0}function bi(e,t){const i=function(e){const t=e.split("?")[1];if(void 0!==t){const e=t.split("=");return{key:e[0],value:e[1]}}return}(e),s=e.split("?")[0].startsWith("/")?e.split("?")[0]:`/${e.split("?")[0]}`,n=function(e){return e.getAdUnitPath().replace("//","/")}(t);return s===(n.startsWith("/")?n:`/${n}`)&&(void 0===i||t.getTargeting(i.key)[0]===i.value)}const pi=(e,t)=>{const i=e.path;if(void 0===t||void 0===i)return;const s=t.pubads().getSlots();if(void 0===s)return;const n=s.filter((e=>bi(i,e)));switch(n.length){case 0:return;case 1:return n[0];default:if(-1!==i.indexOf("?"))return V("[pubstackGoogleTag] retrieve first slot matching the  dimension",i),n[0];{const s=t.pubads();try{!function(e){if("object"!=typeof e||null===e||!("getSlotIdMap"in e)||"function"!=typeof e.getSlotIdMap)throw new Error("Missing property getSlotIdMap on googletag")}(s);const t=s.getSlotIdMap();V("[pubstackGoogleTag] get all slot map",t);return t[Object.keys(t).filter((e=>e.startsWith(i)))[function(e){const t=Array.from(document.querySelectorAll(`div[id*='${e.name}']`)).map((e=>e.id));return t.findIndex((t=>t===e.code))}(e)]]}catch(e){return void V(`[pubstackGoogleTag] ${e}`)}}}};const hi=e=>{const t=e;if(void 0!==t&&t.apiReady&&void 0!==t.cmd&&void 0!==t.pubads&&"function"==typeof t.pubads){if("function"==typeof t.pubads().refresh)return t}};const mi=e=>{const t=(e=>hi(e.googletag))(window);V("[pubstackFindElementById] adUnit ",e);const i=pi(e,t);return V("[pubstackFindElementById] slot ",i),i?document.getElementById(i.getSlotElementId()):vi(e.code)},vi=e=>{const t=document.getElementById(e);return null===t?document.querySelector(`iframe[id*='${e}']`):t};function fi(e){const t=document.getElementsByTagName("meta");return Array.from(t).filter((t=>t.name.includes(`${e}:`)))}function gi(e,t){return e.replace(`${t}:`,"")}const wi=(e,t)=>{const i=new Set;return e.tags.forEach((e=>i.add(e))),t.tags.forEach((e=>i.add(e))),i};class yi{constructor(e){this.coreAuctionStream=new c,this.coreImpressionStream=new c,this.state=e}subscribe(e){this.coreAuctionStream.subscribe(e.onAuction),this.coreImpressionStream.subscribe(e.onImpression)}pushNewImpression(e){var t,i,s,n,o;const r=this.state.getAuction(e.auctionId),a=null!==(t=this.state.findLastAuctionId(r.adUnit))&&void 0!==t?t:"",d=this.state.findBidsByAuctionId(r.auctionId).filter((e=>e.state===K.BID)).map((e=>e)).sort(((e,t)=>t.cpm-e.cpm)),c=(null!==(s=null===(i=d[0])||void 0===i?void 0:i.cpm)&&void 0!==s?s:0)-(null!==(o=null===(n=d[1])||void 0===n?void 0:n.cpm)&&void 0!==o?o:0);return this.state.storeCoreBidResponses.set(e.bidId,e),this.impressionFormatAndForward(r,e,c,a)}pushNewAuction(e){var t,i;e.bidRequests=(t=e.bidRequests,i="adThink",t.filter((e=>e.bidderCode!==i))),0!==e.bidRequests.length&&this.coreAuctionStream.next(e)}checkMeasurability(e){return"IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype&&"isIntersecting"in window.IntersectionObserverEntry.prototype&&!!mi(e)}impressionSasFormatAndForward(e,t){const i={bidId:"smart-"+d(),auctionId:"smart-"+d(),lastAuctionId:"smart-"+d(),adUnit:t,bidderCode:e.bidderCode,cpm:e.cpm,currency:e.currency,refresh:!1,size:e.size,userConsentState:"notAvailable",userConsentVersion:"notAvailable",hasUserId:"notAvailable",userIdProviderList:[],pbjsVersion:"smart-ad-server",tags:new Set,viewabilityMeasurable:!1,cpmUplift:0,pubstackRefresh:!1,pubstackRefreshRank:0,customFields:e.customFields};this.coreImpressionStream.next(i)}impressionFormatAndForward(e,t,i,s){const n=this.state.findBidsByAuctionId(e.auctionId),o=this.state.getAuction(s),r=J(n),a=this.checkMeasurability(e.adUnit),{userConsentState:d,userConsentVersion:c}=Q(r),u={bidId:t.bidResponseId,auctionId:e.auctionId,lastAuctionId:s,adUnit:e.adUnit,bidderCode:t.bidderCode,cpm:t.cpm,currency:t.currency,refresh:!1,size:t.size,userConsentState:d,userConsentVersion:c,hasUserId:e.hasUserId,userIdProviderList:e.userIdProviderList,pbjsVersion:e.pbjsVersion,tags:wi(e,t),viewabilityMeasurable:a,cpmUplift:i,dealId:t.dealId,advertiserDomains:t.advertiserDomains,pubstackRefresh:o.pubstackRefresh,pubstackRefreshRank:o.pubstackRefreshRank,customFields:t.customFields,bidNetRevenue:t.bidNetRevenue,source:t.source,adapterCode:t.adapterCode};this.coreImpressionStream.next(u)}}class Ii{constructor(){this.storeAuctions=new Map,this.storeCoreBidResponses=new Map,this.mappingAdUnitNameAuctions=new Map,this.mappingAdUnitCodeLastAuctions=new Map}setAuction(e){var t;this.storeAuctions.set(e.auctionId,e);const i=null!==(t=this.mappingAdUnitNameAuctions.get(e.adUnit.name))&&void 0!==t?t:[];i.find((t=>t===e.auctionId))||(i.push(e.auctionId),this.mappingAdUnitNameAuctions.set(e.adUnit.name,i)),this.mappingAdUnitCodeLastAuctions.set(e.adUnit.code,e.auctionId)}getAuction(e){const t=this.storeAuctions.get(e);return w(t,`auction not found, @auctionId=${e}`),t}findBidsByAuctionId(e){var t;return(null===(t=this.storeAuctions.get(e))||void 0===t?void 0:t.bidRequests)||[]}findLastAuctionId(e){return this.mappingAdUnitCodeLastAuctions.get(e.code)}findAuctionByAdUnitPath(e){return Array.from(this.storeAuctions.values()).find((t=>{var i;return null===(i=t.adUnit.path)||void 0===i?void 0:i.endsWith(e)}))}}class ki{constructor(){this.state=new Ii,this.forwarder=new yi(this.state),this.fallbackCurrency=void 0}bindIntegration(e){e instanceof re&&(e.onBidResponseStream.subscribe((e=>this.bidResponse(e)),x),e.onAuctionEndStream.subscribe((e=>this.auctionDone(e)),x),e.onBidWonStream.subscribe((e=>this.impression(e)),x),e.onBidWonFromSdkStream.subscribe((e=>this.impressionFromSdk(e)),x),this.fallbackCurrency=e.getAdServerCurrency()),e instanceof ui&&e.onAdStream.subscribe((e=>this.impressionSas(e)),x)}helperToBidResponse(e,t){var i;const s=Object.assign({},e);s.state=K.BID,t.tags.forEach((e=>s.tags.add(e)));let n=t.size;return"native"===t.mediaType&&(n="native"),"video"===t.mediaType&&(n=`video-${n}`),s.size=n,s.cpm=t.cpm,s.currency=null!==(i=t.currency)&&void 0!==i?i:this.fallbackCurrency,s.bidResponseId=t.bidResponseId,s.bidderCode=t.bidderCode,s}bidResponse(e){V("[pubstackCoreController] onBidResponse",e),e.bidderCode="nexx360"===e.adapterCode?"nexx360":e.bidderCode;const t={auctionId:e.auctionId,state:K.BID,tags:new Set(e.tags),customFields:e.customFields};try{const i=this.state.getAuction(e.auctionId);if(i){const s=i.bidRequests.find((t=>t.bidId===e.bidId));s&&(i.bidRequests=i.bidRequests.filter((t=>t.bidId!==e.bidId)),i.bidRequests.push(Object.assign(Object.assign(Object.assign({},s),this.helperToBidResponse(t,e)),{tags:s.tags})))}}catch(e){}}helperAuctionBidToBidResponse(e){var t;const i=Object.assign(Object.assign({},e),{tags:new Set});if(e.state===K.BID){let s=e.size;"native"===e.mediaType&&(s="native"),"video"===e.mediaType&&(s=`video-${s}`),i.size=s,i.cpm=e.cpm,i.currency=null!==(t=e.currency)&&void 0!==t?t:this.fallbackCurrency,i.bidResponseId=e.bidResponseId,i.timeToRespond=e.timeToRespond,i.dealId=e.dealId,i.advertiserDomains=e.advertiserDomains,i.bidNetRevenue=e.bidNetRevenue,i.admMapping=e.admMapping}return e.tags.forEach((e=>i.tags.add(e))),i}bidWonToCoreBidResponse(e){var t,i;const s=a(e.requestId);let n=e.size;return"native"===e.mediaType&&(n="native"),"video"===e.mediaType&&(n=`video-${n}`),{adId:e.adId,bidId:s,bidResponseId:a(s,e.adId),bidderCode:"nexx360"===e.adapterCode?"nexx360":null!==(t=e.bidderCode)&&void 0!==t?t:"",adapterCode:e.pbstckAdapterCode,cpm:e.cpm,size:n,state:K.BID,auctionId:e.auctionId,tags:new Set(e.tags),currency:null!==(i=e.currency)&&void 0!==i?i:this.fallbackCurrency,customFields:e.customFields,dealId:e.dealId,advertiserDomains:e.advertiserDomains,bidNetRevenue:e.bidNetRevenue,source:e.source}}auctionDone(e){V("[pubstackCoreController] onAuctionDone",e.auctionId);try{const t=(e.bidRequests||[]).map(this.helperAuctionBidToBidResponse),i=J(t),{userConsentState:s,userConsentVersion:n}=Q(i),o={auctionId:e.auctionId,adUnit:e.adUnit,tags:new Set(e.tags),sizes:new Set(e.sizes),hasUserId:e.userId.hasUserId,userIdProviderList:e.userId.userIdProviderList,refreshIndex:e.refreshIndex,pbjsVersion:e.pbjsVersion,refresh:!1,pubstackRefresh:e.labels.includes("pubstackRefresh"),pubstackRefreshRank:li(e.labels),userConsentState:s,userConsentVersion:n,bidRequests:t,customFields:e.customFields,duration:e.duration,timeout:e.timeout,state:"RUNNING",admConfig:e.admConfig};this.state.setAuction(o);const r=()=>{const t=this.state.getAuction(e.auctionId);"FINISHED"!==t.state?(this.forwarder.pushNewAuction(t),t.state="FINISHED",this.state.setAuction(t)):V("[pubstackCoreController] auction is already finished",t)};void 0===e.gracePeriod?r():setTimeout((()=>r()),e.gracePeriod)}catch(e){V("[pubstackCoreController] error: cannot set auction as done because auction is not running")}}findBidResponseDuplicate(e){const t=this.state.storeCoreBidResponses.get(e.bidId),i=!!t&&t.bidResponseId===e.bidResponseId&&t.bidderCode===e.bidderCode;return i&&V("[pubstackCoreController] duplicate bid response found",e),i}_impression(e){if("FINISHED"===this.state.getAuction(e.auctionId).state)this.findBidResponseDuplicate(e)||this.forwarder.pushNewImpression(e);else{const t=i=>{i.auctionId===e.auctionId&&(this.findBidResponseDuplicate(e)||this.forwarder.pushNewImpression(e),this.forwarder.coreAuctionStream.unsubscribe(t))};this.forwarder.coreAuctionStream.subscribe(t)}}impression(e){V("[pubstackCoreController] onImpression",e);try{const t=this.bidWonToCoreBidResponse(e);this._impression(t)}catch(e){V("[pubstackCoreController] error: cannot create impression",e)}}impressionFromSdk(e){V("[pubstackCoreController] onImpression",e);try{const t=Array.from(this.state.storeAuctions.values()).find((t=>t.bidRequests.filter((e=>e.state===K.BID)).find((t=>t.adId===e.adId))));if(t){const i=t.bidRequests.filter((e=>e.state===K.BID)).find((t=>t.adId===e.adId));i.customFields=Object.assign(Object.assign({},i.customFields),e.customFields),this._impression(i)}}catch(e){V("[pubstackCoreController] error: cannot create impression",e)}}impressionSas(e){V("[pubstackCoreController] onImpressionSas",e);try{const t=this.state.findAuctionByAdUnitPath(e.adUnitPathSuffix);w(t,`onSasNewBidResponse: cannot retrieve related auction, @adUnitName=${e.adUnitName}, @adUnitPath=${e.adUnitPathSuffix}`),e.currency=this.fallbackCurrency,this.forwarder.impressionSasFormatAndForward(e,t.adUnit)}catch(e){V("[pubstackCoreController] error: cannot create impression",e)}}subscribe(e){this.forwarder.subscribe(e)}debug(){const e=[];return this.forwarder.subscribe({onAuction:t=>e.push(t),onImpression:t=>e.push(t)}),{auctions:this.state,auctionsDone:void 0,adUnits:void 0,events:e}}}const Ci=(e,t)=>Object.entries(t).every((([t,i])=>typeof i==typeof{}&&typeof e[t]==typeof{}?Ci(e[t],i):typeof e[t]==typeof i)),Ai=(e,t)=>{if(t)for(let i=0;i<1e3;i+=1)try{const s=e([],{},[i]);try{if(Ci(s,t))return s}catch(e){}}catch(e){}},Ri=400;const Si={CALL:"call",AD_CALLBACK:"pbstck:ad"};const Ti=()=>({toAd(e,t){k(e),w(t,"toAd: id is undefined"),w(e.formatId,"toAd: formatId is undefined"),g(t),function(e,t,i){if(!C(e,t))throw new l(`Expected object to have key '${t}', but not found`)}(e,"formatId");const i="string"==typeof e.size?e.size:"unknown";return{cpm:p(e.cpm)?e.cpm:0,size:i,formatId:p(e.formatId)?e.formatId.toString():e.formatId}}});const xi=new WeakSet;function Ei(e,t,i){const s=e[i.globalName];if(void 0===s||!s.__smartLoaded)return{status:Y.NOT_READY};const n=Ti(),o=new ui,r=function(e,t){return{on(i,s,n){V("sas.dispatcher",i,s),i===Si.AD_CALLBACK&&t.onAd(e.toAd(s,n))}}}(n,o);t.bindIntegration(o);const a=[];if(xi.has(s))return{status:Y.LOADED};xi.add(s);const d=Object.values(Si);return d.forEach((e=>{s.events.on(e,((t,i)=>{const s=de(t);a.push({eventName:e,data:s,id:i});try{r.on(e,s,i)}catch(e){e.context=e.context||{},e.context.adapter={source:"sas:on"},x(e)}}))})),s.events.history().filter((({eventName:e})=>d.includes(e))).map(de).forEach((({eventName:e,data:t,id:i})=>{const s=de(t);a.push({eventName:e,data:s,id:i});try{r.on(e,t,i)}catch(e){e.context=e.context||{},e.context.adapter={source:"sas:replayed"},x(e)}})),{status:Y.LOADED,instance:{debug:()=>({events:a})}}}function ji(e,t){return"object"==typeof t&&t instanceof Set?Array.from(t):t}function Ui(e,t){return"tags"!==e&&"sizes"!==e||!Array.isArray(t)?t:new Set(t)}class Oi{constructor(e){this.coreEvents=[],this.errors=[],e.forwarder.coreAuctionStream.subscribe((e=>this.addEvent(e))),e.forwarder.coreImpressionStream.subscribe((e=>this.addEvent(e)))}addEvent(e){this.coreEvents.push(e)}addError(e){this.errors.push(e)}getEvents(){return this.coreEvents.map((e=>JSON.parse(JSON.stringify(e,ji),Ui)))}getErrors(){return this.errors}}const Bi=e=>{var t;const i=null!==(t=null==e?void 0:e.host)&&void 0!==t?t:"unknown";return i.startsWith("www.")?i.substring(4):i},Ni=e=>{let t;return t=e&&e.protocol&&e.host&&e.pathname?`${e.protocol}//${e.host}${e.pathname}`:"unknown",t};class Vi{constructor(e,t,i){this.url=e,this.context=i,this.sender=t}buildUrl(e){return`${this.url}?sId=${this.context.scopeId.substring(0,8)}&tId=${this.context.tagId}&c=${e}&ctr=${this.context.country}`}send(e){const t=e.map((e=>qi(e,this.context)));this.sender(this.buildUrl(t.length),t)}}const qi=(e,t)=>Object.assign(Object.assign(Object.assign({},e),t),{domain:Bi(window.location),href:Ni(window.location)});function $i(e){var t;const i="pbstck",s="pbstck_context",n=[...fi(i),...fi(s)],o=n.find((e=>"pbstck_ab_test"===gi(e.name,i)));if(o)return o.content;{let i=null===(t=n.find((e=>"pbstck_ab_test"===gi(e.name,s))))||void 0===t?void 0:t.content;return i&&!e.includes(i)&&(i=void 0),i}}const zi=20;function Di(){const e="pbstck",t=new Map;fi(e).forEach((i=>{const s=gi(i.name,e);t.has(s)&&q(`Custom dim ${s} is present many times`),t.size<zi?t.set(s,i.content):q(`Skipping custom dim ${s} with ${i.content}: limit of ${zi} keys exceeded`)}));const i=Object.assign({},...Array.from(t.entries()).map((([e,t])=>({[e]:t}))));return t.size>0&&V("Custom dim found :",i),i}class Mi{constructor(e,t,i,s,n){var o;this.items=[],this.url=e,this.buffer=null!==(o=null==n?void 0:n.buffer)&&void 0!==o?o:Mi.defaults.buffer,this.sender=t,this.context=i,this.abTestValues=s}buildUrl(){const e=this.context.customFields["kleanads-version"],t=document.querySelector('meta[name="pbstck:config-version"]'),i=null==t?void 0:t.content,s=this.items.reduce(((e,t)=>e+(t.pubstackRefresh?1:0)),0),n=e?`&v=${e}&s=${i}`:"",o=s>0?`&rc=${s}`:"";return`${this.url}?tId=${this.context.tagId}&c=${this.items.length}${n}${o}`}batchThenSend(e,t=!0){const i=t?Fi(e,this.context,this.abTestValues):e;if(this.items.push(i),0===this.buffer)return this.flush();1===this.items.length&&setTimeout((()=>this.flush()),this.buffer)}batchThenSendAdmMapping(e){if(this.items.push(Object.assign(Object.assign({},e),{scope:this.context.scopeId,tagId:this.context.tagId,device:this.context.device,pbstckVersion:this.context.pbstckVersion})),0===this.buffer)return this.flush(!1);1===this.items.length&&setTimeout((()=>this.flush(!1)),this.buffer)}flush(e=!0){0!==this.items.length&&(this.sender(e?this.buildUrl():this.url,[...this.items]),this.reset())}reset(){this.items=[]}}Mi.defaults={buffer:150};const Fi=(e,t,i)=>{var s;const n="utm_source",o="utm_medium",{customFields:r}=e,{customFields:a}=t,d=Di(),c=null===(s=navigator.connection)||void 0===s?void 0:s.effectiveType,u=Object.assign(Object.assign(Object.assign(Object.assign({},r),a),d),{windowWidth:window.innerWidth.toString(),windowHeight:window.innerHeight.toString()}),l=new URLSearchParams(window.location.search);return l.get(n)&&(u[n]=l.get(n)),l.get(o)&&(u[o]=l.get(o)),Object.assign(Object.assign(Object.assign({},e),t),{customFields:u,abTestPopulation:$i(null!=i?i:[]),domain:Bi(window.location),href:Ni(window.location),networkConnectionEffectiveType:c,pageId:window.__pbstck_page_id||"unknown",kleanAdsStackVersion:u["config-version"],kleanAdsStackId:u["kleanads-stack-id"]})},_i=d();class Pi{constructor(e,t,i,s,n=!1,o){this.admOnboarding=n;const r=e.slice(0,-7),a=e.slice(0,-7);this.admMappingGateway=new Mi(`${a}/adm-mapping`,Li,t),this.admConfigGateway=new Mi(`${a}/adm-config`,Li,t),this.viewabilityGateway=new Mi(`${e}/viewability`,Li,t,o),this.auctionGateway=new Mi(`${e}/auction`,Li,t,o),this.impressionGateway=new Mi(`${e}/impression`,Li,t,o),this.errorGateway=new Mi(`${e}/error`,Li,t,o),this.traceGateway=new Mi(`${r}/trace`,Li,t,void 0,{buffer:5e3}),this.measuredImpressionGateway=new Mi(`${e}/measured`,Li,t,o),this.measuredImpressionBeaconGateway=new Vi(`${e}/measured`,Wi,t),this.pageGateway=new Mi(`${e}/page`,Li,t,o),this.bindController(i,s)}bindController(e,t){e.forwarder.coreAuctionStream.subscribe((e=>this.formatAndForwardAuction(e))),e.forwarder.coreImpressionStream.subscribe((e=>{this.formatAndForwardImpression(e)})),void 0!==t&&(t.viewabilityStream.subscribe((e=>{this.formatAndForwardViewability(e)})),t.viewedStream.subscribe((e=>{this.formatAndForwardMeasuredImpression(e)})),t.onUnload((e=>this.formatAndForwardMeasuredImpressionForBeacon(e))))}formatAndForwardAuction(e){const t=[];e.bidRequests.forEach((e=>{t.push({bidId:ee(e),bidderCode:e.bidderCode,state:e.state,source:e.source,tags:0===e.tags.size?void 0:Array.from(e.tags),cpm:Z(e)?e.cpm:void 0,currency:Z(e)?e.currency:void 0,size:Z(e)?e.size:void 0,customFields:e.customFields,timeToRespond:e.timeToRespond,rejectionReason:e.rejectionReason,dealId:Z(e)?e.dealId:void 0,advertiserDomains:Z(e)?e.advertiserDomains:void 0,bidNetRevenue:Z(e)?e.bidNetRevenue:void 0,adapterCode:e.adapterCode})}));const i=void 0===e.userConsentState?"notAvailable":e.userConsentState,s=void 0===e.userConsentVersion?"notAvailable":e.userConsentVersion,n={auctionId:e.auctionId,adUnit:e.adUnit.name,adUnitPath:e.adUnit.path,sizes:Array.from(e.sizes),tags:0===e.tags.size?void 0:Array.from(e.tags),refresh:e.refresh,userConsentState:i,userConsentVersion:s,hasUserId:e.hasUserId,userIdProviderList:e.userIdProviderList,pbjsVersion:e.pbjsVersion,bidRequests:t,pubstackManaged:!1,pubstackRefresh:e.pubstackRefresh,pubstackRefreshRank:e.pubstackRefreshRank,customFields:e.customFields,duration:e.duration,timeout:e.timeout};this.auctionGateway.batchThenSend(n),this.admOnboarding&&(e.bidRequests.filter((e=>e.admMapping)).forEach((e=>this.admMappingGateway.batchThenSendAdmMapping(e.admMapping))),this.admConfigGateway.batchThenSendAdmMapping(e.admConfig))}formatAndForwardImpression(e){const t={bidId:e.bidId,auctionId:e.auctionId,lastAuctionId:e.lastAuctionId,adUnit:e.adUnit.name,adUnitPath:e.adUnit.path,bidderCode:e.bidderCode,cpm:e.cpm,currency:e.currency,refresh:e.refresh,size:e.size,userConsentState:e.userConsentState,userConsentVersion:e.userConsentVersion,hasUserId:e.hasUserId,userIdProviderList:e.userIdProviderList,pbjsVersion:e.pbjsVersion,cpmUplift:e.cpmUplift,dealId:e.dealId,advertiserDomains:e.advertiserDomains,tags:Array.from(e.tags),viewabilityMeasurable:e.viewabilityMeasurable,pubstackManaged:!1,pubstackRefresh:e.pubstackRefresh,pubstackRefreshRank:e.pubstackRefreshRank,customFields:e.customFields,bidNetRevenue:e.bidNetRevenue,source:e.source,adapterCode:e.adapterCode};this.impressionGateway.batchThenSend(t)}formatAndForwardViewability(e){const t={bidId:e.bidId,auctionId:e.auctionId,lastAuctionId:e.lastAuctionId,adUnit:e.adUnit.name,adUnitPath:e.adUnit.path,bidderCode:e.bidderCode,pbjsVersion:e.pbjsVersion,cpm:e.cpm,currency:e.currency,size:e.size,refresh:e.refresh,htmlElementId:e.htmlElementId,mrcViewable:!0,pubstackManaged:!1,pubstackRefresh:e.pubstackRefresh,pubstackRefreshRank:e.pubstackRefreshRank};this.viewabilityGateway.batchThenSend(t)}formatAndForwardMeasuredImpression(e){const t={bidId:e.bidId,auctionId:e.auctionId,lastAuctionId:e.lastAuctionId,adUnit:e.adUnit.name,adUnitPath:e.adUnit.path,bidderCode:e.bidderCode,cpm:e.cpm,currency:e.currency,refresh:e.refresh,size:e.size,pbjsVersion:e.pbjsVersion,viewedTime:e.viewedTime,pubstackManaged:!1,pubstackRefresh:e.pubstackRefresh,pubstackRefreshRank:e.pubstackRefreshRank};this.measuredImpressionGateway.batchThenSend(t)}formatAndForwardMeasuredImpressionForBeacon(e){const t=e.map((e=>({bidId:e.bidId,auctionId:e.auctionId,lastAuctionId:e.lastAuctionId,adUnit:e.adUnit.name,adUnitPath:e.adUnit.path,bidderCode:e.bidderCode,cpm:e.cpm,currency:e.currency,refresh:e.refresh,size:e.size,pbjsVersion:e.pbjsVersion,viewedTime:e.viewedTime,pubstackManaged:!1,pubstackRefresh:e.pubstackRefresh,pubstackRefreshRank:e.pubstackRefreshRank})));this.measuredImpressionBeaconGateway.send(t)}sendError(e){this.errorGateway.batchThenSend(e)}sendLog(e){this.traceGateway.batchThenSend(e,!1)}sendToDatadog(e){var t;if(void 0===e.error||""===e.error)return;const i=e.error,s=null!==(t=e.context)&&void 0!==t?t:{};k(s),g(i);const n=Object.assign(Object.assign({pageId:_i,status:"error",domain:Bi(window.location),href:Ni(window.location)},s),{message:i}),o=new XMLHttpRequest;o.open("POST","https://browser-http-intake.logs.datadoghq.com/v1/input/pub551f730416e5317842afc2792691e95c?ddsource=browser&ddtags=version:1.3.2",!0),o.setRequestHeader("Content-Type","text/plain"),o.send(JSON.stringify(n))}}const Li=(e,t)=>{const i=new XMLHttpRequest;i.open("POST",e,!0),i.setRequestHeader("Content-Type","text/plain"),i.send(JSON.stringify(t)),V("post",e,t)},Wi=(e,t)=>{const i=JSON.stringify(t);navigator.sendBeacon(e,i),V("beacon",e,t)};class Gi{constructor(e){this.adUnit=e}visibilityRatioFromIntersection(e){const t=Hi(this.adUnit,this.adUnit);if(this.adUnit===t)return e.intersectionRatio;const i=t.getBoundingClientRect();return e.intersectionRect.height/i.height}}const Hi=(e,t)=>(Ji(e)<Ji(t)&&(e=t),Array.from(t.children).filter((e=>e instanceof HTMLElement)).forEach((t=>{e=Hi(e,t)})),e),Ji=e=>e.getBoundingClientRect?e.getBoundingClientRect().height:0;class Qi{constructor(){this.state="new",this.elapsedTime=0,this.timeTargets=[]}start(){return"stopped"===this.state&&(this.elapsedTime=0),"started"===this.state?this.elapsed():(this.state="started",this.timeoutId=setTimeout((()=>this.update()),Qi.pacing),this.elapsedTime)}pause(){if("paused"===this.state||"stopped"===this.state)return this.elapsedTime;const e=this.update();return this.state="paused",e}stop(){if("stopped"===this.state)return this.elapsedTime;const e=this.update();return this.state="stopped",e}elapsed(){return"started"===this.state&&this.update(),this.elapsedTime}timeTargetReached(e){return new Promise((t=>{this.timeTargets.push([e,t])}))}update(){let e=Qi.pacing;if("started"===this.state){this.elapsedTime+=e;for(let t=this.timeTargets.length;t--;){const[i,s]=this.timeTargets[t];this.elapsedTime>=i?(s(i),this.timeTargets.splice(t,1)):e=Math.min(e,i-this.elapsedTime)}}return"stopped"!==this.state&&(this.timeoutId&&clearTimeout(this.timeoutId),this.timeoutId=setTimeout((()=>this.update()),e)),this.elapsedTime}}Qi.pacing=100;class Xi{constructor(e,t,i,s,n){this.creative=s,this.timer=new Qi,this.inViewPercentage=e,this.cumulative=i,this.timer.timeTargetReached(t).then((()=>{n()}))}pauseTimer(){this.timer.pause()}startTimer(){this.timer.start()}stopTimer(){this.timer.stop()}getElapsed(){return this.timer.elapsed()}isViewable(){return this.inView}intersectionChange(e){this.creative.visibilityRatioFromIntersection(e)>=this.inViewPercentage?this.inView||(this.timer.start(),this.inView=!0):this.inView&&(this.cumulative?this.timer.pause():this.timer.stop(),this.inView=!1)}getTimerState(){return this.timer.state}}const Yi={root:null,rootMargin:"0px",threshold:[0,.3,.5,1]};class Ki{constructor(e,t,i){this.windowActive=!0,this.pbstckWindow=i,this.visibilityState=i.document.visibilityState,this.trackedOnFocusChange=this.onFocusChange.bind(this),i.addEventListener("focus",this.trackedOnFocusChange),i.addEventListener("blur",this.trackedOnFocusChange),this.trackedOnVisibilityChange=this.onVisibilityChange.bind(this),i.addEventListener("visibilitychange",this.trackedOnVisibilityChange);const s=this.getObserverThresholds(t);this.observer=new IntersectionObserver((e=>this.intersectionObserverCallback(e)),s),this.observer.observe(e);const n=new Gi(e);this.computer=new Xi(t.minPercentageInView,t.minTimeInView,t.cumulativeTimer,n,(()=>t.completionCallback(e.id))),"hidden"!==this.visibilityState&&this.windowActive||this.stop()}getObserverThresholds(e){return.3===e.minPercentageInView?Object.assign(Object.assign({},Yi),{threshold:[.3,.5,.75,1]}):Object.assign(Object.assign({},Yi),{threshold:[.5,.75,1]})}onVisibilityChange(){this.visibilityState="visible"===this.visibilityState?"hidden":"visible",this.checkWindowActive()}onFocusChange(e){this.windowActive="focusin"===e.type||"focus"===e.type,this.checkWindowActive()}checkWindowActive(){"visible"===this.visibilityState&&this.windowActive?this.start():this.pause()}destroy(){var e;this.stop(),null===(e=this.observer)||void 0===e||e.disconnect(),this.pbstckWindow.removeEventListener("visibilitychange",this.trackedOnVisibilityChange),this.pbstckWindow.removeEventListener("focus",this.trackedOnFocusChange),this.pbstckWindow.removeEventListener("blur",this.trackedOnFocusChange),this.computer=null,this.observer=null}getElapsed(){return null===this.computer?0:this.computer.getElapsed()}pause(){var e;null===(e=this.computer)||void 0===e||e.pauseTimer()}start(){var e;null===(e=this.computer)||void 0===e||e.startTimer()}stop(){var e;null===(e=this.computer)||void 0===e||e.stopTimer()}intersectionObserverCallback(e){e.forEach((e=>{var t;null===(t=this.computer)||void 0===t||t.intersectionChange(e)}))}getTimerState(){var e;return null===(e=this.computer)||void 0===e?void 0:e.getTimerState()}}const Zi={viewableTime:1e3,largeAdunitSize:242e3,largeAdunitTreshold:.3,standardAdunitTreshold:.5};class es{constructor(e,t){this.viewabilityState=new Map,this.viewedTimeState=new Map,this.elementIdToCode=new Map,this.viewabilityStream=new c,this.viewedStream=new c,V("[pubstackViewability] Create ViewabilityController with config",Zi),this.pbstckWindow=t,this.pbstckWindow.addEventListener("unload",(()=>this.unloadMeasuredImpressions())),e.forwarder.coreImpressionStream.subscribe((e=>{V("[pubstackViewability] Receive impression",e.bidderCode,e.adUnit.code),this.track(e)})),e.forwarder.coreAuctionStream.subscribe((e=>{V("[pubstackViewability] Receive auctionend",e.adUnit.code),this.endMeasure(e.adUnit.code)}))}onUnload(e){this.unloadCallback=e}endMeasure(e){V("[pubstackViewability] receive event to stop measure");const t=this.viewedTimeState.get(e);void 0!==t?(t.viewabilitytracker.stop(),this.onMeasurable(e)):V("[pubstackViewability] event received but no tracker to stop, skipping")}track(e){if(!e.viewabilityMeasurable)return void V("[pubstackViewability] Cannot track impression for adUnit ",e.adUnit);const t=mi(e.adUnit);null!==t?(this.trackViewability(e,t),this.trackMeasure(e,t)):x(new Error(`[pubstackViewability] Unexpected null HTML Element on viewable impression for adUnit ${e.adUnit.name}`))}trackMeasure(e,i){return t(this,void 0,void 0,(function*(){var t;let s=null===(t=this.viewedTimeState.get(e.adUnit.code))||void 0===t?void 0:t.viewabilitytracker;this.elementIdToCode.set(i.id,e.adUnit.code),void 0!==s&&(V(`[pubstackViewability] replacing existing measurability tracker on ${i.id}`),s.stop(),this.onMeasurable(e.adUnit.code)),V(`[pubstackViewability] tracking code ${e.adUnit.code} with rule MRC for measurability`);const n={minPercentageInView:this.minPercentageInView(i,Zi),minTimeInView:18e4,cumulativeTimer:!0,completionCallback:e=>{const t=this.elementIdToCode.get(e);void 0!==t?this.onMeasurable(t):V(`[pubstackViewability] unable to find matching adunitcode for element ${e}`)}};s=new Ki(i,n,this.pbstckWindow),this.viewedTimeState.set(e.adUnit.code,{impression:e,viewabilitytracker:s})}))}trackViewability(e,i){return t(this,void 0,void 0,(function*(){var t;let s=null===(t=this.viewabilityState.get(i.id))||void 0===t?void 0:t.viewabilitytracker;void 0!==s&&(V(`[pubstackViewability] replacing existing tracker on ${i.id}`),s.destroy(),this.viewabilityState.delete(i.id)),V(`[pubstackViewability] tracking element ${i.id} with rule MRC for monitoring`);const n={minPercentageInView:this.minPercentageInView(i,Zi),minTimeInView:Zi.viewableTime,cumulativeTimer:!1,completionCallback:e=>this.onViewable(e)};s=new Ki(i,n,this.pbstckWindow),this.viewabilityState.set(i.id,{impression:e,viewabilitytracker:s})}))}minPercentageInView(e,t){const i=window.getComputedStyle(e);return Number(i.getPropertyValue("width").replace(/px/,""))*Number(i.getPropertyValue("height").replace(/px/,""))>t.largeAdunitSize?t.largeAdunitTreshold:t.standardAdunitTreshold}unloadMeasuredImpressions(){if(V("[pubstackViewability] page unloaded, forwarding impressions measured"),void 0!==this.unloadCallback){const e=[];Array.from(this.viewedTimeState.values()).forEach((t=>{if(void 0!==t.viewabilitytracker){t.viewabilitytracker.stop();const i=Math.floor(t.viewabilitytracker.getElapsed()/1e3);i>0&&e.push({bidId:t.impression.bidId,auctionId:t.impression.auctionId,lastAuctionId:t.impression.lastAuctionId,adUnit:t.impression.adUnit,bidderCode:t.impression.bidderCode,pbjsVersion:t.impression.pbjsVersion,cpm:t.impression.cpm,currency:t.impression.currency,refresh:t.impression.refresh,size:t.impression.size,viewedTime:i,pubstackRefresh:t.impression.pubstackRefresh,pubstackRefreshRank:t.impression.pubstackRefreshRank})}})),e.length>0&&this.unloadCallback(e)}}onMeasurable(e){V(`[pubstackViewability] Measurability Event on AdUnit code ${e}`);const t=this.viewedTimeState.get(e);if(void 0===t)return void x(new Error(`[pubstackViewability] Impression not found for AdUnit code ${e}`));this.viewedTimeState.delete(e);if(Math.floor(t.viewabilitytracker.getElapsed()/1e3)>0){const e=t.impression,i={bidId:e.bidId,auctionId:e.auctionId,lastAuctionId:e.lastAuctionId,adUnit:e.adUnit,bidderCode:e.bidderCode,pbjsVersion:e.pbjsVersion,cpm:e.cpm,currency:e.currency,refresh:e.refresh,size:e.size,viewedTime:Math.floor(t.viewabilitytracker.getElapsed()/1e3),pubstackRefresh:e.pubstackRefresh,pubstackRefreshRank:e.pubstackRefreshRank};V(`[pubstackViewability] Forwarding measured impression on code ${e.adUnit.code}`),this.viewedStream.next(i)}t.viewabilitytracker.destroy()}onViewable(e){V(`[pubstackViewability] Viewability Event on element ${e}`);const t=this.viewabilityState.get(e);if(void 0===t)return void x(new Error(`[pubstackViewability] Impression not found for ElementId ${e}`));this.viewabilityState.set(e,t);const i=t.impression,s={bidId:i.bidId,auctionId:i.auctionId,lastAuctionId:i.lastAuctionId,adUnit:i.adUnit,bidderCode:i.bidderCode,pbjsVersion:i.pbjsVersion,cpm:i.cpm,currency:i.currency,refresh:i.refresh,size:i.size,htmlElementId:e,pubstackRefresh:i.pubstackRefresh,pubstackRefreshRank:i.pubstackRefreshRank};V(`[pubstackViewability] Forwarding viewable impression ${s.htmlElementId}`),this.viewabilityStream.next(s)}}const ts=()=>{const e=navigator.userAgent;return/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(e)?"tablet":/Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(e)?"mobile":"desktop"};function is(e,i,s){var n;e.pbstck=e.pbstck||{lock:{}},e.pbstck.lock=e.pbstck.lock||{},e.pbstck.scopeId=s.scopeId,e.pbstck.tagId=s.tagId;const o={},r=`${s.tagId}@${i.gateway}@collector`;if(function(e,t){return e[t]}(e.pbstck.lock,r))return;!function(e,t){e[t]=!0}(e.pbstck.lock,r);const a=new ki;let d;o.core=a,i.viewabilityEnabled&&(d=new es(a,e),o.viewability=d);const c=new Pi(i.gateway,s,a,d,i.admOnboarding,i.abTestValues);var u;o.intake=c,S((e=>c.sendError(e)),1),u=e=>{i.logsEnabled.includes(e.id)&&c.sendLog(e)},ue.subscribe(u),function(e){R.subscribe(e)}((e=>c.sendToDatadog(e)));const l=new Promise(((s,n)=>{if(i.pbjsVariableName){V("Prebid dropin mode",i.pbjsVariableName);const r={debug:N(),globalName:i.pbjsVariableName},d=function(e,t){return e[t.globalName]||(e[t.globalName]={}),e[t.globalName].que||(e[t.globalName].que=[]),e[t.globalName].que}(e,r);d.push((()=>t(this,void 0,void 0,(function*(){var t,d;const c=e[r.globalName],u=null!==(t=Number(c.getConfig("timeoutBuffer")))&&void 0!==t?t:Ri,l=null===(d=c.getConfig("currency"))||void 0===d?void 0:d.adServerCurrency;let b;h(l)&&(b=l);const p=new re({version:c.version,gracePeriod:u,adServerCurrency:b,pbjsVariableName:r.globalName},i.admOnboarding);o.prebid=p;try{o.prebid=function(e,t,i,s){const n=e[s.globalName];i.bindIntegration(t);const o=le(ve(),t);let r;if(null!=n.getEvents)V("[pbjsIntegration] retrieve pbjs events using getEvents on public API"),r=n.getEvents;else{V("[pbjsIntegration] retrieve pbjs events using chunk");const t=e[`${s.globalName}Chunk`];if(void 0===t)throw new Error("[pbjsIntegration] unable to find pbjs chunk");const i=Ai(t,{on:Function,getEvents:Function});if(void 0===i)throw new Error("[pbjsIntegration] unable to use event handler on adapter");r=i.getEvents}return Object.values(ce).forEach((e=>{n.onEvent(e,(t=>{var i;try{o.on(e,t)}catch(s){$("[pbjsIntegration] Error on event "+e+": "+s.message,t),T({context:Object.assign(Object.assign({},null!==(i=s.context)&&void 0!==i?i:{}),{adapter:{version:n.version,source:"pbjs:on"}}),message:s.message})}}))})),r().forEach((({eventType:e,args:t})=>{var i;try{o.on(e,t)}catch(s){$("[pbjsIntegration] Error on event "+e+": "+s.message,t),T({context:Object.assign(Object.assign({},null!==(i=s.context)&&void 0!==i?i:{}),{adapter:{version:n.version,source:"pbjs:replayed"}}),message:s.message})}})),t}(e,p,a,r),s()}catch(e){return $("Unable to load pbjs integration due to",e),void n()}}))))}}));let b,p=[];if(i.smartEnabled||i.debug){const t={globalName:"sas"};p=function(e,t){return e[t.globalName]||(e[t.globalName]={}),e[t.globalName].cmd||(e[t.globalName].cmd=[]),e[t.globalName].cmd}(e,t),p.push((()=>{b=Ei(e,a,t).instance}))}const m={tagId:s.tagId,globalQueue:i.sdk.globalQueue},v=ge(e,a,m);if(v.dispatchEvents(),i.debug||N()){a.subscribe({onAuction:e=>V("controller.onAuction",e),onImpression:e=>V("controller.onImpression",e)});const t=new Oi(a);o.debug=t,S((e=>t.addError(e)),1e3),e.pbstck.debug=e.pbstck.debug||{},e.pbstck.debug[r]={getEvents:()=>t.getEvents(),getErrors:()=>t.getErrors(),sdk:null!==(n=null==v?void 0:v.debug())&&void 0!==n?n:void 0},(i.smartEnabled||i.debug)&&p.push((()=>{b&&(e.pbstck.debug[r].sas=b.debug())}))}return Promise.resolve().finally(),e.pbstck.controllers=e.pbstck.controllers||{},e.pbstck.controllers[`${i.gateway}@collector`]=o,l.then((()=>{e.dispatchEvent(new Event(we(`${i.gateway}@collector`,"pubstackMonitoringReady")))})),a}e.bootPubstack=is,e.pubstackAutoconfig=function(e){var i,s,n,o;const r={gateway:null===(i=e.endpoint)||void 0===i?void 0:i.gateway,sdk:{globalQueue:"pbstckQ"},debug:!0===e.debug,viewabilityEnabled:e.viewabilityEnabled,smartEnabled:null!==(s=e.smartEnabled)&&void 0!==s&&s,refreshConfigurationUrl:null!==(n=e.refreshConfigurationUrl)&&void 0!==n?n:"",pbjsVariableName:e.pbjsVariableName||"pbjs",abTestValues:e.abTestValues,logsEnabled:e.logsEnabled||[],admOnboarding:e.admOnboarding};if(void 0===r.gateway)return;const a=new ci(navigator.userAgent),d=a.getOS(),c=a.getBrowser(),u={tagId:e.tagId,scopeId:e.scopeId,country:e.country,device:ts(),browserName:c.name,browserVersion:c.major,osName:d.name,osVersion:d.version,pbstckVersion:null!==(o="cfcddc4")?o:"unknown",customFields:Di()};u.customFields.kleanadsDefaultDevice=window.innerWidth<768?"mobile":"desktop",t(void 0,void 0,void 0,(function*(){try{return navigator&&navigator.cookieDeprecationLabel&&navigator.cookieDeprecationLabel.getValue&&(yield navigator.cookieDeprecationLabel.getValue())||void 0}catch(e){V("Error while getting cookie depreciation label",e)}})).then((e=>{e&&(u.customFields.cdep=e)})),u.tagId&&u.scopeId&&is(window,r,u)}}(this.collector=this.collector||{});
;
 return this;}.bind({}); var _ = load();_.collector.pubstackAutoconfig({"endpoint":{"gateway":"https://intake.pbstck.com/v1/intake"},"scopeId":"bb520c1b-ea12-40be-9a16-8de34e0e8435","tagId":"621169b6-0529-464c-8712-1e799d75704d","viewabilityEnabled":true,"refreshEnabled":false,"smartEnabled":false,"pbjsVariableName":"aaw","abTestValues":["true","false","true2","false2"]}); })()</script><script async="" src="https://fundingchoicesmessages.google.com/f/AGSKWxUjsYt2xN-Rx8DMuMVuB19VpAnRrYyMIHMgG92iSNT8Pi-dHC8tdsbuX7A-Y0vuRbnzewLr24rIfSs4_VMI816QolGrqAw2hx16OTBWo_HOsbQjRVxHr8KSwifeWMjKnV0IlKgH?fccs=W251bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLFsxNzQ4OTU4NzMwLDgzNDAwMDAwMF0sbnVsbCxudWxsLG51bGwsW251bGwsWzddXSwiaHR0cHM6Ly93d3cuc21hcnRiYWNrZ3JvdW5kY2hlY2tzLmNvbS9waG9uZS81NjE5MzI0MjE3IixudWxsLFtbOCwiNWpVb291VWotQ1kiXSxbOSwiZW4tVVMiXSxbMjMsIjE3NDg5NTg3MDEiXSxbMjYsIjciXSxbMjcsIjE4Il0sWzE5LCIyIl0sWzE3LCJbMF0iXSxbMjQsIiJdLFsyNSwiW1szMTA4NDQ5MV1dIl0sWzI5LCJmYWxzZSJdXV0"></script></head>
<body>
<div class="container" style="max-width: 100%; padding-left:0px"><div class="span12">

<div class="container-fluid no-gutters d-block"><div class="justify-content-center row no-gutters" style="max-width: 100%">

<div name="leftPanel" class="col-md-2"></div>

<div name="centerPanel"><span class="text-center"><a href="https://www.smartbackgroundchecks.com/"><img src="/images/sbc_logo_trans_dark.png" width="312" height="61" title="Start a SmartBackgroundCheck Now" alt="SmartBackgroundChecks" style="object-fit: contain;width: 80%; max-width:312px; max-height:48px"></a></span>&nbsp;<a class="btn btn-small btn-secondary" href="#" onclick="newSearch()"><img data-src="/images/search-solid.svg" src="/images/search-solid.svg" alt="Background Check" title="Background Check" height="15" width="15"></a></div>

<div name="rightPanel" class="col-md-2"></div></div></div>



<div class="container-fluid" style="box-sizing: content-box !important"><div class="justify-content-center row" style="max-width:100%">

<div name="leftPanel" class="col ad-sidebar break-word hidden-sm-down d-none d-lg-block" style="min-width:200px"><div id="bsa-zone_1743777975783-3_123456" data-google-query-id="CMmI74Cz1Y0DFTiupgQdf6wy8A"><div id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_LeftSidebar_ROS_0__container__" style="border: 0pt none; margin: auto; text-align: center; width: 160px; height: 600px;"><iframe frameborder="0" src="https://e0d235542a55b49e6f47dae4abe4b5a2.safeframe.googlesyndication.com/safeframe/1-0-45/html/container.html" id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_LeftSidebar_ROS_0" title="3rd party ad content" name="1-0-45;55256;<!doctype html><html><head><script>var jscVersion = 'r20250602';</script><script>var google_casm=[];</script></head><body leftMargin=&quot;0&quot; topMargin=&quot;0&quot; marginwidth=&quot;0&quot; marginheight=&quot;0&quot;><script>window.dicnf = {};</script><script data-jc=&quot;42&quot; data-jc-version=&quot;r20250602&quot; data-jc-flags=&quot;[&amp;quot;x%278446&amp;#39;9efotm(&amp;amp;20067;&amp;gt;8&amp;amp;&amp;gt;`dopb/%&amp;lt;1732261!=|vqc)!7201061?&amp;#39;9efotm(&amp;amp;20723;&amp;gt;:&amp;amp;&amp;gt;`dopb/%&amp;lt;1245;05!=nehu`/!361:&amp;lt;320!9sqrm(&amp;amp;2057?61&amp;lt;&amp;amp;&amp;gt;`dopb~&amp;quot;]&quot;>(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var r=this||self;function aa(a){r.setTimeout(()=>{throw a;},0)};function v(a){v[&quot; &quot;](a);return a}v[&quot; &quot;]=function(){};var ba={},w=null;let ca=void 0;function A(a,b=!1){return b&amp;&amp;Symbol.for&amp;&amp;a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var B=A(),E=A(&quot;m_m&quot;,!0);const F=A(&quot;jas&quot;,!0);var da;const ea=[];ea[F]=7;da=Object.freeze(ea);var G={};function H(a,b){return b===void 0?a.g!==I&amp;&amp;!!(2&amp;(a.j[F]|0)):!!(2&amp;b)&amp;&amp;a.g!==I}const I={};const fa=BigInt(Number.MIN_SAFE_INTEGER),ha=BigInt(Number.MAX_SAFE_INTEGER);function ia(a){if(typeof a!==&quot;boolean&quot;){var b=typeof a;throw Error(`Expected boolean but got ${b!=&quot;object&quot;?b:a?Array.isArray(a)?&quot;array&quot;:b:&quot;null&quot;}: ${a}`);}return a};function ka(a){return a};function J(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let l,h=**********,m=!1;const k=!!(b&amp;64),n=k?b&amp;128?0:-1:void 0;b&amp;1||(l=g&amp;&amp;a[g-1],l!=null&amp;&amp;typeof l===&quot;object&quot;&amp;&amp;l.constructor===Object?(g--,h=g):l=void 0,!k||b&amp;128||e||(m=!0,h=(la??ka)(h-n,n,a,l)+n));b=void 0;for(e=0;e<g;e++){let p=a[e];if(p!=null&amp;&amp;(p=c(p,d))!=null)if(k&amp;&amp;e>=h){const q=e-n;(b??(b={}))[q]=p}else f[e]=p}if(l)for(let p in l){a=l[p];if(a==null||(a=c(a,d))==null)continue;g=+p;let q;k&amp;&amp;!Number.isNaN(g)&amp;&amp;(q=g+n)<h?f[q]=a: (b??(b={}))[p]=a}b&amp;&amp;(m?f.push(b):f[h]=b);return f}function ma(a){switch(typeof a){case &quot;number&quot;:return Number.isFinite(a)?a:&quot;&quot;+a;case &quot;bigint&quot;:return a>=fa&amp;&amp;a<=ha?Number(a):&quot;&quot;+a;case &quot;boolean&quot;:return a?1:0;case &quot;object&quot;:if(Array.isArray(a)){const b=a[F]|0;return a.length===0&amp;&amp;b&amp;1?void 0:J(a,b,ma)}if(a!=null&amp;&amp;a[E]===G)return K(a);return}return a}let la;function K(a){a=a.j;return J(a,a[F]|0,ma)};function na(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error(&quot;narr&quot;);b=a[F]|0;2048&amp;b&amp;&amp;!(2&amp;b)&amp;&amp;oa();if(b&amp;256)throw Error(&quot;farr&quot;);if(b&amp;64)return b&amp;2048||(a[F]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&amp;&amp;typeof d===&quot;object&quot;&amp;&amp;d.constructor===Object){const f=b&amp;128?0:-1;e-=f;if(e>=1024)throw Error(&quot;pvtlmt&quot;);for(const g in d){const l=+g;if(l<e)c[l+f]=d[g],delete d[g];else break}b=b&amp;-8380417|(e&amp;1023)<<13}}}a[F]=b|2112;return a} function oa(){if(B!=null){var a=ca??(ca={});var b=a[B]||0;b>=5||(a[B]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity=&quot;incident&quot;,aa(a))}};function pa(a,b){if(typeof a!==&quot;object&quot;)return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&amp;&amp;c&amp;1?a=void 0:c&amp;2||(!b||4096&amp;c||16&amp;c?a=L(a,c,!1,b&amp;&amp;!(c&amp;16)):(a[F]|=34,c&amp;4&amp;&amp;Object.freeze(a)));return a}if(a!=null&amp;&amp;a[E]===G){c=a.j;const d=c[F]|0;H(a,d)||(d&amp;2?b=!0:!(d&amp;32)||d&amp;4096?b=!1:(c[F]=d|2,a.g=I,b=!0),b?(a=new a.constructor(c),a.i=I):a=L(c,d));return a}}function L(a,b,c,d){d??(d=!!(34&amp;b));a=J(a,b,pa,d);d=32;c&amp;&amp;(d|=2);b=b&amp;8380609|d;a[F]=b;return a} function qa(a){if(a.g===I){var b=a.j;b=L(b,b[F]|0);b[F]|=2048;a.j=b;a.g=void 0;a.i=void 0;b=!0}else b=!1;if(!b&amp;&amp;H(a,a.j[F]|0))throw Error();};function ra(a,b,c){qa(a);const d=a.j;sa(d,d[F]|0,b,c);return a}function sa(a,b,c,d){const e=c+-1;var f=a.length-1;if(f>=0&amp;&amp;e>=f){const g=a[f];if(g!=null&amp;&amp;typeof g===&quot;object&quot;&amp;&amp;g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&amp;&amp;(f=(b??(b=a[F]|0))>>13&amp;1023||536870912,c>=f?d!=null&amp;&amp;(a[f+-1]={[c]:d}):a[e]=d);return b}function M(a,b,c){if(c!=null&amp;&amp;typeof c!==&quot;string&quot;)throw Error();return ra(a,b,c)};var N=class{constructor(a){this.j=na(a)}toJSON(){return K(this)}};N.prototype[E]=G;N.prototype.toString=function(){return this.j.toString()};var O=class{constructor(a,b=!1){this.key=a;this.defaultValue=b;this.valueType=&quot;boolean&quot;}};var ta=new O(&quot;45368259&quot;),ua=new O(&quot;45357156&quot;,!0),va=new O(&quot;45350890&quot;),wa=new O(&quot;45414892&quot;),xa=new O(&quot;45620832&quot;),ya=new O(&quot;45648564&quot;);const za=RegExp(&quot;ad\\.doubleclick\\.net/(ddm/trackimp|pcs/view)&quot;);var P=(a,b)=>a.substring(a.length-7)==&quot;&amp;adurl=&quot;?a.substring(0,a.length-7)+b+&quot;&amp;adurl=&quot;:a+b;function Aa(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Ba(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&amp;&amp;b(a[c],c,a)}function Ca(a=document){return a.createElement(&quot;img&quot;)};function R(a,b,c){typeof a.addEventListener===&quot;function&quot;&amp;&amp;a.addEventListener(b,c,!1)}function Da(a,b,c){typeof a.removeEventListener===&quot;function&quot;&amp;&amp;a.removeEventListener(b,c,!1)};function Ea(a,b=null){Ja(a,b)}function Ja(a,b){r.google_image_requests||(r.google_image_requests=[]);const c=Ca(r.document);if(b){const d=e=>{b&amp;&amp;b(e);Da(c,&quot;load&quot;,d);Da(c,&quot;error&quot;,d)};R(c,&quot;load&quot;,d);R(c,&quot;error&quot;,d)}c.src=a;r.google_image_requests.push(c)};let Ka=0;function La(a,b=null){return b&amp;&amp;b.getAttribute(&quot;data-jc&quot;)===String(a)?b:document.querySelector(`[${&quot;data-jc&quot;}=&quot;${a}&quot;]`)};function S(a){Ma||(Ma=new Na);const b=Ma.g[a.key];if(a.valueType===&quot;proto&quot;){try{const c=JSON.parse(b);if(Array.isArray(c))return c}catch(c){}return a.defaultValue}return typeof b===typeof a.defaultValue?b:a.defaultValue}var Oa=class{constructor(){this.g={}}};var Na=class extends Oa{constructor(){super();var a=La(Ka,document.currentScript);a=a&amp;&amp;a.getAttribute(&quot;data-jc-flags&quot;)||&quot;&quot;;try{const b=JSON.parse(a)[0];a=&quot;&quot;;for(let c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^&quot;\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003&quot;.charCodeAt(c%10));this.g=JSON.parse(a)}catch(b){}}},Ma;var Pa=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||&quot;&quot;;this.id=b.id||&quot;jserror&quot;}};function Qa(a){let b=a.toString();a.name&amp;&amp;b.indexOf(a.name)==-1&amp;&amp;(b+=&quot;: &quot;+a.name);a.message&amp;&amp;b.indexOf(a.message)==-1&amp;&amp;(b+=&quot;: &quot;+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&amp;&amp;(a=c+&quot;\n&quot;+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp(&quot;((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2&quot;),&quot;$1&quot;);b=a.replace(RegExp(&quot;\n *&quot;,&quot;g&quot;),&quot;\n&quot;);break a}catch(d){b=c;break a}b=void 0}return b};const Ra=RegExp(&quot;^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)&quot;);var Sa=class{constructor(a,b){this.g=a;this.i=b}},Ta=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let T=null;function Ua(){const a=r.performance;return a&amp;&amp;a.now&amp;&amp;a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Va(){const a=r.performance;return a&amp;&amp;a.now?a.now():null};var Wa=class{constructor(a,b){var c=Va()||Ua();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const U=r.performance,Xa=!!(U&amp;&amp;U.mark&amp;&amp;U.measure&amp;&amp;U.clearMarks),V=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=Xa){var b;a=window;if(T===null){T=&quot;&quot;;try{let c=&quot;&quot;;try{c=a.top.location.hash}catch(d){c=a.location.hash}c&amp;&amp;(T=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:&quot;&quot;)}catch(c){}}b=T;a=!!b.indexOf&amp;&amp;b.indexOf(&quot;1337&quot;)>=0}return a});function Ya(a){a&amp;&amp;U&amp;&amp;V()&amp;&amp;(U.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),U.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function W(a,b){const c={};c[a]=b;return[c]}function Za(a,b,c,d,e){const f=[];Ba(a,(g,l)=>{(g=$a(g,b,c,d,e))&amp;&amp;f.push(`${l}=${g}`)});return f.join(b)} function $a(a,b,c,d,e){if(a==null)return&quot;&quot;;b=b||&quot;&amp;&quot;;c=c||&quot;,$&quot;;typeof c===&quot;string&quot;&amp;&amp;(c=c.split(&quot;&quot;));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push($a(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a===&quot;object&quot;)return e||(e=0),e<2?encodeURIComponent(Za(a,b,c,d,e+1)):&quot;...&quot;;return encodeURIComponent(String(a))}function ab(a){let b=1;for(const c in a.i)c.length>b&amp;&amp;(b=c.length);return 3997-b-a.l.length-1} function bb(a,b){let c=&quot;https://pagead2.googlesyndication.com&quot;+b,d=ab(a)-b.length;if(d<0)return&quot;&quot;;a.g.sort((f,g)=>f-g);b=null;let e=&quot;&quot;;for(let f=0;f<a.g.length;f++){const g=a.g[f],l=a.i[g];for(let h=0;h<l.length;h++){if(!d){b=b==null?g:b;break}let m=Za(l[h],a.l,&quot;,$&quot;);if(m){m=e+m;if(d>=m.length){d-=m.length;c+=m;e=a.l;break}b=b==null?g:b}}}a=&quot;&quot;;b!=null&amp;&amp;(a=`${e}${&quot;trn&quot;}=${b}`);return c+a}var cb=class{constructor(){this.l=&quot;&amp;&quot;;this.i={};this.m=0;this.g=[]}};var db=RegExp(&quot;^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$&quot;);function eb(a,b,c,d){const e=c.length;for(;(b=a.indexOf(c,b))>=0&amp;&amp;b<d;){var f=a.charCodeAt(b-1);if(f==38||f==63)if(f=a.charCodeAt(b+e),!f||f==61||f==38||f==35)return b;b+=e+1}return-1}var fb=/#|$/; function gb(a){const b=a.search(fb);let c=eb(a,0,&quot;ase&quot;,b);if(c<0)return null;let d=a.indexOf(&quot;&amp;&quot;,c);if(d<0||d>b)d=b;return decodeURIComponent(a.slice(c+4,d!==-1?d:0).replace(/\+/g,&quot; &quot;))}var hb=/[?&amp;]($|#)/; function ib(a,b){var c=a.search(fb),d=0,e;const f=[];for(;(e=eb(a,d,&quot;nis&quot;,c))>=0;)f.push(a.substring(d,e)),d=Math.min(a.indexOf(&quot;&amp;&quot;,e)+1||c,c);f.push(a.slice(d));a=f.join(&quot;&quot;).replace(hb,&quot;$1&quot;);(b=&quot;nis&quot;+(b!=null?&quot;=&quot;+encodeURIComponent(String(b)):&quot;&quot;))?(c=a.indexOf(&quot;#&quot;),c<0&amp;&amp;(c=a.length),d=a.indexOf(&quot;?&quot;),d<0||d>c?(d=c,e=&quot;&quot;):e=a.substring(d+1,c),a=[a.slice(0,d),e,a.slice(c)],c=a[1],a[1]=b?c?c+&quot;&amp;&quot;+b:b:c,b=a[0]+(a[1]?&quot;?&quot;+a[1]:&quot;&quot;)+a[2]):b=a;return b};function jb(a,b,c,d){let e,f;try{a.g&amp;&amp;a.g.g?(f=a.g.start(b.toString(),3),e=c(),a.g.end(f)):e=c()}catch(g){c=!0;try{Ya(f),c=a.v(b,new Pa(g,{message:Qa(g)}),void 0,d)}catch(l){a.m(217,l)}if(c)window.console?.error?.(g);else throw g;}return e}function kb(a,b,c,d){var e=X;return(...f)=>jb(e,a,()=>b.apply(c,f),d)} var mb=class{constructor(a=null){this.u=Y;this.g=a;this.i=null;this.l=!1;this.v=this.m}m(a,b,c,d,e){e=e||&quot;jserror&quot;;let f=void 0;try{const C=new cb;var g=C;g.g.push(1);g.i[1]=W(&quot;context&quot;,a);b.error&amp;&amp;b.meta&amp;&amp;b.id||(b=new Pa(b,{message:Qa(b)}));g=b;if(g.msg){b=C;var l=g.msg.substring(0,512);b.g.push(2);b.i[2]=W(&quot;msg&quot;,l)}var h=g.meta||{};l=h;if(this.i)try{this.i(l)}catch(z){}if(d)try{d(l)}catch(z){}d=C;h=[h];d.g.push(3);d.i[3]=h;var m;if(!(m=q)){d=r;h=[];let z;l=null;do{var k=d;try{var n;if(n=!!k&amp;&amp;k.location.href!= null)b:{try{v(k.foo);n=!0;break b}catch(x){}n=!1}var p=n}catch{p=!1}p?(z=k.location.href,l=k.document&amp;&amp;k.document.referrer||null):(z=l,l=null);h.push(new Ta(z||&quot;&quot;));try{d=k.parent}catch(x){d=null}}while(d&amp;&amp;k!==d);for(let x=0,Fa=h.length-1;x<=Fa;++x)h[x].depth=Fa-x;k=r;if(k.location&amp;&amp;k.location.ancestorOrigins&amp;&amp;k.location.ancestorOrigins.length===h.length-1)for(p=1;p<h.length;++p){const x=h[p];x.url||(x.url=k.location.ancestorOrigins[p-1]||&quot;&quot;,x.g=!0)}m=h}var q=m;let Q=new Ta(r.location.href,!1);m= null;const ja=q.length-1;for(k=ja;k>=0;--k){var t=q[k];!m&amp;&amp;Ra.test(t.url)&amp;&amp;(m=t);if(t.url&amp;&amp;!t.g){Q=t;break}}t=null;const pb=q.length&amp;&amp;q[ja].url;Q.depth!==0&amp;&amp;pb&amp;&amp;(t=q[ja]);f=new Sa(Q,t);if(f.i){q=C;var u=f.i.url||&quot;&quot;;q.g.push(4);q.i[4]=W(&quot;top&quot;,u)}var D={url:f.g.url||&quot;&quot;};if(f.g.url){const z=f.g.url.match(db);var y=z[1],Ga=z[3],Ha=z[4];u=&quot;&quot;;y&amp;&amp;(u+=y+&quot;:&quot;);Ga&amp;&amp;(u+=&quot;//&quot;,u+=Ga,Ha&amp;&amp;(u+=&quot;:&quot;+Ha));var Ia=u}else Ia=&quot;&quot;;y=C;D=[D,{url:Ia}];y.g.push(5);y.i[5]=D;lb(this.u,e,C,this.l,c)}catch(C){try{lb(this.u,e,{context:&quot;ecmserr&quot;, rctx:a,msg:Qa(C),url:f?.g.url??&quot;&quot;},this.l,c)}catch(Q){}}return!0}};class nb{};function lb(a,b,c,d=!1,e,f){if((d?a.g:Math.random())<(e||.01))try{let g;c instanceof cb?g=c:(g=new cb,Ba(c,(h,m)=>{var k=g;const n=k.m++;h=W(m,h);k.g.push(n);k.i[n]=h}));const l=bb(g,&quot;/pagead/gen_204?id=&quot;+b+&quot;&amp;&quot;);l&amp;&amp;(typeof f!==&quot;undefined&quot;?Ea(l,f):Ea(l))}catch(g){}}function ob(){var a=Y,b=window.google_srt;b>=0&amp;&amp;b<=1&amp;&amp;(a.g=b)}var qb=class{constructor(){this.g=Math.random()}};let Y,X; const Z=new class{constructor(a,b){this.i=[];this.l=b||r;let c=null;b&amp;&amp;(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.i=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=V()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new Wa(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;U&amp;&amp;V()&amp;&amp;U.mark(b);return a}end(a){if(this.g&amp;&amp;typeof a.value===&quot;number&quot;){a.duration=(Va()||Ua())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;U&amp;&amp;V()&amp;&amp;U.mark(b);!this.g||this.i.length> 2048||this.i.push(a)}}}(1,window);function rb(){window.google_measure_js_timing||(Z.g=!1,Z.i!==Z.l.google_js_reporting_queue&amp;&amp;(V()&amp;&amp;Array.prototype.forEach.call(Z.i,Ya,void 0),Z.i.length=0))} (function(a){Y=a??new qb;typeof window.google_srt!==&quot;number&quot;&amp;&amp;(window.google_srt=Math.random());ob();X=new mb(Z);X.i=b=>{var c=Ka;c!==0&amp;&amp;(b.jc=String(c),c=(c=La(c,document.currentScript))&amp;&amp;c.getAttribute(&quot;data-jc-version&quot;)||&quot;unknown&quot;,b.shv=c)};X.l=!0;window.document.readyState===&quot;complete&quot;?rb():Z.g&amp;&amp;R(window,&quot;load&quot;,()=>{rb()})})();function sb(a,b,c,d){return kb(a,b,c,d)} function tb(a,b,c,d){var e=nb;var f=&quot;o&quot;;e.o&amp;&amp;e.hasOwnProperty(f)||(f=new e,e.o=f);e=[];!b.eid&amp;&amp;e.length&amp;&amp;(b.eid=e.toString());lb(Y,a,b,!0,c,d)};function ub(a){let b;a.visibilityState?b=&quot;visibilitychange&quot;:a.mozVisibilityState?b=&quot;mozvisibilitychange&quot;:a.webkitVisibilityState&amp;&amp;(b=&quot;webkitvisibilitychange&quot;);return b};function vb(){const a=window;if(a.gmaSdk||a.webkit?.messageHandlers?.getGmaViewSignals)return a;try{const b=window.parent;if(b.gmaSdk||b.webkit?.messageHandlers?.getGmaViewSignals)return b}catch(b){}return null} function wb(a,b={},c=()=>{},d=()=>{},e=200,f,g){const l=String(Math.floor(Aa()*2147483647));let h=0;const m=k=>{try{const n=typeof k.data===&quot;object&quot;?k.data:JSON.parse(k.data);l===n.paw_id&amp;&amp;(window.clearTimeout(h),window.removeEventListener(&quot;message&quot;,m),n.signal?c(n.signal):n.error&amp;&amp;d(n.error))}catch(n){g(&quot;paw_sigs&quot;,{msg:&quot;postmessageError&quot;,err:n instanceof Error?n.message:&quot;nonError&quot;,data:k.data==null?&quot;null&quot;:k.data.length>500?k.data.substring(0,500):k.data})}};window.addEventListener(&quot;message&quot;,k=>{f(903, ()=>{m(k)})()});a.postMessage({paw_id:l,...b});h=window.setTimeout(()=>{window.removeEventListener(&quot;message&quot;,m);d(&quot;PAW GMA postmessage timed out.&quot;)},e)};function xb(a=document){return!!a.featurePolicy?.allowedFeatures().includes(&quot;attribution-reporting&quot;)};var yb=class extends N{};function zb(a,b){return M(a,2,b)}function Ab(a,b){return M(a,3,b)}function Bb(a,b){return M(a,4,b)}function Cb(a,b){return M(a,5,b)}function Db(a,b){return M(a,9,b)} function Eb(a,b){{var c=b;qa(a);const k=a.j;b=k[F]|0;if(c==null)sa(k,b,10);else{var d=c===da?7:c[F]|0,e=d,f=!!(2&amp;d)&amp;&amp;!!(4&amp;d)||!!(256&amp;d),g=f||Object.isFrozen(c),l=!0,h=!0;for(let n=0;n<c.length;n++){var m=c[n];f||(m=H(m),l&amp;&amp;(l=!m),h&amp;&amp;(h=m))}f||(d=l?13:5,d=h?d&amp;-4097:d|4096);g&amp;&amp;d===e||(c=[...c],e=0,d=2&amp;b?d|2:d&amp;-3,d&amp;=-273);d!==e&amp;&amp;(c[F]=d);b=sa(k,b,10,c);2&amp;d||!(4096&amp;d||16&amp;d)||(c=k,b===void 0&amp;&amp;(b=c[F]|0),b&amp;32&amp;&amp;!(b&amp;4096)&amp;&amp;(c[F]=b|4096))}}return a}function Fb(a,b){return ra(a,11,b==null?b:ia(b))} function Gb(a,b){return M(a,1,b)}function Hb(a,b){return ra(a,7,b==null?b:ia(b))}var Ib=class extends N{};const Jb=&quot;platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64&quot;.split(&quot; &quot;);function Kb(){var a=window;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!==&quot;function&quot;)return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Jb).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} function Lb(a){return Fb(Eb(Cb(zb(Gb(Bb(Hb(Db(Ab(new Ib,a.architecture||&quot;&quot;),a.bitness||&quot;&quot;),a.mobile||!1),a.model||&quot;&quot;),a.platform||&quot;&quot;),a.platformVersion||&quot;&quot;),a.uaFullVersion||&quot;&quot;),a.fullVersionList?.map(b=>{var c=new yb;c=M(c,1,b.brand);return M(c,2,b.version)})||[]),a.wow64||!1)}function Mb(){return Kb()?.then(a=>Lb(a))??null};class Nb{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};window.viewReq=[];function Ob(a,b){b?(b=Ca(),b.src=a.replace(&quot;&amp;amp;&quot;,&quot;&amp;&quot;),b.attributionSrc=&quot;&quot;,window.viewReq.push(b)):(b=new Image,b.src=a.replace(&quot;&amp;amp;&quot;,&quot;&amp;&quot;),window.viewReq.push(b))} function Pb(a,b){const c={keepalive:!0,credentials:&quot;include&quot;,redirect:&quot;follow&quot;,method:&quot;get&quot;,mode:&quot;no-cors&quot;};b&amp;&amp;(c.mode=&quot;cors&quot;,&quot;setAttributionReporting&quot;in XMLHttpRequest.prototype?c.attributionReporting={eventSourceEligible:&quot;true&quot;,triggerEligible:&quot;false&quot;}:c.headers={&quot;Attribution-Reporting-Eligible&quot;:&quot;event-source&quot;});fetch(a,c).catch(()=>{Ob(a,b)})}function Qb(a,b){window.fetch?Pb(a,b):Ob(a,b)} function Rb(){const a=r.document;return new Promise(b=>{const c=ub(a);if(c){var d=()=>{(a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,&quot;&quot;:0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||&quot;&quot;]??0)!==3&amp;&amp;(Da(a,c,d),b())};R(a,c,d)}})}Ka=42; window.vu=a=>{var b=S(ua)||S(wa);const c=vb();if(b&amp;&amp;c?.gmaSdk?.getViewSignals){var d=c.gmaSdk.getViewSignals();d&amp;&amp;!S(wa)&amp;&amp;(a=P(a,&quot;&amp;ms=&quot;+d))}S(ta)&amp;&amp;&quot;__google_lidar_radf_&quot;in window&amp;&amp;(a=P(a,&quot;&amp;avradf=1&quot;));const e=[];d=()=>{const l=new Nb;e.push(l.promise);return l.resolve};if(S(ya)){var f=Rb();if(f!=null){const l=d();f.then(()=>{a=P(a,&quot;&amp;sbtr=1&quot;);l()})}}S(xa)&amp;&amp;(a=P(a,&quot;&amp;sbtr=1&quot;));if(S(va)&amp;&amp;(f=Mb(),f!=null)){const l=d();f.then(h=>{var m=JSON.stringify(K(h));h=[];var k=0;for(var n=0;n<m.length;n++){var p= m.charCodeAt(n);p>255&amp;&amp;(h[k++]=p&amp;255,p>>=8);h[k++]=p}m=3;m===void 0&amp;&amp;(m=0);if(!w)for(w={},k=&quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;.split(&quot;&quot;),n=[&quot;+/=&quot;,&quot;+/&quot;,&quot;-_=&quot;,&quot;-_.&quot;,&quot;-_&quot;],p=0;p<5;p++){var q=k.concat(n[p].split(&quot;&quot;));ba[p]=q;for(var t=0;t<q.length;t++){var u=q[t];w[u]===void 0&amp;&amp;(w[u]=t)}}m=ba[m];k=Array(Math.floor(h.length/3));n=m[64]||&quot;&quot;;for(p=q=0;q<h.length-2;q+=3){var D=h[q],y=h[q+1];u=h[q+2];t=m[D>>2];D=m[(D&amp;3)<<4|y>>4];y=m[(y&amp;15)<<2|u>>6];u=m[u&amp;63];k[p++]=t+D+y+u}t= 0;u=n;switch(h.length-q){case 2:t=h[q+1],u=m[(t&amp;15)<<2]||n;case 1:h=h[q],k[p]=m[h>>2]+m[(h&amp;3)<<4|t>>4]+u+n}h=k.join(&quot;&quot;);h.length>0&amp;&amp;(a=P(a,&quot;&amp;uach=&quot;+h));l()})}if(b&amp;&amp;c?.webkit?.messageHandlers?.getGmaViewSignals){const l=d();wb(c.webkit.messageHandlers.getGmaViewSignals,{},h=>{S(wa)||(a=P(a,&quot;&amp;&quot;+h));l()},()=>{l()},200,sb,tb)}const g=gb(a)===(2).toString()||za.test(a);g&amp;&amp;(b=xb(window.document)?6:5,a=ib(a,b));e.length>0?Promise.all(e).then(()=>{Qb(a,g)}):Qb(a,g)};}).call(this);</script><script>vu(&quot;https://securepubads.g.doubleclick.net/pagead/adview?ai\x3dCDa2vDv4-aMnJKrjcmtUP_9jKgQ_xi56QXPKJ5eG5BcCNtwEQASAAYMm2iYfMo8AXggEXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjfIAQngAgCoAwHIAwKqBLICT9C7QjX7_rczWmcqV6DYi0W96RQFbNfZWJyxnx8gHlFxQ0YiB9n7e7BF4QCasuRw7PN3XTlAPMs8FFEK7NEJDGnage0ec9TDQR2lFp3UYAwROiF1Y_FiIqrX-x7Rs80lWY30sgf2IJOIr9YAqy0rs2jaA_15w9ogj9xwLmh8UOcuNUklCkQDLkbKYUf55Sd8-M5bzY0iV9hZRLN5Qs8kAWxvL180jXaXkuV8_GQ7h6sbWFZLSGcf9SllkyR_oHvsAlApa97p9iQo3Ie5TuA5jUfFrY_7rTG3Ol0PPWy-RTrAlsSwLnbxIfQOoGNesiXenOOU6vifu0IcHXP4QyRDyJIfQSHeKiqvKgXguqfBQoIzOxOL4OOXZrL6pHBEQeSxjEJYvaUCLj5JXD6rDKUxeg344AQBgAb3iYLOjqDZhZYBoAYhqAemvhuoB5bYG6gHqpuxAqgH_56xAqgH35-xAqgHrb6xAtgHANIIJgiAYRABMgKKAjoNgECAwICAgICogAKgA0i9_cE6WOC07ICz1Y0DgAoD-gsCCAGADAGqDQJVU-INEwjZhO2As9WNAxU4rqYEHX-sMvDqDRMIvP_tgLPVjQMVOK6mBB1_rDLw0BUBgBcBshcrChsSFHB1Yi05OTYxODE0ODIzOTMwOTY3GP_9lQEYCyoKNDE2MDM0NTc4NQ\x26sigh\x3dl6XzRpr4VnY\x26uach_m\x3d%5BUACH%5D\x26cid\x3dCAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bRgB\x26tpd\x3dAGWhJmtYvol4kLXPui846YFh48sk1xuyH3vm6TpNPa0kjw1ho00_Svlwz4tSZ8XfOMVZt-hh_jCL1SSz4U0dq_gEDKy6RGsbgiYOQ5LLP5pz8x-X_UENdVFANgCzfC4zziPDoOj8eIAZbBuu0aXah0NK1-c2mZrv96uIfXjDwL1bSiEm7eRiYS09AafFYvEwtSP_WIQQRWokkL-UeX25XtXBdfh2VCcD3XjgUZsFaI20yZnJ2kqRYpGb9jlncUN299NrslcG2PsDhAv6Kh2CN0jJdpcBJihtgKyYArljj224DWvTedLiLx8ZcNcnnfmWTYpJRt5l-KdPA9_B__KbBzvJI_na1bkjxfA_eJusmdaO1gLHnGhsvTVQXKlenUOm41SnZt8oP-8x6o3J1UU3wL1lYk5koMWTeJ92SajQNOQ-lbbHpqV-dTywNZcZsVNM_L4X9t3wR6WNrk7qevCKcai8Uo86zJY83RHcyyBqqR_YYFtW7AbuxoL5Av_V5Q2IKhQaYMWT6E3x6pDw0UU-yQ0bWTRdBRters5b7rVbDozHm3ppTVH8HcIQ_ycgZ2zFUjc3uWOg9r9PQqvDcD9ZR9M-b9VGsrW25N2zfoUxUjxI1WSWw5E91dNRYs9azwi472B4c8kC1R9OfTjCY7YGTgVIeS4gaZ04w-_is3xbgoysr2Ot7a2GwaKIvnUbMk8fc-9a7oaBCjdmEEGHOQVG_hH5-41Zz7ASclhdrcM9wTYUDRe67Jtaa3GafcmcXwvfrh2Ha_xYBPWzHvQFSnb0T2_Lb6OvLQUWt_9UyOwPTk_0cUsVP-n2m3m4Y7c8LfekJa1Lgdz9EwhtI8vPySOR0dNAR2pogErQ0BUK5HQyc_pVqp28qF8mqPTvgJDIsoyMuhler-GITiSpAhrL-yASYzBnc80gkdv3bCbo4ydu4mYUpDD7cdd7b8r8Kv6dLefoPqMsx1aA-RyJlTaEY_0weNSG0YwZzyPJ2wBWT27wYiGhxuiLHoToBsUPig3SeM8GT2x3k8V0GYaze5O3eIjRTQafjzQZrxAH_378xI9wxZnnQj3IQ7lJ0ONKr4E1WkOAJhVJnn_mgzum2J9LtrOSBzgfoCp3G0F1jTusFGOGIqMeULlK37gyBT8Ui3HjrKPSzrdIjs6MA6Ae9iW3sL4jFDYu4ZBVzsiAbaXgi-RK5mtV-oieW57BQYHA_aMK9YB81T6jW5-m50PnfvlKQtebGMxfPkniY2m7tHPdWenxFigtlJM9O3hh3pSqXplTtJtnVOOlqOf80UCRx-UKP60d7YoIneAKMmwg1PSc3qPVDgLg1xP40w99CkcQ6uhG2TTcPjxhmdqZc9UEkRVbkm6a61TvTR8R27ajXu3k_-D_CT1Tbh3Ej6B6D3bjS-NOjDAUynKFtNiqIMIaybYdtnmMKw4PU__mx89EqhKz8Z9yHgVxffgQHrg4fCdAZe55GYxWg-0-CVmBrvCvLOHU8Re1vX-X4j_WJsFU05YBRK47RUsuHBpmc0JBJNYLJll-HOjoOFS2Xlrveoi4iMc9zPvoR-t1TQj-dUfUAAbDrLBmX0fsf2h5knjgLbCBkbVgdtVI1fHy6aL-yyJGHZi6RkukO30G8nLcBTOPBc036z0dz4MMySBJ2H0cvwo_qoJCjELVkIsR7Jmgvoace3MeMByl_Tbh1DH9fbOjlByKnajCyW_FtoWGAirEyGjbOJgbZNG8HBc9JUscFBBcJ8ZZKGVDY4pGEmuRQ9LjvEWvtHjhuZo22Si46LaVL15fTN04nU37USvW1i-CK1Ey4aX41ReFESRgLgoBkWCCY2kpR6vIUZxqo_AH1KRNckXE77Qy5h_Ts4IqDIqJmcBtHaNi6C9R1YJPKGR8LQH7e5eAjlAtKYEnQ6R-hMWvtJZOviNmLIcZ7w0nguHlelt8KfHPj8bLW3s6oMUdjnJOTRzBbUYZD0a4Uq6gL3_tCP8H9WSnwEYqDDZNEXS0Lytm35X8TivR6etjmXiEgy7b89MvcRB56JnhWr9SkjDsgo7yC0Ndm2o1bNOrdf1DCrORYTQrVbtJbaraK1yhSbRRBOVETN4Ofk6fofV2t3P6Pq1hP1SjXlGvl4WeXoEkkWjgDRSvP-UrgZ4aVpw6VWhNAIac5ubmi9ou00tqxEbRCrkKkTXj_JRKx3lssqjA6Iu0NuYfjDjkHpoYvGU8Fjp2tSlfqZ1-zdTemE28-tNv4z4fO7UCbVzwcv--hK9Ir_s1uaMXc5d06S9KLlRgRAXE2KUVUE80WmNIQuusnUlo2xK6H7nCj_m7hyuemzVhf9Ov0Ril_SXiaBliMfxi-FCmksfZvUWlyDhssv8Yt0gOrk4jhkx2biRuPonhG6jcHc00jq_093tnDeXPCYRBM3BakWRyQAPefdaNfjdE_lLN3A0a4MwCynv0odiJ5x4LsSQ-SCZYPxsAvjsugwQWue9FUFpWTcz5wGm6OujWUsMmanO3I0vcLJn5HeEgXw6oDsihNez-5SzeKxTLWzPh_2jLaTtsz3aS8rBzQq-6EwLnAtIH2bkuiqAHtGZdk-_8wR31IruREnRNVZCtg10tCjhHGJKgpFS4irGjOE2J3d7K7GYzAPKDDkBuduAIAYSX7kF04ZWfCB4Rpjg6dN__RfjXa3DTpwN93cwuheNEpH2xwnvNq50SLcDAxXZtqdjTNrH6E41kZGRn38nvQ9pQ1pVQYHPIHGTajXUCChezh6sqXbPE8Krfsm6w9FKRvANJIYS0uAeW7dyfdPcxv6rJJSA052mynlR1tUu42IW6VYJJBAgp2w488aG7OsKHlqpchBVPp3CxZUTch-vOD2E_GZW8k4N3_F2A4V5AjRXXT3gWCPfjs6dPVb70zCNQXFoBVIGXzekworDDvYfZ8-0s53y5L9Oqx6DG6AjR4pwUEEhBSkuZFXCJe_MCKqZYX5By1ewzBMyH-m5Rt60Y5T6dGoleBBR_ozhlQ5CYJ4Q5tzWsQIZ43EoGzZSvclOxMKrbE9Dds80gJNONCbHCILo5sqXZvZWqg5o9Vz0c7zCU84lNciOKcp6-Ou73s9OMYL2SclItXnKlefUpMZvmY8YmfJx8y0pS7Vkxq8DGmq_cEEVsitqjKvQEEEGJNN6OGeY0r45RN0NZcmc1dHuO-td3oVIs0zEaniJTSfUmlByeagt9e7_Z2VrTDYuGFrwmv6iy1rwwv8im_SPTf5wKdlfftaoOGyOBQITqOY79ioDlruPFsNP5OL_EY5nVY1v1UdBAKog_Z3yrPphWZBPWG_3ledzhYcONvW3P6WY8v41cCP6kUXZa2zsUDfz9yoOX1Gx-58uUSc8ZLKNpKpur8-82dE5R8Fwq_3aXI9-gkrvsS4eaaPSltAauFof5YUc5Ryi9WENfSNe0xlYx__g-l9wAHXYbG7Y5eXo7-OKfF6SX2bU49sOyXmu-eG0TRvZZUmbdrcDZ7xMnT_ib1NomkbixrBcYmqt-hjiKAbvCI-qE2cohi3E9LmPkf0kJuh5DWHIUwZlo3lAW-cd4ikB0S-pGKaHoVYoG6hx_aO3-Q6JdHVFdeaTFGb9nW0iMlZpwLaYAf-k97yM0Jr4GZSb-XMxH68Se10rZSC2EkMLhDJ9tmlul14HKXLN9Se3aGD5bvaFQ9HKziGkxuKzQSUmtkKwPFc_rn7TP3S2no40aiYGhKCSc7LU2B7h-V6QReOuttnUQf6AjPs65hBdTcTVglKzoUYeYf62wvkqiteW0npCRfxsZxYZ6-6p7MurHNdZ_e5rBJyopM-5vQHX6WDJRw6bbZ1KkQZLKR-qrUypBacfFzLIbSzcnQovBSsdGaplgzC0DMBIyMk88rMBOQbqPMOXRw5-uGnLuium5XxpEHqF_kcIoUONvdsE2TSkUvnO7gs_jIhZ9YgZVI27iXk9xV_7s0ErFwKs9zlHELoxVHhT6zRyMqvc-jJtcmWNE5q6rjNXr1vLahPmJJtJSJxiQey8ywWDPcIXQInMzoLNzkm88SD7LGD0XNzeW-cpE0i0vJCmRE7SHs2ucSwVzKqDAKFsJXCPw-yUbv5rCamsh1iKWu2Cj1dFVQPmddfLEpe7jp6WHZl11CCcYZJ9tHr1lFRNxR-2rprRpFsfrO6WlDP1-hJWTykeya4-OsuXWiroYaYooKMHfe5U3coMwxeJkIJcMAB63iDEhGarWI-7Q-jvsyx27ZoHkWYxr65n71uoE0NqdNY4BdFQa4urdMfG4wJ6kYpPHs_y1b9Brjift1Ju_zwExIbHOw6xfl4F7ivkbGu9Hs95ruHBkLtQj6i6lSTUfBMKNb10DAqzKTnkQf4CRQclTwiNWJ0PIBrGXtnjlguNRBPNQ4vmeghvRqLiQwpFb9ztK3-cNjoaVRbGIw9Zl1T3Px68G4dG5S1_DLWcOP73JEfy-zDKRTlrart0qPgzTEhg_ecdx2SnRaJYz5LKQN7zuf-rf1_lkj4-0vXtEShWmgkuBia6eutjwhlCcOe2q7hetBqDx4rih47mzaoGiQO5_XbcyIvUhWpdqIG9HuPjvUSdAkhbSmQj347QW4T6boikdHEVpH9VNN2uDpY6u4Tm21py_eaS_Vcr1xw8u2BDg5Y1r9D_2uM_vGck5TcqobxXCc3Ca0GkZKRv48Viv1Lb4Jh0HNpsHhJ0e_zdL50AOKygjUYYmzPA9JM_VwvccihkeLdVzdPZCrHF2Q_VFAE55Qj6uqK-1wMQjRrmi0vQsfH4x3S57M8-AmDa9wcRof60AKtSmKlZVMfKQ1XqK9wAS2h8scAZPevxFFWVUoRUzDXPwQrCPhErd7fbUcLkbnnMy9uge8yhd2ccFSPpr7riPmqmFKwZSCL-Txe8hbRdH5shHXqjHndHfv6fry5VqIlXl6Cp5MTq0bxbHTXIWAxHVO22yW3DGeLqTLHjlB177zkPk39-ZTdcMaZpB3eV-IdZvophBsDqy9xg2YZ_G3RBf8ccPdlIaefSJ8R0mmcpC3o--L5AQyBDGo6QmqdIC68s80nPjKX7WmEdS87GQkcxImPfuO9lnq8zPTpC84vGXzc5nOpZQEMKOarw47vtpF5kMQHOAdh-wgqHUeDG7KAMlTFDg7TxmM6RjqEsLtCfiqpIpti71Ks9u2-tL5Xu-7ySlXGuBMm-pxdBiciomj1hUQg8qqZsoH3EIUtDCt_ZXJfhBeFcpfmJKYbvT40OKgtjMMCvN_cRjAj8l1lDbuR4c1drXGhrRhiyGLBhTthk7Z-cxDSIBpdcxpoC6NAH_tQgsrBZd1ZVaCVOq9aqAH-Vd-R7hxReZmhFZn36OncS_uAtkrrrqOoaRqqJti26zqsM-6SmbUkl0dgz_HoSFFhPGzKHg-FniEEPowj9NbGVP7VR_ImrgfROXd5LtV7YidC0nt-rKw2LcVleofCA4wjSWaV2oH8uT_BEsCEzQifw1My1mm6LeKHeJZK0ld4YbHWvtUwiy2jEB4TgoRgDUdvIx-1UBhmQicImDJKUsfWch-9mY-7mtYTV9HHvS_-QKKKcHNfuAQX6yS8iW-obXR2dMElzn5tvsKyj3rkyjEmwZy5lnghchv3CfCBpEPubmJUEvQ9PaNf7_e8D7zfOLHwG8W7bE0q3DaGxiMFRXoHygcOptxUa2PBu3Sc86j0F8neXz3zXPLSsK2uLyV_jIck0WCcCEX-9J9MgFq2GlIFsKPuaYS0Y7tPbyLGzKcktPeCJBQGQl0EuVN22U-Xkc282PCEXFeXFTggWSF9XfcA8TzUgdboU1nI_nnzF1RbIJhwxKUoXDlsdQlXxlWiLTmfuYJ_Jvf_U1qkDk2wP3q8X4300SqfvXOQtHzWNTz5kxgQx-HqP7uaJxUh7fDlSA9QGbQro6lxTpvAPVKkdC6TW8V6pVIw4CQcTguDOQIoKNIZluTigAHB3242xINLgsq6cUnMuVb1rD0hgBYiJTqYYJQV6HFJwURuBE3VSajT2PFzkxOZ0yA_5MS9STxrmGUNHScvIx9LuZw0Ga4Hp_9SfdPiHJaCXfLQkLteL6opRMuZMAhIZoCwZeg4oyLpugCpdQ8k1O7hy5Nl9T0vW1NynGSOENJDGZCGjaf0_gfvKAmOPXtgmUaj_sZqHivToeLT-JUZF92GUn2XJuhf1RUPfxWpcMyDsMmCzXPhTPK0u0uhRKweiM5vG0fZJOeGBEzJGAw7NxZdNB69VMiX1f1hjuuB4m5p4Bk6IEV3Xf1fjMo1ldoompAeqk1gufKqgJRTq_joEN4Lo6ZgP4Qw4Vy6wdlN9tVt1xXmRSnnsBF3Jmzf_5rfgVsEYROkZvVVH4BHts2whTOpyp3_6kGkWcDposNNgMUOBCz3ATyv1egpAoDERyLg1ed1X3ECPN692rb7vIcRkUJGvQIlN0AOXT3NjzVIznpZX0LnBefyzZncTDv-pojv53IN3b6ceIwELqilxV_CSNnRcpllZegUtVtsvrqqA1JHShGnr1Mb3ZQuJ2PbFHlxnTLJFuecSFed6ujwKFltXAKKJ1EYm0IWKkygm-Q2zMqybx5i_xcp7HvF2dLHZXjXwXpMWWNTH2Hd4IYbU94j0Xm5wf1TG36YNra6zR9u317Ega9-QTtwAh6-JMs1gNw2_Sh3HuEiYeQTJr609jSON4gcDIvluKAv1orqx104D-zxB9OBvN59xgwsjD1zLmF0ybV5J9C5Wj-Wl4kCUD_2PrEQQqkdweq8kb2kL_Myr81-RFK4eo_Kmdl09sPuE1kyVQ2gB7WBTBfWWyi1ovBO34tBg9gFcSUnbYZFw1SnD1yP7lU3L0qirkOVDgouFNCdg6gCI5R0i4jJf8FBo9-5Qpydx8ZstumxdxsiQGTpF6l6-kzp-VRw1eVPofmwmiasL9_4vTs44MnXqwFKM2Ni4hypHHFA-jeVWOznhXb3gi9KVSPQc1avTa9BP1r3uR_aUdZfcYnonERQcFqkBH7dIe5ZxrrxSGxd61_TfHJ0pTyu6tAJLxvkMDyc94TIOS3xplgvIoY6SBPuyLeTV8ictQwmB2eShwoQ4M_0nUjnF68LsBoPWIuM18Yg0k5T65xs9EvaV18eVxVkU6OTOSXO_NNpWedonaI_tii61dboktFIJO52rCuixuGGCC7N8ltTJ_tKL-BnVPmqcmG5crvojZCcwOmNiXTIWid3dtyUYflnFyBbjS1iG6wnRw8prCGAyciU28lihWmmncxfFiiEJuEsXfoOFlXnPDN2NkZ8PC6ATi8a-o9qJ9ebhUHD0efjOwKAPt-48J0ra1Cpmgz1KZZpEoJb3iTfXkxpAdTgloVzFxmXh2TeA3G-B-bZ-B4pr2lnyYtvuIqk2h5xfPzVJp1vmI8rBToc6heLn8yS32e7qzhzyUuWN7HB04rHYNzNvY3CXJWgEos74lzm4quJ2DK505Vp0-6tTOeyJBcGuvdcL4AVa8tjTAOJydLWqsaKPSv2KpPlPSHZplHckU4pVoEC3fIs32cy2PXkW5thZ-2lQSu5WJsmbKwV8yTqxpqOK_cQPP3Q8I_z770r13ltqnIwE5TASxLjq91wcdcLo-2PY1Awq8wqbT64YxytyQmUljjY91TTMDLG2_GIIDBSwZ0F6calRPk8XVQNsfquaNJ6DyYNKBP66XJCmC6gGKabs9K_w9AzKq6ITIsUkrKGPTuY5XM1Cw95XaiNgZebOGvf2lrEuWvOMG3LzGPrS1qEkoVLmHPZI0oR5FcUuHtl7zakju13kXgAETXWOL_5stpkv2dww0oyVWehttGe2de82csbPWvk6GtXgVlupkvdPtaky040NBFR1D4MrWAsP9muel4XljkNAeZZ8IdRDtgta12UovXf-VJ_Yr0tMj11nmOroBp745FK4bqlqNtK-mMKxHsBGCStnoqEjJzuikl2KIpkCTL8dqL8r-5xjuyRrdqkFPLCisLSFtaxTBMdnbMKdQaE5UVpL1GxRGw5jO6bkR78DEbIugvKKXXea1n7vQwRYcLpjqwAE7S4JLsXscqBz1sdC8Tj5C4t6hsNOKsX_KEOJ_sUzsb8KWkVQl9vIV2FkuRNKP8CUsj-xYdc8L8ZC5_UxgmA6Z_lsYfws4zmSfep_87mJ59EXgPw_NCXGyukbPuZBHG7yQ8q7aBxNIFx9rkz8nmOAHzjmdo8FD7jJlAOHOVdU9Pz3L3UvEdA3MtOSKArDwj9UQrZMdGy6puum5nGvJGmfaTmsJNnGDSpd9kFhpfMgXFbsUHDroEdLuf9MHfcSjeiOGh7OeLunuFCG_hg1HHDBh2gb1NkuDZWtjpXc2m7NvvWbB0RMv52Ff_u1CSuf31Hy2W_FMNSfxQKDc6zHfCGzQonsfdKSMw69gc_KUCdwsRSZCBMlOeBKxXMNxui6xKWAEOFpQuHN-8FkLkfPqUwe70whg0WORVXVV3ZrgOf6qczmsRtRX4ZpXbjNByn6IYvqnzqrTPxE8uQOlQMtcmyLT7EicveX981jlLf8XG64wFcgsYbgQj-8qWYDvuDQ7pS8FI2hQXQ3XHRcMN1BECjz_3peVSnI9uwPh0CSjaswFhICiURvzvtsW_RYnWqiEFvRgZ_du0Pi5te9jsVT3rw6SZQ_2rcHFjHzI8WjFHhyl83CdOTQBBOiY94Ohp0PhBDCUwt2dhf5OpO14MuafHsBIEa3Eu1SegdcgvR3J26ua8QQyLk-eyTXEIfoU0TM1rGUwbpAI8WML1pateRJyjo9sMYE636-4haQFIJU4IEbd4-8HzuAosBG-WBR15DEv6DZCWYXAWu0QNDfn2_SOVa30B9x_DDxChfSlezIeEERg2YJ4a8cFIojJPXdHrrGo0I4ktdJlyMhYR3wM9v7cdRk2nvt29vP-4zhd8bFdtz6kHa0-kS8UyU5IDi3RlGo6eL-xI8XXfxlsCXOWO4JJLxPm8nKOG4F_mSf7wqXzB6pHj85Vb4lo_Y4NadUd5EfiwaqAqQkiQ7IelOpIIy-ooUgBlHybqq79ieVcqEMj7-rpiKa4-FbOrKDd1qaCkNbcqAt1md8Bvj6qtp5cfCt8VzCXqH1Hxz3qOJ8cvqr3Mrzzg4UbwL4Yb2Q2GD0FcnDptFAZPPL4KprWWba0Wy7GoQAW2InE4rsfMC8KaRLdVIDSbnKnDXSTnpgpr6OyPnwLanCzMn-vHrBLL6eFNVKGQGjDBQAtkOewsraDDZxXabC6u_qVgRYOECxWqX1ojZ2ZCyekV8SQ8EDCMs6zyy9WQL-boC7q0nT_u6LCZCAkZHs3VV5aDljMfZpOl0cg86YbjlzB-G3-JSonYgF8ZAjOPhf0mzpz5QLiiuNDTLD-2UpuEv-GNyW0U12afCWC&quot;)</script><div class=&quot;GoogleActiveViewInnerContainer&quot;id=&quot;avic_CMmI74Cz1Y0DFTiupgQdf6wy8A&quot;style=&quot;left:0px;top:0px;width:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;&quot;></div><div style=&quot;display:inline&quot;class=&quot;GoogleActiveViewElement&quot;data-google-av-cxn=&quot;https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjsvkfX1AwtQ4I7Q4P5C-xyr5RwHLrauA502eAj5jYJwgkIt_9SKaaF7XfXWyj_sgs6aimiyg_rbYCzJd7YmfTnSh31mAZPyLkQZGIZJ12C88rsaZT8KgXyYy_hr0zJO1ozF2v0UiJlCZDpWyr0-JKt01DWlF-J_zQx-K_J8W7h4&amp;amp;sig=Cg0ArKJSzDcAdQDBC7uMEAE&quot;data-google-av-adk=&quot;30323315&quot;data-google-av-metadata=&quot;la=0&amp;amp;xdi=0&amp;amp;&quot;data-google-av-ufs-integrator-metadata=&quot;CpkBCkFtb2RlbF9wZXJzb25fY291bnRyeV9jb2RlX1VTX3BlcnNvbl9yZWdpb25fY29kZV80MzRmNWYzNzM1MzEuanNvbhIaQ01tSTc0Q3oxWTBERlRpdXBnUWRmNnd5OEEYASIeCJgWEJKlARjV9T0g1fU9KAIwAjgBXc3MzD1gr6AMKMz38un4_____wEwzPfyaTgBQAFIAFABEpECCoQCaHR0cHM6Ly9wYWdlYWQyLmdvb2dsZXN5bmRpY2F0aW9uLmNvbS9wY3MvYWN0aXZldmlldz94YWk9QUtBT2pzdmtmWDFBd3RRNEk3UTRQNUMteHlyNVJ3SExyYXVBNTAyZUFqNWpZSndna0l0XzlTS2FhRjdYZlhXeWpfc2dzNmFpbWl5Z19yYllDekpkN1ltZlRuU2gzMW1BWlB5TGtRWkdJWkoxMkM4OHJzYVpUOEtnWHlZeV9ocjB6Sk8xb3pGMnYwVWlKbENaRHBXeXIwLUpLdDAxRFdsRi1KX3pReC1LX0o4VzdoNCZzaWc9Q2cwQXJLSlN6RGNBZFFEQkM3dU1FQUUSABoAIAEoADAEGh4KGkNNbUk3NEN6MVkwREZUaXVwZ1FkZjZ3eThBEAU&quot;data-google-av-override=&quot;-1&quot;data-google-av-dm=&quot;2&quot;data-google-av-aid=&quot;0&quot;data-google-av-naid=&quot;1&quot;data-google-av-slift=&quot;&quot;data-google-av-cpmav=&quot;&quot;data-google-av-btr=&quot;&quot;data-google-av-itpl=&quot;20&quot;data-google-av-rs=&quot;4&quot;data-google-av-flags=&quot;[&amp;quot;x%278440&amp;#39;9efotm(&amp;amp;753374%2bejvf/%27844&amp;gt;&amp;#39;9wuvb$&amp;amp;56533&amp;gt;!=|vqc)!273794&amp;amp;&amp;lt;qqvb/%&amp;lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;amp;&amp;lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&amp;#39;76463;21$?ebkpb$&amp;amp;0366717&amp;gt;*&amp;gt;bgipf+!3=712363%9aihwc)!7202&amp;lt;217&amp;#39;9efotm(&amp;amp;20061;48&amp;amp;&amp;gt;`dopb/%&amp;lt;1707200!=8(&amp;amp;2005575?&amp;amp;&amp;gt;`dopb/%&amp;lt;170642?!=|vqc)!7201;=50&amp;#39;9wuvb$&amp;amp;03641654*&amp;gt;bgipf+!3=731103%9aihwc)!7200?073&amp;#39;9efotm(&amp;amp;2004?51;&amp;amp;&amp;gt;`dopb/%&amp;lt;17&amp;gt;474&amp;gt;!=nehu`/!36406412!9abk{a($167745;=&amp;amp;&amp;lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&amp;gt;7&amp;amp;&amp;lt;qqvb/%&amp;lt;104=460!=nehu`/!363;42&amp;gt;7!9abk{a($1656;3?&amp;lt;&amp;amp;&amp;lt;cbotf+*01011776%2bejvf/%72&amp;gt;17266!=efdwa*&amp;#39;7616?=&amp;lt;=$?ebkpb$&amp;amp;0335225&amp;gt;*&amp;gt;bgipfz&amp;quot;]&quot;><div id=&quot;mnet-vtgt-f4892c4627fbb0257c220fbc1ed34dc0&quot;><script>(function(j){try{var a=j-1748958734990;var i=Math.random();var c=false;var k=window.mraid;function f(){try{return !!window.top.location.href}catch(l){return false}}function g(e){if(a>0){e+=&quot;&amp;utime=&quot;+a}if(typeof k!==&quot;undefined&quot;){e+=&quot;&amp;mraid_version=&quot;+k.getVersion()}e+=&quot;&amp;sf=&quot;+(f()?0:1);e+=&quot;&amp;cpr=&quot;+i;e+=&quot;&amp;audit_scanning=aD7-DgAKpMkEpq44ADKsf6E4DitovuDnajfwJA&quot;;e+=&quot;&amp;audit_cur=&quot;;return e.replace(&quot;&amp;&quot;,&quot;&amp;pixel_len_bucket=&quot;+e.length+&quot;&amp;&quot;)}var d=&quot;https://hblg.media.net/log?logid=kfke&amp;evtid=plutol1&amp;__q=AcYEewKELAQCEAABAIAAAgAAAABAAAEABgAAQIABAAgAINCYCXDGAVQzNTg1NTAzNDUxNDc1MTdfMjA5ODE1NzMyMF8yODIxNDcyOTkyNTExXzBANjI0ODVjZDliYjYxNTBiMDEyNWY3ZmVjNzRhNzFhODQAxueJjQL2A9jTDn9N1sg_SgwCK4cWyT9saHR0cHM6Ly93d3cuc21hcnRiYWNrZ3JvdW5kY2hlY2tzLmNvbS9waG9uZS81NjE5MzI0MjE3BFVTMnNtYXJ0YmFja2dyb3VuZGNoZWNrcy5jb20SOENVTUROVDAyCA4xNjB4NjAwEDAuMTcwOTU0FHNjaHdhYi5jb20Od2VzdF9vchoxMzRfNjE3ODU3NzAxCEVCREEIBmFkbQAAAAAAAABVQJKqg-HmZQIxAAAAIFvyHz84cnRiLWViZGEtN2M5ODc2YzY0OC04cTg1Yi5PUgIQN2NjYTNhMGQCYgIIZWJkYSIxMzMxMDI0XzYxNzg1NzcwMUBmNDg5MmM0NjI3ZmJiMDI1N2MyMjBmYmMxZWQzNGRjMAIKAAIBAAIxBjEzNDJzbWFydGJhY2tncm91bmRjaGVja3MuY29tAAAGMi44&quot;;function b(){(new Image()).src=g(d);c=true}b()}catch(h){(new Image()).src=&quot;//hblg.media.net/log?logid=kfke&amp;evtid=plutoevents&amp;__q=AVzjwLwAACAGU1NYJlJBX1NDUklQVF9FWENFUFRJT04yc21hcnRiYWNrZ3JvdW5kY2hlY2tzLmNvbRI4Q1VNRE5UMDISMjgyMTQ3Mjk5OHJ0Yi1lYmRhLTdjOTg3NmM2NDgtOHE4NWIuT1IOd2VzdF9vcgJANjI0ODVjZDliYjYxNTBiMDEyNWY3ZmVjNzRhNzFhODQIRUJEQQJiEDdjY2EzYTBkBjIuOA&amp;error=&quot;+h.message}})(new Date().getTime());</script> <noscript> <img style=&quot;display:none&quot; src=&quot;//hblg.media.net/log?logid=kfke&amp;evtid=plutoevents&amp;__q=AVzjwLwAACAGU1NYMFNDUklQVF9UQUdfTk9UX1NVUFBPUlRFRDJzbWFydGJhY2tncm91bmRjaGVja3MuY29tEjhDVU1ETlQwMhIyODIxNDcyOTk4cnRiLWViZGEtN2M5ODc2YzY0OC04cTg1Yi5PUg53ZXN0X29yAkA2MjQ4NWNkOWJiNjE1MGIwMTI1ZjdmZWM3NGE3MWE4NAhFQkRBAmIQN2NjYTNhMGQGMi44&quot;> </noscript><DIV STYLE=&quot;position: absolute; left: 0px; top: 0px; visibility: hidden;&quot;><IMG SRC=&quot;https://pagead2.googlesyndication.com/pagead/gen_204?id=xbid&amp;dbm_b=AKAmf-DPIRlD9_vUrX1g4bc7c79So8n1bXBKf3DF8f8VLjqBqthbSoC9wa172LzIXRq8TxsePMSwhAY3PiS5YvlpvgBHIVkEnhVg-oixtNSnP6tweiBfk-E&quot; BORDER=0 WIDTH=1 HEIGHT=1 ALT=&quot;&quot; STYLE=&quot;display:none&quot;></DIV><iframe title=&quot;Blank&quot; src=&quot;https://googleads.g.doubleclick.net/xbbe/pixel?d=CIYBEM3zNhilhc-mAjAB&amp;v=APEucNUA_i1d7WshyrvPx5YeWirunyqrRXdKqC2mOT4PcLvVuiD1cRKokuKulwxDQc2KZ3vrRPpOhshZsCPUG5J_eQPmxIgz2M1GoOYB9ScHjatg8HTStQM&quot; style=&quot;display:none&quot; aria-hidden=&quot;true&quot;></iframe><div><div style=&quot;position:absolute;&quot;><script>(function() {var u = 'https://googleads.g.doubleclick.net/dbm/ad?dbm_c=AKAmf-CVhFEHfqT3tftEYQ0MOnQ0KTh4BTofXLmP6LV2LEkmyLtdBIVkUPXBrpfQk1DVSfvOBB3qz8zJrGuIu2r5TRTa06fK2p0pJqG1jVfG0nCg7NE4pHLyY6slRioQaFzoS8pKch5RVXSxSfAz6Y2UZetKeaXz2VFH72w4LvlfnkIVVOeJDMEAocvDd37vpuNssgJXJsJh4vm-kab6EejUxiXIWS1gdDijy06fNsGAjoBJcWTD2MiOz2lYFeeccw7IVt6MzCnF37I2zP9QDYdyv6Zopg5PdA&amp;dbm_d=AKAmf-Bfzm9GDU3XhwTrrsKTfugDjmEI6gjFSK2zJTUNuYPcjQ5qxOBbleKoUZFDGw2zo5J7yYf4GHfK2tnNJdaia8_1N8lW1H3icH63_N3KsL-CI2aEF97xLDaan4pBA21jgUk5RV-ndTlQLPcrGxQZ8nKz7jXHuagI8xmX2wd8xXdPFtPn_cLzBXfvix1sOTuCnAE7E1YudrMR1eXgWb2lRE79zkoSdfq-utanrqWWJ87zOKjGF0T-7hhfPXfpU_dLmY4JFmGiNJ5NeTosDdzGCNIOq9NxZalCPXNj6eO_yFUs_4hQhm1xSdZvas4RcVsIGDRno1pYO9ufbGhBqWJ6Po_eyroJo7pBGvbROV8slzikfY82exMU3U1xxpalIwBnZ4xwitoMjqANMOmIBzocqb-Vrtz9nMMgXO0bE7S-sjBOTeFDXpLay2hFa1_PekyXRkRwunx9CB024Vc4CKlwR1Web9eLNRb9-E2UaKeumVe5dnL0uHBDMDCNX2sBxW2EkMLI8tGh90c-FjpfftqYIZhM49groVPpHr-U7oUgXAqE4Dyp9VraK4t0cJtgfBrd9dQI9QtkPPOVx4EGJJS62a8gUmulC7l8KE1rYxX2sxaTzATJ_kOdQsFkNiRKn95YQrDUbGD72y4S56rEjl8AZb3W4E3lG_2Z2e5vo3aANxPrX-_SyDSPRga9WdQsPwYjPXaM4gbZP8Nuq-cPF4UfSwHohKaAPzCajr9rmByGEgNJ8xDBB0MDpdsaYpD7cJ4fIMjtGcN4zlR9HPjc-KamqQ3hC26q6WM3vNW5OUvk8EAmogD6TOcSaprO1Dp0l4MzSohRB3TrStSVXuHF9dDJlK1-qK868C-aDXCvhILGvM7pKSVhrWVoe1AjdTEzEf_i_-KgvFcm4-0PVo-3HT-kBbbuSDGVPklG8D7xRpR5USy2EH5E1kedErM4jS4vEXasNsg90Ik3EH_TdzoqyEDhnCMeNVkjHLMtHAcao4abAvfu5xmaWGX1oFSB8VZIYULoxWr7VEwQ1umdwMdLMTWe9vTdv1yX4yonPHTSmDZsP0OzY6aASws07lbliZVTzErMFirI-VXud_Evh_SfMg-a5luflKrUlIdvrMpHXw6mOdIwMCssTOacgbk3J7o1UD-rYVGIHJa_38PEzuR6HNyLpnTTepYBl4qcx07j4ef2JxDeHWlC_a9GqquiFimXEF2j4yCHR-ScSq2YlbL6X45lOSbNRYSS4jFoRcttcvmzpgOy_scL0feANNhwEs_pnc8cNUOo6ry1Zi_ypeRFxk5ZrtofQJ6tEnBtMxAQBOHzYl2h7UiwkvdG8z6J_tOt7jFO26O9vNYMK-X6HW_7g1mFCXr6ECt3elGVYH1ucT63pqJpfeyi1uY9Spz_26eapiuoeXHaXthIJyTxawevUV_4dJbFXuPeHIJBumA6C_qBOwBfr6ceX7EFyI6rEC7rf4HOwapx0KliWvhAJYqU89GU4sYne7gKphkzyyLXaJtIiHkYc9waL1V9nGaULH7M1K9aAVkhlkAAI59F3DLIm-sWJR1T52H-RQ3cmBE9vaezVoSlERj4xbqS7o_FHIAPzIfmTc7ElqC2XVZSIZZE3rbXf4uuYi3zffOfS49xSas3kgUDYpSDDkem9xoM3paSTx2koPS6AdfNob2hof5DFkrqE82Z5Z82CmfcSJVpJ2-sxf6-QHsUlJRXqDEDoPerR8buWMSFdj6NDeqwwJgAHAaioiHnj4FaGfVIKXnr0BbZiNt85DwVHPxPVJMcoRtyjXs4xiua1ugJnyHlJ0wRRyEJkSp4FEnfdLKlitmmHM76UoDTo-eN_w9xzrBF49HroUyD6UoGmc0FDA_o5BabXpELU0SPqopmdYmyu4uinXq0_jgLgREVbwGuAOVzNLFVV8C16bKyBYyM3eUAJ_GQOK9GCIhoMtdapTQm5qWiOepcf-ZsLuJ_1_4lgRqkaNXoMW1hPCiRZvM3wsM9d8fGIOFhhISUZFrLaTFYZaIe6h7XaE1scEdEJZtTBnydqoR4UvRIY5nnvxa5sd10RGWMRHF4t-14UkG6Nly7kK6bjS6kJ7eCrWX5lonkHEqXECbaDwf1MK2dFjhBDSojd2XzSL4A2OyE14pPr9wbqn-VJFtN0qaGNCsuNgoXUQ4bCiqrv5AHqLaq389EWH_5pquPNwldVOMXYXjMNcislUXI74up0ON30WmAeo7CV-rtTaSXK4YDfv9vwe1Bex2IkZjuOEndlfEG55n49uAZsGZ7YmSfE-4FHA1K66em-efCIWFPWa7OpY1MYlN9jPzJO6qeSL1Py6TEsbHYXVBSE2YGpoilAKpVlv3D-h8FLJv9xgeL1SMiC9YWZRENYI0iUodAb6p5eg0CiiF6LnYg_bQuqjxf7WyVpseJ0cFN7oDNUa8zvEs4HmgRX0RHNAVU8DKcfRRNo4JBVaztHgg4Mfb_pQmpFik4EBx6Snxs0wj2TCMgxdT0XdOKCd3YaMZH4qodw-Tp2wNqilJq1ItKKWhSxzj_xNMWxvQcvjqMLvWlwAa8b9RTkqK2xwaQmHwpDGlRteizdfdEJ8YnKv_T_8SFgyh2mCNIDZq5nDTmgb3V4tj0FWpLF1uxo0phZzYFvb4p7zdA68_s_Dlpd8DjmAAmsCzBCG7ocw7-CeEK1tseN8y6hq9MYz0WD4F8LaEBvRA4nM00092RgMhSaWH48lmdoBbq-SP4nKCsBmUXMk8zhmO8M3ECrZETqf62TDENypcusiZv1hn0b2LUG-v9uOGOspdotxXFwKvM4JsRjONLfgRoRvZ6dSA4kIGqQLpQafX9rg6pZwmzOgsxCbW_gUtuNliY90m0fTd2yjXf7WNyxeDx7KrgEHPjWC71x7B0YJeQmD87Riz9liNI6bTkC-b4CB28v84jvLHM4IawFTQWcO2jUQfXBYaC9-ElTWfnsglNB97A_AJeF3DXujPWuVKrlDBRH1GrOzOGJKcUO8YZgry0aLrK1Loqgqziu4tecvm_iqmAieYe2YdfpnGYFrC0FWsY6SIBH2nugoi77OmXcrZpAo9PWAj-qmY7usaw-GXI9n4uzktnQhhOJx7H08C69ztNwCktjeqnjoueXZlBhKkGgwAoZTF1tjRrbFHWJpiZ0E3m-VtXhlMIv2MjoO6GUwVNiLDcdwvs1Dr5g45_rk6ja4l-RDE7ZNqDS8xvSi8dwFDlXe7GoEMATq_Z5O1NgjbHhAixjxpS-nGJMaEb-6ORlWgGsUEdCgshMuD4JH9T9EYkhMYwvMQOyhxsoU70PSFx270r-uhdoF12C9KB_3E56qjJjEHejt89Q1gWXOdTJ0ahixOiVKfiVpPr3dJAmjiWA2tXBrToEzsSkA7Lj1061C-CKAZT0qB1kkTJ6O8EcrO5jPeq4dJU00SqzdYREwngYcBouE9svN-w8vqhF6eFDGBiGNLl9ZjqBx94VM37KG3V0Tkkj2hWD8skynCdIZNmPBnYLlr02Ixtk8vK0nEdhuaV-sdgh-R96X7U3RwIn8SlD81PjI7wN2J81uH9bXfATtyLT5RyWwxo01ui1bH9dfu0ShFyXKuZPTpmaDlPXjPTzok5MA4nSWwcUx2i3eA-XujvRw_70vuMPXQdZlomzZfHA-gr1D49a7qRTNuZNcnE73VVET2slezDvYQUa9FWV0d7uLOFRGYLDsI0IB_9VKfUWBSKIoJmaobHGr1RKoaM_Y_f6YlUfo0U83CdBaU9ZVqKYiR1yn2qS1FI7v6UR9XqX1SHqAPbcfbcDDwfpoDnkD5uWzpjqnxzDEDGrVExxITd0z71m86N3BtrUZnY8xBcyNKWGtsMSTTcWbvbOLcgSSNzXLQKOguKIY-0tg51DXkrPmnYvW7Xe9TzhEHpewwKMDuPVFsR&amp;cid=CAQSSQDZpuyzApSorbxLdHMxo_UOniwmJoudm4pXqjks9sins8byFOLVwl45a5_7rQT_LfAjv0LfPy7gqVAVj6fk20xqwu5CrHCaH0YYAQ';window.dv3Utw = {u: u,w: function() {document.write('<script src=&quot;' + u + '&amp;flb=1&quot;></s' + 'cript>');}};})();</script><script src=&quot;https://pagead2.googlesyndication.com/pagead/js/dv3.js&quot; data-dv3-creative-fetch=&quot;https://googleads.g.doubleclick.net/dbm/ad?dbm_c=AKAmf-CVhFEHfqT3tftEYQ0MOnQ0KTh4BTofXLmP6LV2LEkmyLtdBIVkUPXBrpfQk1DVSfvOBB3qz8zJrGuIu2r5TRTa06fK2p0pJqG1jVfG0nCg7NE4pHLyY6slRioQaFzoS8pKch5RVXSxSfAz6Y2UZetKeaXz2VFH72w4LvlfnkIVVOeJDMEAocvDd37vpuNssgJXJsJh4vm-kab6EejUxiXIWS1gdDijy06fNsGAjoBJcWTD2MiOz2lYFeeccw7IVt6MzCnF37I2zP9QDYdyv6Zopg5PdA&amp;dbm_d=AKAmf-Bfzm9GDU3XhwTrrsKTfugDjmEI6gjFSK2zJTUNuYPcjQ5qxOBbleKoUZFDGw2zo5J7yYf4GHfK2tnNJdaia8_1N8lW1H3icH63_N3KsL-CI2aEF97xLDaan4pBA21jgUk5RV-ndTlQLPcrGxQZ8nKz7jXHuagI8xmX2wd8xXdPFtPn_cLzBXfvix1sOTuCnAE7E1YudrMR1eXgWb2lRE79zkoSdfq-utanrqWWJ87zOKjGF0T-7hhfPXfpU_dLmY4JFmGiNJ5NeTosDdzGCNIOq9NxZalCPXNj6eO_yFUs_4hQhm1xSdZvas4RcVsIGDRno1pYO9ufbGhBqWJ6Po_eyroJo7pBGvbROV8slzikfY82exMU3U1xxpalIwBnZ4xwitoMjqANMOmIBzocqb-Vrtz9nMMgXO0bE7S-sjBOTeFDXpLay2hFa1_PekyXRkRwunx9CB024Vc4CKlwR1Web9eLNRb9-E2UaKeumVe5dnL0uHBDMDCNX2sBxW2EkMLI8tGh90c-FjpfftqYIZhM49groVPpHr-U7oUgXAqE4Dyp9VraK4t0cJtgfBrd9dQI9QtkPPOVx4EGJJS62a8gUmulC7l8KE1rYxX2sxaTzATJ_kOdQsFkNiRKn95YQrDUbGD72y4S56rEjl8AZb3W4E3lG_2Z2e5vo3aANxPrX-_SyDSPRga9WdQsPwYjPXaM4gbZP8Nuq-cPF4UfSwHohKaAPzCajr9rmByGEgNJ8xDBB0MDpdsaYpD7cJ4fIMjtGcN4zlR9HPjc-KamqQ3hC26q6WM3vNW5OUvk8EAmogD6TOcSaprO1Dp0l4MzSohRB3TrStSVXuHF9dDJlK1-qK868C-aDXCvhILGvM7pKSVhrWVoe1AjdTEzEf_i_-KgvFcm4-0PVo-3HT-kBbbuSDGVPklG8D7xRpR5USy2EH5E1kedErM4jS4vEXasNsg90Ik3EH_TdzoqyEDhnCMeNVkjHLMtHAcao4abAvfu5xmaWGX1oFSB8VZIYULoxWr7VEwQ1umdwMdLMTWe9vTdv1yX4yonPHTSmDZsP0OzY6aASws07lbliZVTzErMFirI-VXud_Evh_SfMg-a5luflKrUlIdvrMpHXw6mOdIwMCssTOacgbk3J7o1UD-rYVGIHJa_38PEzuR6HNyLpnTTepYBl4qcx07j4ef2JxDeHWlC_a9GqquiFimXEF2j4yCHR-ScSq2YlbL6X45lOSbNRYSS4jFoRcttcvmzpgOy_scL0feANNhwEs_pnc8cNUOo6ry1Zi_ypeRFxk5ZrtofQJ6tEnBtMxAQBOHzYl2h7UiwkvdG8z6J_tOt7jFO26O9vNYMK-X6HW_7g1mFCXr6ECt3elGVYH1ucT63pqJpfeyi1uY9Spz_26eapiuoeXHaXthIJyTxawevUV_4dJbFXuPeHIJBumA6C_qBOwBfr6ceX7EFyI6rEC7rf4HOwapx0KliWvhAJYqU89GU4sYne7gKphkzyyLXaJtIiHkYc9waL1V9nGaULH7M1K9aAVkhlkAAI59F3DLIm-sWJR1T52H-RQ3cmBE9vaezVoSlERj4xbqS7o_FHIAPzIfmTc7ElqC2XVZSIZZE3rbXf4uuYi3zffOfS49xSas3kgUDYpSDDkem9xoM3paSTx2koPS6AdfNob2hof5DFkrqE82Z5Z82CmfcSJVpJ2-sxf6-QHsUlJRXqDEDoPerR8buWMSFdj6NDeqwwJgAHAaioiHnj4FaGfVIKXnr0BbZiNt85DwVHPxPVJMcoRtyjXs4xiua1ugJnyHlJ0wRRyEJkSp4FEnfdLKlitmmHM76UoDTo-eN_w9xzrBF49HroUyD6UoGmc0FDA_o5BabXpELU0SPqopmdYmyu4uinXq0_jgLgREVbwGuAOVzNLFVV8C16bKyBYyM3eUAJ_GQOK9GCIhoMtdapTQm5qWiOepcf-ZsLuJ_1_4lgRqkaNXoMW1hPCiRZvM3wsM9d8fGIOFhhISUZFrLaTFYZaIe6h7XaE1scEdEJZtTBnydqoR4UvRIY5nnvxa5sd10RGWMRHF4t-14UkG6Nly7kK6bjS6kJ7eCrWX5lonkHEqXECbaDwf1MK2dFjhBDSojd2XzSL4A2OyE14pPr9wbqn-VJFtN0qaGNCsuNgoXUQ4bCiqrv5AHqLaq389EWH_5pquPNwldVOMXYXjMNcislUXI74up0ON30WmAeo7CV-rtTaSXK4YDfv9vwe1Bex2IkZjuOEndlfEG55n49uAZsGZ7YmSfE-4FHA1K66em-efCIWFPWa7OpY1MYlN9jPzJO6qeSL1Py6TEsbHYXVBSE2YGpoilAKpVlv3D-h8FLJv9xgeL1SMiC9YWZRENYI0iUodAb6p5eg0CiiF6LnYg_bQuqjxf7WyVpseJ0cFN7oDNUa8zvEs4HmgRX0RHNAVU8DKcfRRNo4JBVaztHgg4Mfb_pQmpFik4EBx6Snxs0wj2TCMgxdT0XdOKCd3YaMZH4qodw-Tp2wNqilJq1ItKKWhSxzj_xNMWxvQcvjqMLvWlwAa8b9RTkqK2xwaQmHwpDGlRteizdfdEJ8YnKv_T_8SFgyh2mCNIDZq5nDTmgb3V4tj0FWpLF1uxo0phZzYFvb4p7zdA68_s_Dlpd8DjmAAmsCzBCG7ocw7-CeEK1tseN8y6hq9MYz0WD4F8LaEBvRA4nM00092RgMhSaWH48lmdoBbq-SP4nKCsBmUXMk8zhmO8M3ECrZETqf62TDENypcusiZv1hn0b2LUG-v9uOGOspdotxXFwKvM4JsRjONLfgRoRvZ6dSA4kIGqQLpQafX9rg6pZwmzOgsxCbW_gUtuNliY90m0fTd2yjXf7WNyxeDx7KrgEHPjWC71x7B0YJeQmD87Riz9liNI6bTkC-b4CB28v84jvLHM4IawFTQWcO2jUQfXBYaC9-ElTWfnsglNB97A_AJeF3DXujPWuVKrlDBRH1GrOzOGJKcUO8YZgry0aLrK1Loqgqziu4tecvm_iqmAieYe2YdfpnGYFrC0FWsY6SIBH2nugoi77OmXcrZpAo9PWAj-qmY7usaw-GXI9n4uzktnQhhOJx7H08C69ztNwCktjeqnjoueXZlBhKkGgwAoZTF1tjRrbFHWJpiZ0E3m-VtXhlMIv2MjoO6GUwVNiLDcdwvs1Dr5g45_rk6ja4l-RDE7ZNqDS8xvSi8dwFDlXe7GoEMATq_Z5O1NgjbHhAixjxpS-nGJMaEb-6ORlWgGsUEdCgshMuD4JH9T9EYkhMYwvMQOyhxsoU70PSFx270r-uhdoF12C9KB_3E56qjJjEHejt89Q1gWXOdTJ0ahixOiVKfiVpPr3dJAmjiWA2tXBrToEzsSkA7Lj1061C-CKAZT0qB1kkTJ6O8EcrO5jPeq4dJU00SqzdYREwngYcBouE9svN-w8vqhF6eFDGBiGNLl9ZjqBx94VM37KG3V0Tkkj2hWD8skynCdIZNmPBnYLlr02Ixtk8vK0nEdhuaV-sdgh-R96X7U3RwIn8SlD81PjI7wN2J81uH9bXfATtyLT5RyWwxo01ui1bH9dfu0ShFyXKuZPTpmaDlPXjPTzok5MA4nSWwcUx2i3eA-XujvRw_70vuMPXQdZlomzZfHA-gr1D49a7qRTNuZNcnE73VVET2slezDvYQUa9FWV0d7uLOFRGYLDsI0IB_9VKfUWBSKIoJmaobHGr1RKoaM_Y_f6YlUfo0U83CdBaU9ZVqKYiR1yn2qS1FI7v6UR9XqX1SHqAPbcfbcDDwfpoDnkD5uWzpjqnxzDEDGrVExxITd0z71m86N3BtrUZnY8xBcyNKWGtsMSTTcWbvbOLcgSSNzXLQKOguKIY-0tg51DXkrPmnYvW7Xe9TzhEHpewwKMDuPVFsR&amp;cid=CAQSSQDZpuyzApSorbxLdHMxo_UOniwmJoudm4pXqjks9sins8byFOLVwl45a5_7rQT_LfAjv0LfPy7gqVAVj6fk20xqwu5CrHCaH0YYAQ&quot; data-dv3-width=&quot;160&quot; data-dv3-height=&quot;600&quot; data-dv3-render-mode=&quot;script&quot; data-dv3-meta-data=&quot;[104,76,608586769706706821]&quot;></script><script data-jc=&quot;112&quot; data-jc-version=&quot;r20250602&quot;>(function(){'use strict';const d=/[&amp;?]dbm_c=([^ &amp;]+)/;function e(a){return(a=d.exec(a))?a[1]:null}function f(a,b,c){a=a.getElementsByTagName(b);for(b=0;b<a.length;++b)if(e(a[b].src)===c)return!0;return!1};const g=window;if(g.dv3Utw){var h=g.dv3Utw.u,k=g.dv3Utw.w;g.dv3Utw=void 0;var l;a:{const a=e(h);if(!a||f(document,&quot;script&quot;,a)||f(document,&quot;iframe&quot;,a))l=!0;else{var m=document.getElementsByClassName(&quot;dv3-asfrm&quot;);for(let b=0;b<m.length;++b){const c=m[b];if(c.contentWindow&amp;&amp;f(c.contentWindow.document,&quot;script&quot;,a)){l=!0;break a}}l=!1}}l||k()};}).call(this);</script></div></div><iframe width='0' height='0' style='display:none' src='https://contextual.media.net/checksync.php?vsSync=1&amp;cs=6&amp;cv=31&amp;https=1&amp;cid=8CUMDNT02&amp;prvid=2034%2C2033%2C3022%2C2030%2C3020%2C590%2C2073%2C251%2C273%2C2009%2C178%2C255%2C2028%2C3018%2C2027%2C3017%2C2025%2C117%2C3014%2C459%2C636%2C99%2C77%2C38%2C3011%2C182%2C3010%2C261%2C141%2C262%2C461%2C222%2C201%2C3007%2C246%2C301%2C4%2C203%2C225%2C10000%2C404%2C80%2C229%2C9&amp;itype=EBDA&amp;purpose1=1&amp;gdprconsent=1&amp;gdpr=0&amp;usp_status=0&amp;usp_consent=1'></iframe></div><script src=&quot;https://warp.media.net/rtb/resources/release-20250527-200-adperformance.js&quot; async></script> <script>window._adp=window._adp||[];window.adPerformancePixel=&quot;https://hblg.media.net/log?logid=kfke&amp;evtid=adpvlog&amp;__q=AYwGcgCAjASAAAiAAABAgAEAAAAIAAAAAAEAAAAAAgEEAAAIAAAAAAEAAAAAAAxQwAQEEEA2MjQ4NWNkOWJiNjE1MGIwMTI1ZjdmZWM3NGE3MWE4NMbniY0C9gPY0w5_TdbIPwRVUzJzbWFydGJhY2tncm91bmRjaGVja3MuY29tEjhDVU1ETlQwMj5ic2Etem9uZV8xNzQzNzc3OTc1NzgzLTNfMTIzNDU2DjE2MHg2MDAQMC4xNzA5NTQUc2Nod2FiLmNvbQ53ZXN0X29yGjEzNF82MTc4NTc3MDEEMjMIRUJEQRI4UFJMNEU3TjMAPmJzYS16b25lXzE3NDM3Nzc5NzU3ODMtM18xMjM0NTYCMDhydGItZWJkYS03Yzk4NzZjNjQ4LThxODViLk9SBmVjcAIxAjAABAAQRVhDSEFOR0UCAmJAZjQ4OTJjNDYyN2ZiYjAyNTdjMjIwZmJjMWVkMzRkYzAGMi44&quot;;window._adp.push(function(){window.adPerf.track(&quot;mnet-vtgt-f4892c4627fbb0257c220fbc1ed34dc0&quot;,[&quot;VIEW&quot;,&quot;CLICK&quot;])});</script></div><script data-jc=&quot;22&quot; src=&quot;https://tpc.googlesyndication.com/pagead/js/r20250602/r20110914/client/window_focus_fy2021.js&quot; async data-jc-version=&quot;r20250602&quot; data-jcp-url=&quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai=C5uMnDv4-aMnJKrjcmtUP_9jKgQ_xi56QXPKJ5eG5BcCNtwEQASAAYMm2iYfMo8AXggEXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjfIAQngAgCoAwHIAwKqBLUCT9C7QjX7_rczWmcqV6DYi0W96RQFbNfZWJyxnx8gHlFxQ0YiB9n7e7BF4QCasuRw7PN3XTlAPMs8FFEK7NEJDGnage0ec9TDQR2lFp3UYAwROiF1Y_FiIqrX-x7Rs80lWY30sgf2IJOIr9YAqy0rs2jaA_15w9ogj9xwLmh8UOcuNUklCkQDLkbKYUf55Sd8-M5bzY0iV9hZRLN5Qs8kAWxvL180jXaXkuV8_GQ7h6sbWFZLSGcf9SllkyR_oHvsAlApa97p9iQo3Ie5TuA5jUfFrY_7rTG3Ol0PPWy-RTrAlsSwLnbxIfQOoGNesiXenOOU6vifu0IcHXP4QyRDyJIfQSHeKiqvKgXguuXDYxCBzZGjJmGrxQP1AukzVu5thWxAGRGrIZja9yCHFBXi4CU-XytZ4AQBgAb3iYLOjqDZhZYBoAYhqAemvhuoB5bYG6gHqpuxAqgH_56xAqgH35-xAqgHrb6xAqgHv9OxAtgHANIIJgiAYRABMgKKAjoNgECAwICAgICogAKgA0i9_cE6WOC07ICz1Y0D-gsCCAGADAGqDQJVU-INEwjZhO2As9WNAxU4rqYEHX-sMvDqDRMIvP_tgLPVjQMVOK6mBB1_rDLw0BUBgBcBshcOGAsqCjQxNjAzNDU3ODU&amp;amp;sigh=L085JUvRZGg&amp;amp;cid=CAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bQ&quot; data-jcp-gws-id=&quot;&quot; data-jcp-qem-id=&quot;CMmI74Cz1Y0DFTiupgQdf6wy8A&quot;></script><iframe title=&quot;Blank&quot; scrolling=&quot;no&quot; frameborder=0 height=0 width=0 src=&quot;https://pagead2.googlesyndication.com/pagead/s/cookie_push_onload.html#aHR0cHM6Ly91bS5zaW1wbGkuZmkvZ3BfbWF0Y2g_Z29vZ2xlX3B1c2g9QVhjb09tU0dDc2U4SFNVTl95bnNrQVh0YjNiaVl0eGJ6VTFrM09kc0dZeUNTYTdjMEJTQ0R1dHFXem1LQVRDaDd1ZkdlalVSeXgtRlUzdnhnQkxuTFNVaktNa2NWN05famRXRkxJUHAxZHFXRUJvTUdGYTREdjN3Vkp2SFUwRkw0NVdYeXBsVXZQWnlWcFVyR2d0SDVJYndnSHM=,aHR0cHM6Ly9zeW5jLmlwcmVkaWN0aXZlLmNvbS9kL3N5bmMvY29va2llL2dlbmVyaWM_aHR0cHM6Ly9jbS5nLmRvdWJsZWNsaWNrLm5ldC9waXhlbD9nb29nbGVfbmlkPWFkZWxwaGljX21vYmlsZSZnb29nbGVfcHVzaD1BWGNvT21Tb2s2SWJ2cUhRSHozd3BfcHBmM1dEaTFFOW9fOHpjQ1dydjBaYzVuZWdualVBT0lPU1EzVVpZZVotRlNzbDV1VXhrSTRucTFZWDZvQUlwb0FQT3BFSmdsc2wzajc5SjB1SlhJbk4wQXFwQjdDclFuOUNQMDBBakI3MmlBR3VUcHhaSElJYnBCb0g3dkNjTXVRSVpBJmdvb2dsZV9obT0ke0FERUxQSElDX0NVSURfQjY0fQ==,aHR0cHM6Ly9wci1iaC55YnAueWFob28uY29tL3N5bmMvYWR4P2dvb2dsZV9wdXNoPUFYY29PbVJXM3dNT1NkRE1MaDdIdG12elZVRlFlcnlzRkd0TWhBVmFQbG1MZ3djbmNwX3dOeXJwNjlQYkVtOW9SZE9ySTR2enVNVEZVQjlGQzU5RW9vUk8tbXRZd0xEalBPYUxPclFFZDNMYmJfQU43VS10MVlmUUxtRWNONjg0TTlSRlVuSzVQV2NHZkh1VHd2OFVGdnNhQ21J,aHR0cHM6Ly9kaXMuY3JpdGVvLmNvbS9kaXMvdXNlcnN5bmMuYXNweD9yPTQmcD0xNCZjcD1nb29nbGUmY3U9MSZ1cmw9aHR0cHMlM0ElMkYlMkZjbS5nLmRvdWJsZWNsaWNrLm5ldCUyRnBpeGVsJTNGZ29vZ2xlX25pZCUzRGNqcCUyNmdvb2dsZV9obSUzRCU0MCU0MENSSVRFT19VU0VSSUQlNDAlNDAlMjZnb29nbGVfcHVzaCUzREFYY29PbVI2N1U4eTRIRzB2U2ZYdmV5cVZMZ3pyMTIxNkV5Mmt0M3cxNTJ5UG9SOFBxckNVUk0wd2VjcWtRRFptejVoLWktbWxPN2l6UWk1NUIzOWFVOEU5ZVRoVTl6dVhKZGh4bXNhY2xyUV9sZHY4MnZ4ZVdkbzdsLUJkMktxVlp6SUpRYTFqdS1jZmpIR1lFTWpxNFJqY3c=,aHR0cHM6Ly9ydGIub3BlbngubmV0L3N5bmMvZGRzP2dvb2dsZV9wdXNoPUFYY29PbVNELThSX0IxQldIZWJwV3N1SnRXQ0FjbWdDaTVlSWVobWd6MUJ2cUdzOUR0cEtTN0NhVklKdXRKbF9ZNTlKc01XN1VMVWtrcFg0LXZaNGo5ZE5zQ1hGNXoyZFVlQ0NJb0xtOFNOLUFzOXVPd0R5NllubW45Sm00N3hkUGE2dnljblJKSTNJeHJ2ZFVnQU5NNTJMVVE=,aHR0cHM6Ly9zeW5jLnNydi5zdGFja2FkYXB0LmNvbS9zeW5jP25pZD0xNTQmZ29vZ2xlX3B1c2g9QVhjb09tU1VVNlE3TkVQVW96QWpmNmttNjI3ZmV3MjRCYkJZc1FhSzBTQmdFWmI2NndNbEdGdVFxeHh4MUxKdmlyU0Z1WWp4U2pocEFyVXNGTHIwRVNHWm5JczlQR21Qa2RjMlZaYVJaY3d6NlliRVZUaVhjeENhV2x6OXFETUFPLXFSUnZqQ0NkN3lIRnRHU1JBc2lPNE0xdw==,aHR0cHM6Ly9ndHJhY2VuZXAuYWRtYXN0ZXIuY2MvanUvY3MvZ29vZ2xlP2dvb2dsZV9wdXNoPUFYY29PbVNLcEZGdEtBaU1OR0V5R0NKTFB0X0x5cDh5UjV2dXdBekpvWnNJZ0VSWTRsdG1CUmxJelVxTTFESnNPOHE4M3QycmtGekxoWFFRNGFmWEhLaFAyQVQxNkV1NGY2ZnFZU1NhZUZYZ0EzeHctTzJGc1loRWZQR3VjUjFGRjV2cHhoSDdXSDc2eTJkVTVzX0RibWRaenlZNA==,aHR0cHM6Ly9jbS5nLmRvdWJsZWNsaWNrLm5ldC9waXhlbC9hdHRyP2Q9QUhORjEzSXdkdlpCRzBrd2hKYWxrcGY0ZlBQT2doS29RSHpBaTdiMWd5eW5raVlzbWJ0amJ2eTJnamUzSHg5VWk0MVJVbXRlT2hEMkp3&quot;  style=&quot;position:absolute&quot; aria-hidden=&quot;true&quot;></iframe><script data-jc=&quot;23&quot; src=&quot;https://tpc.googlesyndication.com/pagead/js/r20250602/r20110914/client/qs_click_protection_fy2021.js&quot; data-jc-version=&quot;r20250602&quot; data-jcp-init-data=&quot;[[[[null,500,99,2,9,null,null,null,1]]]]&quot;></script><img src=&quot;//www.google.com/ads/measurement/l?ebcid=ALh7CaQpNHETH9p2obUDlyyMH5CtgjlC6f728yovzRHqt2_ZyNCBZIkNLbnfQ150miCYMAC8yxf44A2-kLNjjOfrCeGExyJ70w&quot; style=&quot;display:none;&quot; alt=&quot;&quot;></img><script src=&quot;https://tpc.googlesyndication.com/safeframe/1-0-45/js/ext.js&quot;></script><div style=&quot;bottom:0;right:0;width:160px;height:600px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAACBSURBVBjTbZABCkAxCELtBt7/tH8rrQa/QRBz7iXwX1GFc2ImgHl7Ormmq0otrsbTveHR3Z5PNLW6f7Aaq7f3kPhNknSR5MMNkxR5egtBxoZC8W7rIqFxqWUlDqw99cUG8b4rQTRwPPsZQmqTShzyHsdCaJBJBBPHZKdM+CS4NgQ+eyoGEeyn3BsAAAAASUVORK5CYII=') !important;&quot;></div><script data-jc=&quot;103&quot; data-jc-version=&quot;r20250602&quot; data-jcp-base_url=&quot;https://googleads.g.doubleclick.net/pagead/conversion/?ai=C5uMnDv4-aMnJKrjcmtUP_9jKgQ_xi56QXPKJ5eG5BcCNtwEQASAAYMm2iYfMo8AXggEXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjfIAQngAgCoAwHIAwKqBLUCT9C7QjX7_rczWmcqV6DYi0W96RQFbNfZWJyxnx8gHlFxQ0YiB9n7e7BF4QCasuRw7PN3XTlAPMs8FFEK7NEJDGnage0ec9TDQR2lFp3UYAwROiF1Y_FiIqrX-x7Rs80lWY30sgf2IJOIr9YAqy0rs2jaA_15w9ogj9xwLmh8UOcuNUklCkQDLkbKYUf55Sd8-M5bzY0iV9hZRLN5Qs8kAWxvL180jXaXkuV8_GQ7h6sbWFZLSGcf9SllkyR_oHvsAlApa97p9iQo3Ie5TuA5jUfFrY_7rTG3Ol0PPWy-RTrAlsSwLnbxIfQOoGNesiXenOOU6vifu0IcHXP4QyRDyJIfQSHeKiqvKgXguuXDYxCBzZGjJmGrxQP1AukzVu5thWxAGRGrIZja9yCHFBXi4CU-XytZ4AQBgAb3iYLOjqDZhZYBoAYhqAemvhuoB5bYG6gHqpuxAqgH_56xAqgH35-xAqgHrb6xAqgHv9OxAtgHANIIJgiAYRABMgKKAjoNgECAwICAgICogAKgA0i9_cE6WOC07ICz1Y0D-gsCCAGADAGqDQJVU-INEwjZhO2As9WNAxU4rqYEHX-sMvDqDRMIvP_tgLPVjQMVOK6mBB1_rDLw0BUBgBcBshcOGAsqCjQxNjAzNDU3ODU&amp;amp;sigh=L085JUvRZGg&quot; data-jcp-cpu_label=&quot;heavy_ad_intervention_cpu&quot; data-jcp-net_label=&quot;heavy_ad_intervention_network&quot;>(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function n(a){h.setTimeout(()=>{throw a;},0)};let p=void 0;function u(a,b=!1){return b&amp;&amp;Symbol.for&amp;&amp;a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var v=u(),w=u(&quot;m_m&quot;,!0);const x=u(&quot;jas&quot;,!0);var y={};function z(a,b){return b===void 0?a.h!==A&amp;&amp;!!(2&amp;(a.g[x]|0)):!!(2&amp;b)&amp;&amp;a.h!==A}const A={};const B=BigInt(Number.MIN_SAFE_INTEGER),C=BigInt(Number.MAX_SAFE_INTEGER);function D(a){return a};function E(a,b,e,c){var d=c!==void 0;c=!!c;const l=[];var f=a.length;let g,k=**********,H=!1;const r=!!(b&amp;64),q=r?b&amp;128?0:-1:void 0;b&amp;1||(g=f&amp;&amp;a[f-1],g!=null&amp;&amp;typeof g===&quot;object&quot;&amp;&amp;g.constructor===Object?(f--,k=f):g=void 0,!r||b&amp;128||d||(H=!0,k=(F??D)(k-q,q,a,g)+q));b=void 0;for(d=0;d<f;d++){let m=a[d];if(m!=null&amp;&amp;(m=e(m,c))!=null)if(r&amp;&amp;d>=k){const t=d-q;(b??(b={}))[t]=m}else l[d]=m}if(g)for(let m in g){a=g[m];if(a==null||(a=e(a,c))==null)continue;f=+m;let t;r&amp;&amp;!Number.isNaN(f)&amp;&amp;(t=f+q)<k?l[t]=a:(b?? (b={}))[m]=a}b&amp;&amp;(H?l.push(b):l[k]=b);return l}function G(a){switch(typeof a){case &quot;number&quot;:return Number.isFinite(a)?a:&quot;&quot;+a;case &quot;bigint&quot;:return a>=B&amp;&amp;a<=C?Number(a):&quot;&quot;+a;case &quot;boolean&quot;:return a?1:0;case &quot;object&quot;:if(Array.isArray(a)){const b=a[x]|0;return a.length===0&amp;&amp;b&amp;1?void 0:E(a,b,G)}if(a!=null&amp;&amp;a[w]===y)return I(a);return}return a}let F;function I(a){a=a.g;return E(a,a[x]|0,G)};function J(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error(&quot;narr&quot;);b=a[x]|0;2048&amp;b&amp;&amp;!(2&amp;b)&amp;&amp;K();if(b&amp;256)throw Error(&quot;farr&quot;);if(b&amp;64)return b&amp;2048||(a[x]=b|2048),a;var e=a;b|=64;var c=e.length;if(c){var d=c-1;c=e[d];if(c!=null&amp;&amp;typeof c===&quot;object&quot;&amp;&amp;c.constructor===Object){const l=b&amp;128?0:-1;d-=l;if(d>=1024)throw Error(&quot;pvtlmt&quot;);for(const f in c){const g=+f;if(g<d)e[g+l]=c[f],delete c[f];else break}b=b&amp;-8380417|(d&amp;1023)<<13}}}a[x]=b|2112;return a} function K(){if(v!=null){var a=p??(p={});var b=a[v]||0;b>=5||(a[v]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity=&quot;incident&quot;,n(a))}};function L(a,b){if(typeof a!==&quot;object&quot;)return a;if(Array.isArray(a)){var e=a[x]|0;a.length===0&amp;&amp;e&amp;1?a=void 0:e&amp;2||(!b||4096&amp;e||16&amp;e?a=M(a,e,!1,b&amp;&amp;!(e&amp;16)):(a[x]|=34,e&amp;4&amp;&amp;Object.freeze(a)));return a}if(a!=null&amp;&amp;a[w]===y){e=a.g;const c=e[x]|0;z(a,c)||(c&amp;2?b=!0:c&amp;32&amp;&amp;!(c&amp;4096)?(e[x]=c|2,a.h=A,b=!0):b=!1,b?(a=new a.constructor(e),a.m=A):a=M(e,c));return a}}function M(a,b,e,c){c??(c=!!(34&amp;b));a=E(a,b,L,c);c=32;e&amp;&amp;(c|=2);b=b&amp;8380609|c;a[x]=b;return a};function N(a,b,e){if(e!=null&amp;&amp;typeof e!==&quot;string&quot;)throw Error();if(a.h===A){var c=a.g;c=M(c,c[x]|0);c[x]|=2048;a.g=c;a.h=void 0;a.m=void 0;c=!0}else c=!1;if(!c&amp;&amp;z(a,a.g[x]|0))throw Error();a=a.g;a:{var d=a[x]|0;c=b+-1;const l=a.length-1;if(l>=0&amp;&amp;c>=l){const f=a[l];if(f!=null&amp;&amp;typeof f===&quot;object&quot;&amp;&amp;f.constructor===Object){f[b]=e;break a}}c<=l?a[c]=e:e!==void 0&amp;&amp;(d=(d??a[x]|0)>>13&amp;1023||536870912,b>=d?e!=null&amp;&amp;(a[d+-1]={[b]:e}):a[c]=e)}};var O=class{constructor(a){this.g=J(a)}toJSON(){return I(this)}};O.prototype[w]=y;O.prototype.toString=function(){return this.g.toString()};var P=class extends O{};function Q(a=window){return a};var R=/#|$/;const S=function(a,b=null){return b&amp;&amp;b.getAttribute(&quot;data-jc&quot;)===String(a)?b:document.querySelector(`[${&quot;data-jc&quot;}=&quot;${a}&quot;]`)}(103,document.currentScript);if(S==null)throw Error(&quot;JSC not found 103&quot;);const T={},U=S.attributes;for(let a=U.length-1;a>=0;a--){const b=U[a].name;b.indexOf(&quot;data-jcp-&quot;)===0&amp;&amp;(T[b.substring(9)]=U[a].value)} (function(a,b,e){var c=window;a&amp;&amp;b&amp;&amp;e&amp;&amp;c.ReportingObserver&amp;&amp;c.fetch&amp;&amp;(new c.ReportingObserver((d,l)=>{d=d[0];if(d?.body?.id===&quot;HeavyAdIntervention&quot;){d=(d.body.message?.indexOf(&quot;network&quot;)||0)>0?e:b;var f=a.search(R);var g;b:{for(g=0;(g=a.indexOf(&quot;ad_signals&quot;,g))>=0&amp;&amp;g<f;){var k=a.charCodeAt(g-1);if(k==38||k==63)if(k=a.charCodeAt(g+10),!k||k==61||k==38||k==35)break b;g+=11}g=-1}k=g;if(k<0)f=null;else{g=a.indexOf(&quot;&amp;&quot;,k);if(g<0||g>f)g=f;f=decodeURIComponent(a.slice(k+11,g!==-1?g:0).replace(/\+/g,&quot; &quot;))}f? (navigator.sendBeacon(&quot;https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&amp;label=&quot;+d),d={i:f,label:d},f=new P,d!=null&amp;&amp;(d.i!=null&amp;&amp;N(f,1,d.i),d.s!=null&amp;&amp;N(f,3,d.s),d.label!=null&amp;&amp;N(f,6,d.label),d.l!=null&amp;&amp;N(f,7,d.l),d.j!=null&amp;&amp;N(f,8,d.j),d.o!=null&amp;&amp;N(f,11,d.o)),Q(h).fence?.reportEvent({eventType:&quot;interaction&quot;,eventData:JSON.stringify(I(f)),destination:[&quot;buyer&quot;]})):c.fetch(`${a}&amp;label=${d}`,{keepalive:!0,method:&quot;get&quot;,mode:&quot;no-cors&quot;});l.disconnect()}},{types:[&quot;intervention&quot;], buffered:!0})).observe()})(T.base_url,T.cpu_label,T.net_label);}).call(this);</script><script id=&quot;googleActiveViewDisplayScript&quot; src=&quot;https://pagead2.googlesyndication.com/pagead/managed/js/activeview/current/ufs_web_display.js&quot;></script><script type=&quot;text/javascript&quot;>osdlfm();</script></body></html>{&quot;uid&quot;:&quot;3&quot;,&quot;hostPeerName&quot;:&quot;https://www.smartbackgroundchecks.com&quot;,&quot;initialGeometry&quot;:&quot;{\&quot;windowCoords_t\&quot;:-16000,\&quot;windowCoords_r\&quot;:-15958,\&quot;windowCoords_b\&quot;:-15986,\&quot;windowCoords_l\&quot;:-16000,\&quot;frameCoords_t\&quot;:48,\&quot;frameCoords_r\&quot;:180,\&quot;frameCoords_b\&quot;:648,\&quot;frameCoords_l\&quot;:20,\&quot;styleZIndex\&quot;:\&quot;auto\&quot;,\&quot;allowedExpansion_t\&quot;:48,\&quot;allowedExpansion_r\&quot;:842,\&quot;allowedExpansion_b\&quot;:32,\&quot;allowedExpansion_l\&quot;:20,\&quot;xInView\&quot;:1,\&quot;yInView\&quot;:1}&quot;,&quot;permissions&quot;:&quot;{\&quot;expandByOverlay\&quot;:false,\&quot;expandByPush\&quot;:false,\&quot;readCookie\&quot;:false,\&quot;writeCookie\&quot;:false}&quot;,&quot;metadata&quot;:&quot;{\&quot;shared\&quot;:{\&quot;sf_ver\&quot;:\&quot;1-0-45\&quot;,\&quot;ck_on\&quot;:1,\&quot;flash_ver\&quot;:\&quot;0\&quot;}}&quot;,&quot;reportCreativeGeometry&quot;:false,&quot;isDifferentSourceWindow&quot;:false,&quot;goog_safeframe_hlt&quot;:{}}" scrolling="no" marginwidth="0" marginheight="0" width="160" height="600" data-is-safeframe="true" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" allow="private-state-token-redemption;attribution-reporting" aria-label="Advertisement" tabindex="0" data-google-container-id="3" style="border: 0px; vertical-align: bottom;"></iframe></div></div></div>

<div name="centerPanel" class="col break-word" style="padding-right:5px; padding-left:5px; max-width: 800px"><a class="link-underline" href="https://www.smartbackgroundchecks.com/" title="Search" aria-label="Search">Home</a>&nbsp;&gt;&nbsp;<a class="link-underline" href="https://www.smartbackgroundchecks.com/phones/561" title="561 Area Code" aria-label="561 Area Code">561</a>&nbsp;&gt;&nbsp;<a class="link-underline font-weight-bold" href="https://www.smartbackgroundchecks.com/phone/5619324217" title="People With Phone 5619324217" aria-label="People With Phone 5619324217">(*************</a><br><br>
<div class="row no-gutters m-0 p-1 w-100 card-block card-normal" id="EmRjBGt0ZGH1ZQN1ZwHlBQL4AmH">
	<div class="p-1 w-100 lh-20px"> 
		<span class="p-0 align-top"><a href="https://www.smartbackgroundchecks.com/people/brenda-mccorvey/EmRjBGt0ZGH1ZQN1ZwHlBQL4AmH" title="Brenda Mccorvey - Free Background Report" class="btn btn-primary btn-sm btn-block text-center" aria-label="Brenda Mccorvey - Free Background Report">
			<img alt="Open Free Background Report" height="16" width="16" src="/images/unlock-alt-solid.svg"> Open Free Background Report </a></span><br>
		<h1 class="h1Title">Reverse Phone Search<br><a class="link-underline" href="https://www.smartbackgroundchecks.com/people/brenda-mccorvey/EmRjBGt0ZGH1ZQN1ZwHlBQL4AmH" title="Reverse Phone Search for (*************">(*************<br>Brenda Mccorvey</a><br>Royal Palm Beach, FL</h1><br><span class="faq-question-new">Age:60</span><br><span class="faq-answer">MetroPCS Inc Wireless <br>Reported between Apr 2017 - May 2025<br></span><br><h3 class="titleBox">Prior Owners of (*************:</h3><div><div class="row mt-2">
					<div class="col link-underline"><a href="https://www.smartbackgroundchecks.com/people/john-mcphee/El0lZQL3AwtlZmxlZGt2AmV4ZwV4" title="John B Mcphee - Free Background Report"><h4><b>John B Mcphee</b></h4></a></div>
					<div class="col">29 y/o</div>
					<div class="col">Lantana, FL</div>
					<div class="col">Reported on 12/7/2013</div>
				  </div></div><br><div id="mapouter" style="height:300px" class="mapouter"><div class="gmap_canvas"><iframe title="Area Code Map" width="100%" height="300" id="gmap_canvas" src="https://maps.google.com/maps?q=Royal+Palm+Beach%2C+FL&amp;t=&amp;z=10&amp;ie=UTF8&amp;iwloc=&amp;output=embed" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe></div></div><br><div style="min-height:300px"><div id="bsa-zone_1743777761552-3_123456" data-google-query-id="CMiI74Cz1Y0DFTiupgQdf6wy8A"><div id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_MidcontentBanner2_ROS_0__container__" style="border: 0pt none; margin: auto; text-align: center; width: 300px; height: 250px;"><iframe frameborder="0" src="https://e0d235542a55b49e6f47dae4abe4b5a2.safeframe.googlesyndication.com/safeframe/1-0-45/html/container.html" id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_MidcontentBanner2_ROS_0" title="3rd party ad content" name="1-0-45;97311;<!doctype html><html><head><script>var jscVersion = 'r20250602';</script><script>var google_casm=[];</script></head><body leftMargin=&quot;0&quot; topMargin=&quot;0&quot; marginwidth=&quot;0&quot; marginheight=&quot;0&quot;><div class=&quot;GoogleActiveViewInnerContainer&quot;id=&quot;avic_CMiI74Cz1Y0DFTiupgQdf6wy8A&quot;style=&quot;left:0px;top:0px;width:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;&quot;></div><div style=&quot;display:inline&quot;class=&quot;GoogleActiveViewElement&quot;data-google-av-cxn=&quot;https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjsuUtO4I1TVvm6FQTqtVCFrrmu7W7Hl1RWR_4jqg3z0CdwTqqBP3tsxKJxVy6j9R4YHKgxsZLwlZEVkQ26qWhrfNifn1tPJmdEGvagqeNSu9WeDd--mUbBBdgHTcxCpXFjDRGaPz9FBLXslJRW1f-rWB-O3tezjbm3o4ID8zEzDILiSuPDR-t5IU1__3vaZhAGeFzVWaI51f&amp;amp;sai=AMfl-YQE5-Kdx_2NGJJzGpX-5TA7DKTjvSLvcEIpZPnoBkCA1yhAKd8GF_C6DrNTNXqOhoO3QI45cE0wRiMDDpfjZRxZNeeO9rs2_4HYRqIBJEstqDzO4Dmy__WvlwqV&amp;amp;sig=Cg0ArKJSzHbMYOZiu-K5EAE&amp;amp;cid=CAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bRgB&quot;data-google-av-adk=&quot;3753116506&quot;data-google-av-metadata=&quot;la=0&amp;amp;xdi=0&amp;amp;&quot;data-google-av-ufs-integrator-metadata=&quot;CpkBCkFtb2RlbF9wZXJzb25fY291bnRyeV9jb2RlX1VTX3BlcnNvbl9yZWdpb25fY29kZV80MzRmNWYzNzM1MzEuanNvbhIaQ01pSTc0Q3oxWTBERlRpdXBnUWRmNnd5OEEYASIeCJgWEJKlARjV9T0g1fU9KAIwAjgBXc3MzD1gr6AMKMz38un4_____wEwzPfyaTgBQAFIAFABEpQECocEaHR0cHM6Ly9wYWdlYWQyLmdvb2dsZXN5bmRpY2F0aW9uLmNvbS9wY3MvYWN0aXZldmlldz94YWk9QUtBT2pzdVV0TzRJMVRWdm02RlFUcXRWQ0Zycm11N1c3SGwxUldSXzRqcWczejBDZHdUcXFCUDN0c3hLSnhWeTZqOVI0WUhLZ3hzWkx3bFpFVmtRMjZxV2hyZk5pZm4xdFBKbWRFR3ZhZ3FlTlN1OVdlRGQtLW1VYkJCZGdIVGN4Q3BYRmpEUkdhUHo5RkJMWHNsSlJXMWYtcldCLU8zdGV6amJtM280SUQ4ekV6RElMaVN1UERSLXQ1SVUxX18zdmFaaEFHZUZ6VldhSTUxZiZzYWk9QU1mbC1ZUUU1LUtkeF8yTkdKSnpHcFgtNVRBN0RLVGp2U0x2Y0VJcFpQbm9Ca0NBMXloQUtkOEdGX0M2RHJOVE5YcU9ob08zUUk0NWNFMHdSaU1ERHBmalpSeFpOZWVPOXJzMl80SFlScUlCSkVzdHFEek80RG15X19Xdmx3cVYmc2lnPUNnMEFyS0pTekhiTVlPWml1LUs1RUFFJmNpZD1DQVFTUEFEWnB1eXpuSTlJYjVpUFB2TlZjTHhvS1NXTTkxT3kwV2thWGl0TTFXZEZUNGRzaERaSjVjNW5CWUtDSEZKbnJocVlnd09YSFFqNkg0WDNiUmdCEgAaACABKAAwBBoeChpDTWlJNzRDejFZMERGVGl1cGdRZGY2d3k4QRAF&quot;data-google-av-override=&quot;-1&quot;data-google-av-dm=&quot;2&quot;data-google-av-aid=&quot;0&quot;data-google-av-naid=&quot;1&quot;data-google-av-slift=&quot;&quot;data-google-av-cpmav=&quot;&quot;data-google-av-btr=&quot;&quot;data-google-av-itpl=&quot;20&quot;data-google-av-rs=&quot;4&quot;data-google-av-flags=&quot;[&amp;quot;x%278440&amp;#39;9efotm(&amp;amp;753374%2bejvf/%27844&amp;gt;&amp;#39;9wuvb$&amp;amp;56533&amp;gt;!=|vqc)!273794&amp;amp;&amp;lt;qqvb/%&amp;lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;amp;&amp;lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&amp;#39;76463;21$?ebkpb$&amp;amp;0366717&amp;gt;*&amp;gt;bgipf+!3=712363%9aihwc)!7202&amp;lt;217&amp;#39;9efotm(&amp;amp;20061;48&amp;amp;&amp;gt;`dopb/%&amp;lt;1707200!=8(&amp;amp;2005575?&amp;amp;&amp;gt;`dopb/%&amp;lt;170642?!=|vqc)!7201;=50&amp;#39;9wuvb$&amp;amp;03641654*&amp;gt;bgipf+!3=731103%9aihwc)!7200?073&amp;#39;9efotm(&amp;amp;2004?51;&amp;amp;&amp;gt;`dopb/%&amp;lt;17&amp;gt;474&amp;gt;!=nehu`/!36406412!9abk{a($167745;=&amp;amp;&amp;lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&amp;gt;7&amp;amp;&amp;lt;qqvb/%&amp;lt;104=460!=nehu`/!363;42&amp;gt;7!9abk{a($1656;3?&amp;lt;&amp;amp;&amp;lt;cbotf+*01011776%2bejvf/%72&amp;gt;17266!=efdwa*&amp;#39;7616?=&amp;lt;=$?ebkpb$&amp;amp;0335225&amp;gt;*&amp;gt;bgipfz&amp;quot;]&quot;><DIV STYLE=&quot;position: absolute; left: 0px; top: 0px; visibility: hidden;&quot;><IMG SRC=&quot;https://pagead2.googlesyndication.com/pagead/gen_204?id=xbid&amp;dbm_b=AKAmf-AVel6gA1l0N2TiBdOKgsDCO4qBmyz0xjTghL-6XAsaD9XKWpakwC06CO6jDSJYVGWprK8xzSqbJq3ODfh3Vh2dNQpmDUOKVlqhAa677qKiCm5Zi50&quot; BORDER=0 WIDTH=1 HEIGHT=1 ALT=&quot;&quot; STYLE=&quot;display:none&quot;></DIV><iframe title=&quot;Blank&quot; src=&quot;https://googleads.g.doubleclick.net/xbbe/pixel?d=CJfRrNYFEPbY6t8FGNqM0LACMAE&amp;v=APEucNVbcwrnzYPNVzzzwKx-rTU2-D-XhQgFU7UPSNSriuUngR3ZJz9YmXDEfC6Sam4DQ24oqee_FZqa9zgnVVPWpL077Z292Pdiv9RObLtzN282QFuMSGA&quot; style=&quot;display:none&quot; aria-hidden=&quot;true&quot;></iframe><div><div style=&quot;position:absolute;&quot;><script>if (!window.mraid) {document.write('\x3cdiv id=&quot;ad_unit&quot;\x3e');}document.write('\x3cdiv class=&quot;GoogleCreativeContainerClass&quot; ' +'id=&quot;gcc_Dv4-aMjJKrjcmtUP_9jKgQ8&quot;\x3e');(function() {var m = document.createElement('meta');m.setAttribute('data-jc', '82');m.setAttribute('data-jc-version', 'r20250602');var ss = document.getElementsByTagName('script')[0];if (ss &amp;&amp; ss.parentNode) {ss.parentNode.insertBefore(m, ss);}})();(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function k(a,d,b){if(a)for(let c=0;a!=null&amp;&amp;c<500&amp;&amp;!b(a);++c)a=d(a)}function l(a,d){k(a,b=>{try{return b===b.parent?null:b.parent}catch(c){}return null},d)}function m(a,d){if(a.tagName==&quot;IFRAME&quot;)d(a);else{a=a.querySelectorAll(&quot;IFRAME&quot;);for(let b=0;b<a.length&amp;&amp;!d(a[b]);++b);}}function n(a){return(a=a.ownerDocument)&amp;&amp;(a.parentWindow||a.defaultView)||null} function p(a,d,b){let c;try{c=JSON.parse(b.data)}catch(f){}if(typeof c===&quot;object&quot;&amp;&amp;c&amp;&amp;c.type===&quot;creativeLoad&quot;){var g=n(a);if(b.source&amp;&amp;g){var e;l(b.source,f=>{try{if(f.parent===g)return e=f,!0}catch(q){}});e&amp;&amp;m(a,f=>{if(f.contentWindow===e)return d(c),!0})}}}function r(a){return typeof a===&quot;string&quot;?document.getElementById(a):a};window.clsn=(a,d)=>{const b=r(a);if(b)if(b.onCreativeLoad)b.onCreativeLoad(d);else{var c=d?[d]:[],g=e=>{for(let f=0;f<c.length;++f)try{c[f](1,e)}catch(q){}c={push:f=>{f(1,e)}}};b.onCreativeLoad=e=>{c.push(e)};b.setAttribute(&quot;data-creative-load-listener&quot;,&quot;&quot;);b.addEventListener(&quot;creativeLoad&quot;,e=>{g(e.detail)});h.addEventListener(&quot;message&quot;,e=>{p(b,g,e)})}};}).call(this); clsn(&quot;gcc_Dv4-aMjJKrjcmtUP_9jKgQ8&quot;);document.write('\x3ca target\x3d\x22_blank\x22 id\x3d\x22img_anch_CMaI74Cz1Y0DFTiupgQdf6wy8A\x22 href\x3d\x22https://ad.doubleclick.net/pcs/click?xai\x3dAKAOjssCa3DwM_i0IW6lyEyVEuSvgqYIhSrJxMTeUaG4jGpm_L9TkDsUGfRfo010qIY09w4uHNlA7dtP28M-80Wziqjhj9KMCPf-yduv2ZllhHtw1aKCD85onU_QMUtKcty8SOjph3ywpxJTH_KCSRdIO7k0Da-nrDwYkur9WutxVUQfkE2IlfVJRTcWydFqe6y3AAA2SJxhPyBbLa9xF1_M4M7KZYS7wzZu5Zk-xJUBN3bHHjNq2-4IoV8imMrzg1sl6vnrHiC-bXc2xri9Sq0s5hxMD67P-XqlfTYA4nmAPFNIMAl6epgXDJYtarOzD5mJI0I77zB0gmDTt8wHArOlhlontpNd4K4ulHEJ5Dumbo_o3cCDhGunboauFGoYplxg_ee_TuuHEfKQK8bRCwmauinc7xxonp4SJSq_lvMwTftjLDLMxlatf0VY0DUpvTx4EP4mngLOQWdi62XJDY-1kpkhmfG-aaotDX6z1Fb7r8Mc69B5R5R0MZ-oyOgx2ctk8ckr3aaKvtl1j-yTnLOKPntjensUTcs6ZjbnLpZbN-cBq1qIE6F4KFuZlk2f_RBLxQNcVIVarhL8efarzPA12WczPi80OZi6SV6mu0D0gCud9T7dzyiRokLtXtJdsk_qIn9Crlm-vFpYmVKfX8wmu_C8FFIL-pnkKzit6PxsyHCpPDbgUGFdU63KFuONU5qGehcWIk7egwi2h2D9YY-wXPMKzbRSIcby0Qz_E3cLmI6ilwaVPHaX-iNNtBda9HiFAg0W5rmtd7fV7kspTq70TGIFboCcJV4k5Y9xCCUwdMGBfZuDNO1X2p_t4AXPYUPm15zTncfznCRmgq3y_fJL7wO_OPtDbzxUTe6KysK3t03r7koNjcSyGLrBlBB6ifiUQYNhC9IMxPtUpNDKFCJ-OUSulEmcvRqTAuIgAruDZ4PRKvkrLfAQi3eIwBMyH5wa3J-6tFi9pXPfzRnwfvPo-580PDbOVRkr7JqGX6f8HXr2DTNPlCoA0cyYjZ-jZ0OYKdWdEkPqXoeS-XLj5-RvlO8M9woX8lEx9q_2Mc53jB4uK00lLqGGmTt7ZFBFSzq7Oh679BLoLUE6vby4LgWqn3nKBBjQmT3bP7A5YyysQC_1x5fHxbxleXEvDS4G6mbYiJwoMHcIg8cC6AVoYVe-b3-TcwOSmKaFLDZxGRDSm7m3ZVyQnH1mFZDl4rqQgYgWQAZsrp1K5pGpwjfH1V3McgHDaQN1fJ6iBSJf7JbDf8jL-hlA6tu9PuDsVNhj8f4af-IPViuz3LQXry7MUZg06c6fUgV-hwMGoeOPnuMmf4XGThoF_oC5c2ztR-K3Wk5YMYpTqbxdVUXB2KTg__1V-f32UGGyesrIq4EHlgBhyUUV7a2U231nkTGxoiaKdXS-aJMSTwOj-5ivl4Op9n_coqyhOUVV7eOjelM1dMgkclE64vcvfqYRHIqI3Ylvdig7EAmrIHqkETO65hOufGYXfKfnhI5n1Y3wk59tb4xQEfNeemiKCS9_jc4dXTquzePzXC5Z5zwZcnFoQnHSFR5xLC3rCEqXBAgwi3hTdEki8m6EkM8MjPWBQrCSEins_hQgOmOUcdMOTQ9xaxjOtLuHLM4UwrhHpwfzDKZVUeLdHkUoa_aZGqhD1b2YB55quYL7ZNfWWbRFObxCqdGY46O3VAMAk8cmT3sobsxOOseqN2OpY-lHfWyhvXzpBxT_YrV8YCfD7kXx1u2X5OIZQ1JHaN06NCNdkEZqdn7AVdqe7uLOqS9791e1s8t97T9Ui_1nxM86kg4tuG9tL33R_tkkH-2xKyA2kOxEvbQ2EWMwP-WI2CgXG19wSu8oCtCJF6jrZ8V_AKLV_S6j1t90PBc-ERDk2AzBtly07kp3DOc6kmTX-QeZ1ruoYgW_Ih9KGTyw3HFljBgkrH9AYHFN56A6_eDQqlgs73PDi50GLHLqH0HSXw\x26amp;sai\x3dAMfl-YS-sczqScCkr5q0vZtxKIUKicqVb21OpjKqYEQ2GmRpZZ3lOJSqyQmPxYJCC0hbcVspyWY0XP_HluOhZpXU-0cpGWI8rq0kne0aDwGYV7-XHyoQyViNCl5wIKFHKXEdqx1hryXdVk1AI5S81fQB9M_wfYqDxEMsn8hnhCmXmLv3l2JB3jYMglFhZT6jBE_YFzvTl8tDWRgUE5URL3SDPJ9zDW3fSoSIdGh-l1Pm-oCw6ACydNhCqh7ioBuYXX2q4zmw1IlRgUsHbKY_qefv8hlGuKSHSisi1O6QmZo\x26amp;sig\x3dCg0ArKJSzF4cLud4av5j\x26amp;fbs_aeid\x3d%5Bgw_fbsaeid%5D\x26amp;crd\x3daHR0cHM6Ly93c2ouY29t\x26amp;urlfix\x3d1\x26amp;adurl\x3dhttps://www.wsj.com/subscribe/%3Fpage%3Dwsjcore24s%26swg%3Dtrue%26trackingCode%3D6c538b48%26ucid%3DWSJ_DIS_DV3_ACQ_SAL_MDS_BHV_1PW_OR1_BAU%26n2IKsaD9%3Dn2IKsaD9%26Pg9aWOPT%3DPg9aWOPT%26Cp5dKJWb%3DCp5dKJWb%26APCc9OU1%3DAPCc9OU1%26dclid%3D%25edclid!%26gad_source%3D7\x22 attributionsrc\x3d\x22\x22\x3e\x3cimg src\x3d\x22https://s0.2mdn.net/simgad/13688088912146158336?sqp\x3duqWu0g0ICPQDENgEQGQ\x26amp;rs\x3dAOga4qlf_k1JfZTmOeS2eKD6YnqviMQsrw\x22 alt\x3d\x22Advertisement\x22 border\x3d\x220\x22 width\x3d\x22300\x22 height\x3d\x22250\x22 style\x3d\x22display:block\x22\x3e\x3c/a\x3e\x3cscript data-jc\x3d\x2274\x22 data-jc-version\x3d\x22r20250602\x22 data-jcp-a-id\x3d\x22img_anch_CMaI74Cz1Y0DFTiupgQdf6wy8A\x22 data-jcp-for-sure-open-browser\x3d\x22false\x22 data-jcp-for-sure-open-custom-tabs\x3d\x22false\x22 data-jcp-cc-overlay\x3d\x22\x22 data-jcp-cc-button\x3d\x22\x22 data-jcp-is-fledge\x3d\x22false\x22 data-jcp-turtlex-event-ad-signals\x3d\x22\x22 data-jcp-turtlex-event-ad-metadata\x3d\x22\x22\x3e(function(){\x27use strict\x27;/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var l\x3dthis||self;function t(a){l.setTimeout(()\x3d\x3e{throw a;},0)};function u(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382\x3d{});a.__closure__error__context__984382.severity\x3db};let v\x3dvoid 0;function w(){const a\x3dError(\x22int32\x22);u(a,\x22warning\x22);return a};function x(a,b\x3d!1){return b\x26\x26Symbol.for\x26\x26a?Symbol.for(a):a!\x3dnull?Symbol(a):Symbol()}var y\x3dx(),z\x3dx(\x22m_m\x22,!0);const A\x3dx(\x22jas\x22,!0);var B\x3d{};function C(a,b){return b\x3d\x3d\x3dvoid 0?a.h!\x3d\x3dD\x26\x26!!(2\x26(a.g[A]|0)):!!(2\x26b)\x26\x26a.h!\x3d\x3dD}const D\x3d{};const aa\x3dBigInt(Number.MIN_SAFE_INTEGER),ba\x3dBigInt(Number.MAX_SAFE_INTEGER);const ca\x3dNumber.isFinite;function E(a){if(typeof a!\x3d\x3d\x22number\x22)throw w();if(!ca(a))throw w();return a|0};function da(a){return a};function F(a,b,d,c){var e\x3dc!\x3d\x3dvoid 0;c\x3d!!c;const g\x3d[];var f\x3da.length;let h,k\x3d**********,p\x3d!1;const n\x3d!!(b\x2664),q\x3dn?b\x26128?0:-1:void 0;b\x261||(h\x3df\x26\x26a[f-1],h!\x3dnull\x26\x26typeof h\x3d\x3d\x3d\x22object\x22\x26\x26h.constructor\x3d\x3d\x3dObject?(f--,k\x3df):h\x3dvoid 0,!n||b\x26128||e||(p\x3d!0,k\x3d(ea??da)(k-q,q,a,h)+q));b\x3dvoid 0;for(e\x3d0;e\x3cf;e++){let m\x3da[e];if(m!\x3dnull\x26\x26(m\x3dd(m,c))!\x3dnull)if(n\x26\x26e\x3e\x3dk){const r\x3de-q;(b??(b\x3d{}))[r]\x3dm}else g[e]\x3dm}if(h)for(let m in h){a\x3dh[m];if(a\x3d\x3dnull||(a\x3dd(a,c))\x3d\x3dnull)continue;f\x3d+m;let r;n\x26\x26!Number.isNaN(f)\x26\x26(r\x3df+q)\x3ck?g[r]\x3da: (b??(b\x3d{}))[m]\x3da}b\x26\x26(p?g.push(b):g[k]\x3db);return g}function G(a){switch(typeof a){case \x22number\x22:return Number.isFinite(a)?a:\x22\x22+a;case \x22bigint\x22:return a\x3e\x3daa\x26\x26a\x3c\x3dba?Number(a):\x22\x22+a;case \x22boolean\x22:return a?1:0;case \x22object\x22:if(Array.isArray(a)){const b\x3da[A]|0;return a.length\x3d\x3d\x3d0\x26\x26b\x261?void 0:F(a,b,G)}if(a!\x3dnull\x26\x26a[z]\x3d\x3d\x3dB)return H(a);return}return a}let ea;function H(a){a\x3da.g;return F(a,a[A]|0,G)};function fa(a){if(a\x3d\x3dnull){var b\x3d32;a\x3d[]}else{if(!Array.isArray(a))throw Error(\x22narr\x22);b\x3da[A]|0;2048\x26b\x26\x26!(2\x26b)\x26\x26ha();if(b\x26256)throw Error(\x22farr\x22);if(b\x2664)return b\x262048||(a[A]\x3db|2048),a;var d\x3da;b|\x3d64;var c\x3dd.length;if(c){var e\x3dc-1;c\x3dd[e];if(c!\x3dnull\x26\x26typeof c\x3d\x3d\x3d\x22object\x22\x26\x26c.constructor\x3d\x3d\x3dObject){const g\x3db\x26128?0:-1;e-\x3dg;if(e\x3e\x3d1024)throw Error(\x22pvtlmt\x22);for(const f in c){const h\x3d+f;if(h\x3ce)d[h+g]\x3dc[f],delete c[f];else break}b\x3db\x26-8380417|(e\x261023)\x3c\x3c13}}}a[A]\x3db|2112;return a} function ha(){if(y!\x3dnull){var a\x3dv??(v\x3d{});var b\x3da[y]||0;b\x3e\x3d5||(a[y]\x3db+1,a\x3dError(),u(a,\x22incident\x22),t(a))}};function ia(a,b){if(typeof a!\x3d\x3d\x22object\x22)return a;if(Array.isArray(a)){var d\x3da[A]|0;a.length\x3d\x3d\x3d0\x26\x26d\x261?a\x3dvoid 0:d\x262||(!b||4096\x26d||16\x26d?a\x3dI(a,d,!1,b\x26\x26!(d\x2616)):(a[A]|\x3d34,d\x264\x26\x26Object.freeze(a)));return a}if(a!\x3dnull\x26\x26a[z]\x3d\x3d\x3dB){d\x3da.g;const c\x3dd[A]|0;C(a,c)||(c\x262?b\x3d!0:c\x2632\x26\x26!(c\x264096)?(d[A]\x3dc|2,a.h\x3dD,b\x3d!0):b\x3d!1,b?(a\x3dnew a.constructor(d),a.o\x3dD):a\x3dI(d,c));return a}}function I(a,b,d,c){c??(c\x3d!!(34\x26b));a\x3dF(a,b,ia,c);c\x3d32;d\x26\x26(c|\x3d2);b\x3db\x268380609|c;a[A]\x3db;return a};function J(a,b,d){if(a.h\x3d\x3d\x3dD){var c\x3da.g;c\x3dI(c,c[A]|0);c[A]|\x3d2048;a.g\x3dc;a.h\x3dvoid 0;a.o\x3dvoid 0;c\x3d!0}else c\x3d!1;if(!c\x26\x26C(a,a.g[A]|0))throw Error();a\x3da.g;a:{var e\x3da[A]|0;c\x3db+-1;const g\x3da.length-1;if(g\x3e\x3d0\x26\x26c\x3e\x3dg){const f\x3da[g];if(f!\x3dnull\x26\x26typeof f\x3d\x3d\x3d\x22object\x22\x26\x26f.constructor\x3d\x3d\x3dObject){f[b]\x3dd;break a}}c\x3c\x3dg?a[c]\x3dd:d!\x3d\x3dvoid 0\x26\x26(e\x3d(e??a[A]|0)\x3e\x3e13\x261023||536870912,b\x3e\x3de?d!\x3dnull\x26\x26(a[e+-1]\x3d{[b]:d}):a[c]\x3dd)}}function K(a,b,d){if(d!\x3dnull\x26\x26typeof d!\x3d\x3d\x22string\x22)throw Error();J(a,b,d)};var L\x3dclass{constructor(a){this.g\x3dfa(a)}toJSON(){return H(this)}};L.prototype[z]\x3dB;L.prototype.toString\x3dfunction(){return this.g.toString()};var ja\x3dclass extends L{};var ka\x3dclass extends L{};function M(a\x3dwindow){return a};/*  Copyright Google LLC SPDX-License-Identifier: Apache-2.0 */ var N\x3d/^\\s*(?!javascript:)(?:[\\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;let O;function P(a,b,d,c){const e\x3d/^(https?:[^:?]+[/]pcs[/]click[^/]+?)(?:\x26nx[^\x26]+\x26ny[^\x26]+\x26dim[^\x26]+)?(\x26adurl\x3d.*)/.exec(a);return e?e[1]+`\x26nx\x3d${b}\x26ny\x3d${d}\x26dim\x3d${c}`+e[2]:a}function la(a){const b\x3da.currentTarget,d\x3db.querySelector(\x22img[alt]\x22);if(d){const {j:c,l:e,i:g}\x3dQ(a,d.offsetLeft,d.offsetTop,d.width,d.height);a\x3dP(b.href,c,e,g);N.test(a)\x26\x26(b.href\x3da)}} var R\x3d(a,b)\x3d\x3e{M(l)?.fence?.setReportEventDataForAutomaticBeacons({eventType:a,eventData:b,destination:[\x22buyer\x22],once:!0})},S\x3d(a,b)\x3d\x3e{M(l)?.fence?.reportEvent({eventType:\x22click\x22,eventData:a,destination:[b]})};function T(a,b){const d\x3dnew ka;K(d,1,a);a\x3dJSON.stringify(H(O));K(d,4,a);K(d,10,Date.now().toString());R(\x22reserved.top_navigation\x22,JSON.stringify(H(d)));R(\x22reserved.top_navigation_start\x22,JSON.stringify(H(d)));S(JSON.stringify(H(d)),\x22buyer\x22);S(b,\x22component-seller\x22)} function Q(a,b,d,c,e){return{j:+Math.round(a.clientX-b),l:+Math.round(a.clientY-d),i:`${+c}x${+e}`,m:void 0}}function U(a,b){l.AFMA_Communicator!\x3dvoid 0\x26\x26l.AFMA_Communicator.sendMessage!\x3dvoid 0\x26\x26(a.preventDefault(),l.AFMA_Communicator.sendMessage(\x22open\x22,{a:\x22app\x22,u:a.currentTarget.href,system_browser:!0,use_first_package:!0,use_running_process:!0,use_custom_tabs:b}))};const V\x3dfunction(a,b\x3dnull){return b\x26\x26b.getAttribute(\x22data-jc\x22)\x3d\x3d\x3dString(a)?b:document.querySelector(`[${\x22data-jc\x22}\x3d\x22${a}\x22]`)}(74,document.currentScript);if(V\x3d\x3dnull)throw Error(\x22JSC not found 74\x22);const W\x3d{},Z\x3dV.attributes;for(let a\x3dZ.length-1;a\x3e\x3d0;a--){const b\x3dZ[a].name;b.indexOf(\x22data-jcp-\x22)\x3d\x3d\x3d0\x26\x26(W[b.substring(9)]\x3dZ[a].value)} (a\x3d\x3e{const b\x3ddocument.getElementById(a[\x22a-id\x22]),d\x3da[\x22for-sure-open-browser\x22]\x3d\x3d\x3d\x22true\x22,c\x3da[\x22for-sure-open-custom-tabs\x22]\x3d\x3d\x3d\x22true\x22;var e\x3da[\x22cc-overlay\x22],g\x3da[\x22cc-button\x22];const f\x3de?document.getElementById(e):null,h\x3dg?document.getElementById(g):null;e\x3da[\x22is-fledge\x22]\x3d\x3d\x3d\x22true\x22;g\x3df\x26\x26h;O\x3dnew ja;if(e){const k\x3dg?f:b.querySelector(\x22img[alt]\x22);(g?f:b).addEventListener(\x22mousedown\x22,q\x3d\x3e{const {j:m,l:r,i:X,m:Y}\x3dQ(q,k.offsetLeft,k.offsetTop,k.clientWidth,k.clientHeight);m\x26\x26J(O,2,m\x3d\x3dnull?m:E(m));r\x26\x26J(O,3,r\x3d\x3dnull?r: E(r));X\x26\x26K(O,7,X);Y\x26\x26K(O,4,Y)});const p\x3da[\x22turtlex-event-ad-signals\x22],n\x3da[\x22turtlex-event-ad-metadata\x22];h?(h.addEventListener(\x22click\x22,()\x3d\x3e{T(p,n)}),h.addEventListener(\x22auxclick\x22,()\x3d\x3e{T(p,n)})):(b.addEventListener(\x22click\x22,()\x3d\x3e{T(p,n)}),b.addEventListener(\x22auxclick\x22,()\x3d\x3e{T(p,n)}))}else if(b.addEventListener(\x22mousedown\x22,la),g\x26\x26f.addEventListener(\x22mousedown\x22,k\x3d\x3e{const {j:p,l:n,i:q}\x3dQ(k,f.offsetLeft,f.offsetTop,f.clientWidth,f.clientHeight);k\x3dP(h.href,p,n,q);N.test(k)\x26\x26(h.href\x3dk)}),d||c)b.addEventListener(\x22click\x22, k\x3d\x3e{U(k,c)}),h\x26\x26h.addEventListener(\x22click\x22,k\x3d\x3e{U(k,c)})})(W);}).call(this);\x3c/script\x3e');document.write('\x3c/div\x3e');document.write('\x3cstyle\x3ediv{margin:0;padding:0;}.abgcp{height:15px;padding-right:1px;padding-top:1px;padding-left:9px;padding-bottom:13px;right:0px;top:0px;position:absolute;width:31px;z-index:2147483646;}.abgc{display:block;height:15px;position:absolute;right:1px;top:1px;text-rendering:geometricPrecision;z-index:2147483646;}.abgb{display:inline-block;height:15px;}.abgc,.abgcp,.jar .abgc,.jar .abgcp,.jar .cbb{opacity:1;}.abgc{cursor:pointer;}.cbb{cursor:pointer;height:15px;width:15px;z-index:2147483646;background-color:#ffffff;opacity:0;}.cbb svg{position:absolute;top:0;right:0;height:15px;width:15px;stroke:;fill:#00aecd;stroke-width:1.25;}.abgb{position:absolute;right:16px;top:0px;}.cbb{position:absolute;right:0px;top:0px;}.abgs{display:none;height:100%;}.abgl{text-decoration:none;}.abgs svg,.abgb svg{display:inline-block;height:15px;width:auto;vertical-align:top;}.abgc .il-wrap{background-color:#ffffff;height:15px;white-space:nowrap;}.abgc .il-wrap.exp{border-bottom-left-radius:5px;}.abgc .il-text,.abgc .il-icon{display:inline-block;}.abgc .il-text{padding-right:1px;padding-left:5px;height:15px;width:55px;}.abgc .il-icon{height:15px;width:15px;}.abgc .il-text svg{fill:#000000;}.abgc .il-icon svg{fill:#00aecd}\x3c/style\x3e\x3cdiv id\x3d\x22abgcp\x22 class\x3d\x22abgcp\x22\x3e\x3cdiv id\x3d\x22abgc\x22 class\x3d\x22abgc\x22 dir\x3d\x22ltr\x22\x3e\x3cdiv id\x3d\x22abgb\x22 class\x3d\x22abgb\x22\x3e\x3cdiv class\x3d\x22il-wrap\x22\x3e\x3cdiv class\x3d\x22il-icon\x22\x3e\x3csvg xmlns\x3d\x22http://www.w3.org/2000/svg\x22 xmlns:xlink\x3d\x22http://www.w3.org/1999/xlink\x22 viewBox\x3d\x220 0 15 15\x22\x3e\x3ccircle cx\x3d\x226\x22 cy\x3d\x226\x22 r\x3d\x220.67\x22\x3e\x3c/circle\x3e\x3cpath d\x3d\x22M4.2,11.3Q3.3,11.8,3.3,10.75L3.3,4.1Q3.3,3.1,4.3,3.5L10.4,7.0Q12.0,7.5,10.4,8.0L6.65,10.0L6.65,7.75a0.65,0.65,0,1,0,-1.3,0L5.35,10.75a0.9,0.9,0,0,0,1.3,0.8L12.7,8.2Q13.7,7.5,12.7,6.7L3.3,1.6Q2.2,1.3,1.8,2.5L1.8,12.5Q2.2,13.9,3.3,13.3L4.8,12.5A0.3,0.3,0,1,0,4.2,11.3Z\x22\x3e\x3c/path\x3e\x3c/svg\x3e\x3c/div\x3e\x3c/div\x3e\x3c/div\x3e\x3cdiv id\x3d\x22abgs\x22 class\x3d\x22abgs\x22\x3e\x3ca id\x3d\x22abgl\x22 class\x3d\x22abgl\x22 href\x3d\x22https://adssettings.google.com/whythisad?source\x3ddisplay\x26amp;reasons\x3dASp5-QBuoaAIP43StKTrmv7pVknWJeZmPGJ1Co3hCC-SuXuRlg7vWsJHd_MxPiGWBJWRmpEj1T7N7AJgTahH7hp8pLuWK-6b2la10isJM1ID9j-1g1RLMbc1Mb2D5oRYdUMF2kP-cvtqqW60oWXPL-tTadQOUXqUzzhzlMRv_crWbn23L-r07-r7Lc4n3iJmDSoQnhuimx5e3o2ibBfE-E9TOuFXEzMPPQEgljJ0OUXVL8xRTigmfuAqEeYse5WDcUMPr9BySR37BH4A0Vixb12WTXTeJPcPiM6pkIhO-1BFtEKHgmK7AEoSoxdJlG319cRsJDwbGOdx4Oislq1IaT9yQcrTrV7Vv2vKWqHrfW5SJDpz93baAD6C3574ztbxtYcsba5YMOPg-apFE4-W4A5pFpoYOZWr2Lo1vS0zqtjqEBpVbGEu_IRFVEUSaBifm75HZOvJeAvWm-VHrFBaQFkFQ1poNilNoFJSTf2oqOD_6F66qGfT4RgiRXom_c83-fCu-nLC2qx-gePS3OmcsPylRTe-QUpWNlZIl9qOsdjL8TVJhAm5o84VpH6jsT4S4i-DXKTBIJAEJbncFU1UwiTOOzXxLyMF0cngb84saojOENE6It5yMgYH-_QUEO2_aUgamitiy8Ot1hlo3lE3_aM8nq31-y-s3wdbd3n62prW1fFypyn5xwSWMgW5sUPJjeISEXFp-aP9hhD236g3LtSmpl5TW7EIvkCNXJTevtKj3uFYpqzivxc2HD1j2w_fnUSM2AnT1XUUCx12ZXNJ2Mf2NzkyJ9U0DDgrt0V11RUIODn9zVZMADsy7AFmP1M3dn_HmJBaeR6_E1WMIkJlR_V4lECGyohGhXda6cTBbVAX7I70taYWOIcOukTeGFL0oObuYB4fF-vJxDsvdKfJFnqxYE2bVN4VJUqKMXLQUOXV6A2M1Gjw30QuqKqGU6EyZwEPqiaYxFmn9rT5WQhIZ0WccHTgJIUgLKMh5_mUBLwwkWE--N4zZ0XWxPYIq0srhuUzf65qSMyZ42smOl_j3PAxGeUcoxe8hOgZpCGL2VRZmJbp43QARY3nBTG2A639aWL2xpMosQRjM2FA3R2BrzO0Oj3KSmMqgWU-u8P0YCG3rQIiLgG6nM5SaKf79tNWFU0TWuoKBy-bE2Z_VnoPlrfTow0QxUztostPrEeGqHP_S1fzRcyx6bD81T0JBVLXQU03qQ22eZnGRa5Nd7_DruOylGDEbZHne9pmHKQY6vWJKTU6dgVlfNE6thD7yziWphYfFJsMbRGUZeU_ILhUd_z4RecEfE0jZPkNDSRVzuIqVXwwTHNCwSPtjwpSbprtV0-T2y4OBuJvVWQrIl1XTYw2A8SIOaIBhijJ6UsWVgUXlzI-iTycmwolJt5z1SB5YOyAIEdl1nKItvR2qO101KgLyUS7K5Wk64VnKJdvGqgDTqPLNbMX7kQ-R6ABBTkxq41ZXnV2CLeayKk3kxHAqM3hnuZUvHyGQ-Yo0dmEX1U9NXrEZ4H2zgvy7matNj9coRP5z-jxkQlv03-RNjinEdo1zLslNLa4_AjBPkbifCosZ_DWrckpwMkkLPBw9Nrc_ISB-9Uh0kMEvIQp0lHMY9BDsloqsU3tdN0ZxO00OQSBt9QA9xqUwiyuXygWHYM5NpIcy5j_Aw7WSmmStdsmsguQs6-aneR-TSCqQ6lymCITrsbvFTSoTyHBYJVkWnFz7ez9kcDn5isroUEndnHRwgnjzSZVF4XnQ68TADZMKp3Wwq1Jliy-RqH4Nw24-VkfqSpTj9-bFAprsXTT8oYO8mH6J3lHr0w7LKNkpWXculzHxvPVlqmgkipyTprNsMvmBYDE2oNCpOIALvACQLzLyP8MZ5SG16DtB7LHYTNa-yOlIrHXsoydqNF8rIvM5pxN_VrEjQv102MF6XPxMj9rXEwiBVnGuDPUR7ZfDrWOuHRa9oSlnL_jG-KNvqjEF4ZelSbVdPIHnRJFjlKpaoyPWpMlwelKY__s_mVRZhOR9zWl_VFGmYoGef2iDABb4ApmnQWmb55IeuxFpBGU546fyzOn7Q\x22 target\x3d\x22_blank\x22\x3e\x3c/a\x3e\x3c/div\x3e\x3cdiv id\x3d\x22cbb\x22 class\x3d\x22cbb\x22 tabindex\x3d\x220\x22 role\x3d\x22button\x22\x3e\x3csvg width\x3d\x2215\x22 height\x3d\x2215\x22 viewBox\x3d\x220 0 15 15\x22 fill\x3d\x22none\x22 xmlns\x3d\x22http://www.w3.org/2000/svg\x22\x3e\n\x3cg clip-path\x3d\x22url(#clip0_2157_481)\x22\x3e\n\x3crect width\x3d\x2215\x22 height\x3d\x2215\x22 transform\x3d\x22translate(15) rotate(90)\x22 fill\x3d\x22white\x22/\x3e\n\x3crect width\x3d\x2215\x22 height\x3d\x2215\x22 transform\x3d\x22translate(15) rotate(90)\x22 fill\x3d\x22white\x22/\x3e\n\x3ccircle cx\x3d\x227.5\x22 cy\x3d\x2211.5\x22 r\x3d\x221.5\x22 transform\x3d\x22rotate(-180 7.5 11.5)\x22 fill\x3d\x22#00aecd\x22/\x3e\n\x3ccircle cx\x3d\x227.5\x22 cy\x3d\x227.5\x22 r\x3d\x221.5\x22 transform\x3d\x22rotate(-180 7.5 7.5)\x22 fill\x3d\x22#00aecd\x22/\x3e\n\x3ccircle cx\x3d\x227.5\x22 cy\x3d\x223.5\x22 r\x3d\x221.5\x22 transform\x3d\x22rotate(-180 7.5 3.5)\x22 fill\x3d\x22#00aecd\x22/\x3e\n\x3c/g\x3e\n\x3cdefs\x3e\n\x3cclipPath id\x3d\x22clip0_2157_481\x22\x3e\n\x3crect width\x3d\x2215\x22 height\x3d\x2215\x22 fill\x3d\x22white\x22 transform\x3d\x22translate(15) rotate(90)\x22/\x3e\n\x3c/clipPath\x3e\n\x3c/defs\x3e\n\x3c/svg\x3e\x3c/div\x3e\x3c/div\x3e\x3c/div\x3e\x3cstyle\x3e.mute_panel{z-index:2147483646;}.abgac{position:absolute;left:0px;top:0px;z-index:2147483646;display:none;width:100%;height:100%;background-color:#FAFAFA;}.mlsc{height:100%;display:flex;justify-content:center;align-items:center;}.mls{animation:mlskf 2s linear infinite;height:50%;width:50%;}.mlsd{stroke-dasharray:1,189;stroke-dashoffset:0;animation:mlsdkf 1.4s ease-in-out infinite;}@keyframes mlskf{100%{transform:rotate(360deg);}}@keyframes mlsdkf{0%{stroke-dasharray:1,189;stroke-dashoffset:0;}50%{stroke-dasharray:134,189;stroke-dashoffset:-53px;}100%{stroke-dasharray:134,189;stroke-dashoffset:-188px;}}\x3c/style\x3e\x3cdiv id\x3d\x22mute_panel\x22 class\x3d\x22mute_panel\x22 aria-hidden\x3d\x22true\x22\x3e\x3cdiv id\x3d\x22abgac\x22 class\x3d\x22abgac\x22 aria-hidden\x3d\x22true\x22\x3e\x3cdiv id\x3d\x22mlsc\x22 class\x3d\x22mlsc\x22\x3e\x3csvg class\x3d\x22mls\x22 viewBox\x3d\x2250 50 100 100\x22\x3e\x3ccircle class\x3d\x22mlsd\x22 cx\x3d\x22100\x22 cy\x3d\x22100\x22 r\x3d\x2230\x22 fill\x3d\x22none\x22 stroke\x3d\x22#9E9E9E\x22 stroke-width\x3d\x223\x22/\x3e\x3c/svg\x3e\x3c/div\x3e\x3c/div\x3e\x3c/div\x3e\x3cscript data-jc\x3d\x2260\x22 src\x3d\x22https://pagead2.googlesyndication.com/pagead/js/r20250602/r20110914/abg_lite_fy2021.js\x22 async data-jc-version\x3d\x22r20250602\x22 data-jcp-attribution-data\x3d\x22[[null,\x26quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png\x26quot;,null,\x26quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png\x26quot;,\x26quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai\x3dCbDtHDv4-aMjJKrjcmtUP_9jKgQ-Qwd-3f5Snp96HFImi1OOnQhABIMT8ip0BYMm2iYfMo8AXoAH2q8jLKsgBCagDAaoEkAJP0Mr6y1i_7MfuG2v-jKXBoZ9uAsPH3N2cj1ScuVLBOU5Hl_uDlmHLZ5Dr-CX4UriZolMofGjfPijiqY0r5u-O6gujIlgbO5y9KuTYQuvLperibjVFpBbGZbi-aajA0LHhdV59kQTfRiV0VIUEepnNhXRERJkh9Krs496Pp2XCovzcRdIxAEMjBLa9ux50b5FHN6JHNndanh0gmVgP4jIT48TdLrCI7XgZtQIlNux8PSmkSlTewKBu1WLqeGAYYZvu0LvzsrfNU_36ScejaD-Ws5Vlb40QdgIuD8UeowtSnryRLUOSbPNzxGcHO230TARvKrUbtxHHXz2PwnAPFY2BR-TT0EkbYE8UjjY_SPSCAMAEq6_hxY8F4AQDiAXawv36UpAGAaAGTIAH9uOYqwWoB9XJG6gH2baxAqgHpr4bqAfMzrECqAfz0RuoB5bYG6gHqpuxAqgH4L2xAqgHjs4bqAeT2BuoB_DgG6gH7paxAqgH_p6xAqgHr76xAqgHmgaoB_-esQKoB9-fsQKoB_jCsQKoB_vCsQLYBwDSCCkIgGEQARidATICigI6DYBAgMCAgICAqIACoANIvf3BOljgtOyAs9WNA4AKA5gLAcgLAYAMAaoNAlVT4g0TCNiE7YCz1Y0DFTiupgQdf6wy8OoNEwi7_-2As9WNAxU4rqYEHX-sMvCwE_X38hzYEw2IFALYFAHQFQHKFgIKAPgWAYAXAbIXQBgCKjwvMjI5NjAyMTIwOTAvU21hcnRiYWNrZ3JvdW5kY2hlY2tzX1MyU19NaWRjb250ZW50QmFubmVyMl9ST1OyGAkSAvJOGEwiAQCyGQE1\\u0026sigh\x3dhTos7V-jUFY\\u0026cid\x3dCAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bRgB\x26quot;,\x26quot;aUPLHcHV1O4IlKen3ocUENrC_fpSGPbY6t8FIgd3c2ouY29tMggIBRMYxc0KFEIXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjdIElgAcAE\x26quot;,[\x26quot;user_feedback_menu_interaction\x26quot;,\x26quot;\x26quot;,0],null,null,null,null,\x26quot;What was wrong with this ad?\x26quot;,null,\x26quot;https://googleads.g.doubleclick.net/pagead/images/mtad/back_blue.png\x26quot;,\x26quot;Thanks for the feedback!\x26quot;,\x26quot;We’ll review this ad to improve the experience in the future.\x26quot;,\x26quot;Thanks for the feedback!\x26quot;,\x26quot;We’ll use your feedback to review ads on this site.\x26quot;,null,null,null,\x26quot;Closing ad: %1$d\x26quot;,null,\x26quot;https://googleads.g.doubleclick.net/pagead/images/mtad/ad_choices_blue.png\x26quot;,\x26quot;https://www.google.com/url?ct\x3dabg\\u0026q\x3dhttps://www.google.com/adsense/support/bin/request.py%3Fcontact%3Dabg_afc%26url%3D%26gl%3DUS%26hl%3Den%26ai0%3D\\u0026usg\x3dAOvVaw3TcfxaWsjgYHkyyJUvJpAT\x26quot;,\x26quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png\x26quot;,0,[[\x26quot;Send feedback\x26quot;,[\x26quot;user_feedback_menu_option\x26quot;,\x26quot;1\x26quot;,1],[\x26quot;What was wrong with this ad?\x26quot;,[[\x26quot;Seen this ad multiple times\x26quot;,[\x26quot;mute_survey_option\x26quot;,\x26quot;2\x26quot;,1]],[\x26quot;Ad covered content\x26quot;,[\x26quot;mute_survey_option\x26quot;,\x26quot;3\x26quot;,1]],[\x26quot;Not interested in this ad\x26quot;,[\x26quot;mute_survey_option\x26quot;,\x26quot;7\x26quot;,1]],[\x26quot;Ad was inappropriate\x26quot;,[\x26quot;mute_survey_option\x26quot;,\x26quot;8\x26quot;,1]]]],[\x26quot;user_feedback_undo\x26quot;,\x26quot;1\x26quot;,1]]],[\x26quot;https://googleads.g.doubleclick.net/pagead/images/adchoices/iconx2-000000.png\x26quot;,\x26quot;AdChoices\x26quot;,\x26quot;Ad closed by %1$s\x26quot;,null,\x26quot;https://www.gstatic.com/images/branding/googlelogo/2x/googlelogo_dark_color_84x28dp.png\x26quot;,\x26quot;Send feedback\x26quot;,\x26quot;Thanks. Feedback improves Google ads\x26quot;,null,null,null,null,null,null,\x26quot;See my Google ad settings\x26quot;,null,\x26quot;https://www.gstatic.com\x26quot;,\x26quot;\x26quot;,\x26quot;Ads by %1$s\x26quot;,\x26quot;Ad settings\x26quot;,\x26quot;https://adssettings.google.com\x26quot;,null,null,null,0,null,null,null,0,1],\x26quot;APyvJYwAAAcwW1tbW251bGwsWzIsMjAsNTNdLG51bGwsbnVsbCxudWxsLFsiOTIwMDIiXSxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsW1siRG93IEpvbmVzIFx1MDAyNiBDb21wYW55IEluYyIsIlVTIiwxLG51bGwsMV0sIjE1NDMxNTQ4MDYiXV0sW251bGwsImh0dHBzOi8vZ29vZ2xlYWRzLmcuZG91YmxlY2xpY2submV0L3BhZ2VhZC9pbnRlcmFjdGlvbi8_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-nDCfnjSVreLAj3BL7ERQJh0I-yMPy0GK9JoFm81BAVgM3Ce8Y-sECL1e5tEsxLT-pp5X4MpR7d1xINKeClCH7rB0sz4Mqruj81X7vmzwcShCdVf6dc48UIWsR-63DrOUIZsrvIBJb_vkCnuzz3DLJhXZ7gGlqP7lTzzV3qHptHLYAyE-7UP49qLgY7-yMll10tOrWVjr7lEboRcUj4uVoX3r3uiiH5DNSMcKXjwxv-ne76cVw7q9Qdrw348KRL2EL5eVFE_9gNySdn28srR2xcL1eUxf1wUyqMV-DqFZpYLzamRxUQIF8,S2e63arAYz6nJFH7HndtBA\x26quot;,\x26quot;https://adssettings.google.com/whythisad?source\x3ddisplay\\u0026reasons\x3dASp5-QBuoaAIP43StKTrmv7pVknWJeZmPGJ1Co3hCC-SuXuRlg7vWsJHd_MxPiGWBJWRmpEj1T7N7AJgTahH7hp8pLuWK-6b2la10isJM1ID9j-1g1RLMbc1Mb2D5oRYdUMF2kP-cvtqqW60oWXPL-tTadQOUXqUzzhzlMRv_crWbn23L-r07-r7Lc4n3iJmDSoQnhuimx5e3o2ibBfE-E9TOuFXEzMPPQEgljJ0OUXVL8xRTigmfuAqEeYse5WDcUMPr9BySR37BH4A0Vixb12WTXTeJPcPiM6pkIhO-1BFtEKHgmK7AEoSoxdJlG319cRsJDwbGOdx4Oislq1IaT9yQcrTrV7Vv2vKWqHrfW5SJDpz93baAD6C3574ztbxtYcsba5YMOPg-apFE4-W4A5pFpoYOZWr2Lo1vS0zqtjqEBpVbGEu_IRFVEUSaBifm75HZOvJeAvWm-VHrFBaQFkFQ1poNilNoFJSTf2oqOD_6F66qGfT4RgiRXom_c83-fCu-nLC2qx-gePS3OmcsPylRTe-QUpWNlZIl9qOsdjL8TVJhAm5o84VpH6jsT4S4i-DXKTBIJAEJbncFU1UwiTOOzXxLyMF0cngb84saojOENE6It5yMgYH-_QUEO2_aUgamitiy8Ot1hlo3lE3_aM8nq31-y-s3wdbd3n62prW1fFypyn5xwSWMgW5sUPJjeISEXFp-aP9hhD236g3LtSmpl5TW7EIvkCNXJTevtKj3uFYpqzivxc2HD1j2w_fnUSM2AnT1XUUCx12ZXNJ2Mf2NzkyJ9U0DDgrt0V11RUIODn9zVZMADsy7AFmP1M3dn_HmJBaeR6_E1WMIkJlR_V4lECGyohGhXda6cTBbVAX7I70taYWOIcOukTeGFL0oObuYB4fF-vJxDsvdKfJFnqxYE2bVN4VJUqKMXLQUOXV6A2M1Gjw30QuqKqGU6EyZwEPqiaYxFmn9rT5WQhIZ0WccHTgJIUgLKMh5_mUBLwwkWE--N4zZ0XWxPYIq0srhuUzf65qSMyZ42smOl_j3PAxGeUcoxe8hOgZpCGL2VRZmJbp43QARY3nBTG2A639aWL2xpMosQRjM2FA3R2BrzO0Oj3KSmMqgWU-u8P0YCG3rQIiLgG6nM5SaKf79tNWFU0TWuoKBy-bE2Z_VnoPlrfTow0QxUztostPrEeGqHP_S1fzRcyx6bD81T0JBVLXQU03qQ22eZnGRa5Nd7_DruOylGDEbZHne9pmHKQY6vWJKTU6dgVlfNE6thD7yziWphYfFJsMbRGUZeU_ILhUd_z4RecEfE0jZPkNDSRVzuIqVXwwTHNCwSPtjwpSbprtV0-T2y4OBuJvVWQrIl1XTYw2A8SIOaIBhijJ6UsWVgUXlzI-iTycmwolJt5z1SB5YOyAIEdl1nKItvR2qO101KgLyUS7K5Wk64VnKJdvGqgDTqPLNbMX7kQ-R6ABBTkxq41ZXnV2CLeayKk3kxHAqM3hnuZUvHyGQ-Yo0dmEX1U9NXrEZ4H2zgvy7matNj9coRP5z-jxkQlv03-RNjinEdo1zLslNLa4_AjBPkbifCosZ_DWrckpwMkkLPBw9Nrc_ISB-9Uh0kMEvIQp0lHMY9BDsloqsU3tdN0ZxO00OQSBt9QA9xqUwiyuXygWHYM5NpIcy5j_Aw7WSmmStdsmsguQs6-aneR-TSCqQ6lymCITrsbvFTSoTyHBYJVkWnFz7ez9kcDn5isroUEndnHRwgnjzSZVF4XnQ68TADZMKp3Wwq1Jliy-RqH4Nw24-VkfqSpTj9-bFAprsXTT8oYO8mH6J3lHr0w7LKNkpWXculzHxvPVlqmgkipyTprNsMvmBYDE2oNCpOIALvACQLzLyP8MZ5SG16DtB7LHYTNa-yOlIrHXsoydqNF8rIvM5pxN_VrEjQv102MF6XPxMj9rXEwiBVnGuDPUR7ZfDrWOuHRa9oSlnL_jG-KNvqjEF4ZelSbVdPIHnRJFjlKpaoyPWpMlwelKY__s_mVRZhOR9zWl_VFGmYoGef2iDABb4ApmnQWmb55IeuxFpBGU546fyzOn7Q\x26quot;,\x26quot;Why this ad?\x26quot;,1,0],null,null,0,null,0,0,1,0,0,0,1,0,0,0,null,0,1,0,null,[[\x26quot;post_click_menu_height_when_bottom_anchor_on_mobile\x26quot;,\x26quot;0\x26quot;],[\x26quot;show_ad_after_mute\x26quot;,\x26quot;true\x26quot;],[\x26quot;jake_ui_extension\x26quot;,\x26quot;jake_default_ui\x26quot;]],250,300,0,null,null,0,null,null,\x26quot;right\x26quot;,0,null,\x26quot;r20250602/r20110914\x26quot;,null,0,null,0]\x22\x3e\x3c/script\x3e');if (!window.mraid) {document.write('\x3c/div\x3e');}(function() {(function(){var s = document.createElement('script');s.setAttribute('data-jc', '86');s.src = 'https://pagead2.googlesyndication.com/pagead/js/r20250602/r20110914/elements/html/omrhp_fy2021.js';s.setAttribute('data-jc-version', 'r20250602');var ss = document.getElementsByTagName('script')[0];if (ss &amp;&amp; ss.parentNode) {ss.parentNode.insertBefore(s, ss);}})();window.dicnf = {eavp: true,ebrp: true,apfa: true,atsb: true,bvst: 'r20250602',eeid: 'Dv4-aMjJKrjcmtUP_9jKgQ8',aunb: true,adsg: '',admetadata: '',uffp: true,espa: true,};(function(){function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba=typeof Object.defineProperties==&quot;function&quot;?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a}; function ca(a){a=[&quot;object&quot;==typeof globalThis&amp;&amp;globalThis,a,&quot;object&quot;==typeof window&amp;&amp;window,&quot;object&quot;==typeof self&amp;&amp;self,&quot;object&quot;==typeof global&amp;&amp;global];for(var b=0;b<a.length;++b){var c=a[b];if(c&amp;&amp;c.Math==Math)return c}throw Error(&quot;Cannot find global object&quot;);}var da=ca(this);function n(a,b){if(b)a:{var c=da;a=a.split(&quot;.&quot;);for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&amp;&amp;b!=null&amp;&amp;ba(c,a,{configurable:!0,writable:!0,value:b})}} var fa=typeof Object.create==&quot;function&quot;?Object.create:function(a){function b(){}b.prototype=a;return new b},ha;if(typeof Object.setPrototypeOf==&quot;function&quot;)ha=Object.setPrototypeOf;else{var ia;a:{var ja={a:!0},ka={};try{ka.__proto__=ja;ia=ka.a;break a}catch(a){}ia=!1}ha=ia?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+&quot; is not extensible&quot;);return a}:null}var la=ha; function r(a,b){a.prototype=fa(b.prototype);a.prototype.constructor=a;if(la)la(a,b);else for(var c in b)if(c!=&quot;prototype&quot;)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&amp;&amp;Object.defineProperty(a,c,d)}else a[c]=b[c];a.ga=b.prototype}function ma(a){var b=typeof Symbol!=&quot;undefined&quot;&amp;&amp;Symbol.iterator&amp;&amp;a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length==&quot;number&quot;)return{next:aa(a)};throw Error(String(a)+&quot; is not an iterable or ArrayLike&quot;);} var na=typeof Object.assign==&quot;function&quot;?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&amp;&amp;(a[e]=d[e])}return a};n(&quot;Object.assign&quot;,function(a){return a||na});function oa(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b}n(&quot;globalThis&quot;,function(a){return a||da});n(&quot;Object.is&quot;,function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&amp;&amp;c!==c}}); n(&quot;Array.prototype.includes&quot;,function(a){return a?a:function(b,c){var d=this;d instanceof String&amp;&amp;(d=String(d));var e=d.length;c=c||0;for(c<0&amp;&amp;(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b||Object.is(g,b))return!0}return!1}}); n(&quot;String.prototype.includes&quot;,function(a){return a?a:function(b,c){if(this==null)throw new TypeError(&quot;The 'this' value for String.prototype.includes must not be null or undefined&quot;);if(b instanceof RegExp)throw new TypeError(&quot;First argument to String.prototype.includes must not be a regular expression&quot;);return this.indexOf(b,c||0)!==-1}});n(&quot;Number.isFinite&quot;,function(a){return a?a:function(b){return typeof b!==&quot;number&quot;?!1:!isNaN(b)&amp;&amp;b!==Infinity&amp;&amp;b!==-Infinity}});n(&quot;Number.MAX_SAFE_INTEGER&quot;,function(){return 9007199254740991}); n(&quot;Number.MIN_SAFE_INTEGER&quot;,function(){return-9007199254740991});n(&quot;Number.isNaN&quot;,function(a){return a?a:function(b){return typeof b===&quot;number&quot;&amp;&amp;isNaN(b)}});/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var x=this||self;function pa(a){x.setTimeout(function(){throw a;},0)};var qa=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a===&quot;string&quot;)return typeof b!==&quot;string&quot;||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&amp;&amp;a[c]===b)return c;return-1},ra=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a===&quot;string&quot;?a.split(&quot;&quot;):a,e=0;e<c;e++)e in d&amp;&amp;b.call(void 0,d[e],e,a)};function sa(a){sa[&quot; &quot;](a);return a}sa[&quot; &quot;]=function(){};var ta={},A=null;var ua=void 0;var va=typeof Symbol===&quot;function&quot;&amp;&amp;typeof Symbol()===&quot;symbol&quot;;function wa(a,b,c){return typeof Symbol===&quot;function&quot;&amp;&amp;typeof Symbol()===&quot;symbol&quot;?(c===void 0?0:c)&amp;&amp;Symbol.for&amp;&amp;a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var xa=wa(&quot;jas&quot;,void 0,!0),ya=wa(void 0,&quot;0actk&quot;),za=wa(&quot;m_m&quot;,&quot;fa&quot;,!0);var Aa={ea:{value:0,configurable:!0,writable:!0,enumerable:!1}},Ba=Object.defineProperties,B=va?xa:&quot;ea&quot;,Ca,Da=[];C(Da,7);Ca=Object.freeze(Da);function Ea(a,b){va||B in a||Ba(a,Aa);a[B]|=b}function C(a,b){va||B in a||Ba(a,Aa);a[B]=b};var Fa={};function Ga(a,b){return b===void 0?a.g!==D&amp;&amp;!!(2&amp;(a.m[B]|0)):!!(2&amp;b)&amp;&amp;a.g!==D}var D={};var Ha=typeof x.BigInt===&quot;function&quot;&amp;&amp;typeof x.BigInt(0)===&quot;bigint&quot;;var Ja=Number.MIN_SAFE_INTEGER.toString(),Ka=Ha?BigInt(Number.MIN_SAFE_INTEGER):void 0,La=Number.MAX_SAFE_INTEGER.toString(),Ma=Ha?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Na(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};function Oa(a){if(typeof a!==&quot;boolean&quot;){var b=typeof a;throw Error(&quot;Expected boolean but got &quot;+(b!=&quot;object&quot;?b:a?Array.isArray(a)?&quot;array&quot;:b:&quot;null&quot;)+&quot;: &quot;+a);}return a};function Pa(a){return a};function Qa(a,b,c,d){var e=d!==void 0;d=!!d;var g=[],f=a.length,h=**********,k=!1,l=!!(b&amp;64),p=l?b&amp;128?0:-1:void 0;if(!(b&amp;1)){var m=f&amp;&amp;a[f-1];m!=null&amp;&amp;typeof m===&quot;object&quot;&amp;&amp;m.constructor===Object?(f--,h=f):m=void 0;if(l&amp;&amp;!(b&amp;128)&amp;&amp;!e){k=!0;var q;h=((q=Ra)!=null?q:Pa)(h-p,p,a,m)+p}}b=void 0;for(e=0;e<f;e++)if(q=a[e],q!=null&amp;&amp;(q=c(q,d))!=null)if(l&amp;&amp;e>=h){var y=e-p,t=void 0;((t=b)!=null?t:b={})[y]=q}else g[e]=q;if(m)for(var v in m)a=m[v],a!=null&amp;&amp;(a=c(a,d))!=null&amp;&amp;(f=+v,e=void 0,l&amp;&amp;!Number.isNaN(f)&amp;&amp; (e=f+p)<h?g[e]=a:(f=void 0,((f=b)!=null?f:b={})[v]=a));b&amp;&amp;(k?g.push(b):g[h]=b);return g}function Sa(a){switch(typeof a){case &quot;number&quot;:return Number.isFinite(a)?a:&quot;&quot;+a;case &quot;bigint&quot;:return(Ha?a>=Ka&amp;&amp;a<=Ma:a[0]===&quot;-&quot;?Na(a,Ja):Na(a,La))?Number(a):&quot;&quot;+a;case &quot;boolean&quot;:return a?1:0;case &quot;object&quot;:if(Array.isArray(a)){var b=a[B]|0;return a.length===0&amp;&amp;b&amp;1?void 0:Qa(a,b,Sa)}if(a!=null&amp;&amp;a[za]===Fa)return E(a);return}return a}var Ra;function E(a){a=a.m;return Qa(a,a[B]|0,Sa)};function G(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&amp;&amp;(e=e&amp;-8380417|(b&amp;1023)<<13)}else{if(!Array.isArray(a))throw Error(&quot;narr&quot;);e=a[B]|0;2048&amp;e&amp;&amp;!(2&amp;e)&amp;&amp;Ta();if(e&amp;256)throw Error(&quot;farr&quot;);if(e&amp;64)return d!==0||e&amp;2048||C(a,e|2048),a;if(c&amp;&amp;(e|=128,c!==a[0]))throw Error(&quot;mid&quot;);a:{c=a;e|=64;var g=c.length;if(g){var f=g-1,h=c[f];if(h!=null&amp;&amp;typeof h===&quot;object&quot;&amp;&amp;h.constructor===Object){b=e&amp;128?0:-1;f-=b;if(f>=1024)throw Error(&quot;pvtlmt&quot;);for(var k in h)g=+k,g<f&amp;&amp;(c[g+b]=h[k], delete h[k]);e=e&amp;-8380417|(f&amp;1023)<<13;break a}}if(b){k=Math.max(b,g-(e&amp;128?0:-1));if(k>1024)throw Error(&quot;spvt&quot;);e=e&amp;-8380417|(k&amp;1023)<<13}}}e|=64;d===0&amp;&amp;(e|=2048);C(a,e);return a}function Ta(){if(ya!=null){var a;var b=(a=ua)!=null?a:ua={};a=b[ya]||0;a>=5||(b[ya]=a+1,b=Error(),b.__closure__error__context__984382||(b.__closure__error__context__984382={}),b.__closure__error__context__984382.severity=&quot;incident&quot;,pa(b))}};function Ua(a,b){if(typeof a!==&quot;object&quot;)return a;if(Array.isArray(a)){var c=a[B]|0;a.length===0&amp;&amp;c&amp;1?a=void 0:c&amp;2||(!b||4096&amp;c||16&amp;c?a=Va(a,c,!1,b&amp;&amp;!(c&amp;16)):(Ea(a,34),c&amp;4&amp;&amp;Object.freeze(a)));return a}if(a!=null&amp;&amp;a[za]===Fa){b=a.m;c=b[B]|0;if(!Ga(a,c)){if(c&amp;2)var d=!0;else!(c&amp;32)||c&amp;4096?d=!1:(C(b,c|2),a.g=D,d=!0);d?(a=new a.constructor(b),a.i=D):a=Va(b,c)}return a}}function Va(a,b,c,d){d!=null||(d=!!(34&amp;b));a=Qa(a,b,Ua,d);d=32;c&amp;&amp;(d|=2);b=b&amp;8380609|d;C(a,b);return a} function Wa(a){if(a.g===D){var b=a.m;b=Va(b,b[B]|0);Ea(b,2048);a.m=b;a.g=void 0;a.i=void 0;b=!0}else b=!1;if(!b&amp;&amp;Ga(a,a.m[B]|0))throw Error();};function Xa(a,b,c){Wa(a);var d=a.m;Ya(d,d[B]|0,b,c);return a}function Ya(a,b,c,d){var e=c+-1,g=a.length-1;if(g>=0&amp;&amp;e>=g){var f=a[g];if(f!=null&amp;&amp;typeof f===&quot;object&quot;&amp;&amp;f.constructor===Object)return f[c]=d,b}if(e<=g)return a[e]=d,b;if(d!==void 0){var h;g=((h=b)!=null?h:b=a[B]|0)>>13&amp;1023||536870912;c>=g?d!=null&amp;&amp;(e={},a[g+-1]=(e[c]=d,e)):a[e]=d}return b}function H(a,b,c){if(c!=null&amp;&amp;typeof c!==&quot;string&quot;)throw Error();return Xa(a,b,c)};function I(a,b,c){this.m=G(a,b,c)}I.prototype.toJSON=function(){return E(this)};I.prototype[za]=Fa;I.prototype.toString=function(){return this.m.toString()};function Za(a){this.m=G(a)}r(Za,I);function J(a,b){this.key=a;this.defaultValue=b===void 0?!1:b;this.valueType=&quot;boolean&quot;};var $a=new J(&quot;100000&quot;),ab=new J(&quot;45357156&quot;,!0),bb=new J(&quot;45350890&quot;),cb=new J(&quot;45628745&quot;,!0),db=new J(&quot;45414892&quot;);var eb=RegExp(&quot;ad\\.doubleclick\\.net/(ddm/trackimp|pcs/view)&quot;),fb=[&quot;A7CQXglZzTrThjGTBEn1rWTxHOEtkWivwzgea+NjyardrwlieSjVuyG44PkYgIPGs8Q9svD8sF3Yedn0BBBjXAkAAACFeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ==&quot;];function gb(a,b){return a.substring(a.length-7)==&quot;&amp;adurl=&quot;?a.substring(0,a.length-7)+b+&quot;&amp;adurl=&quot;:a+b};function L(a){return a=a===void 0?window:a};var M=x.dicnf||{};function hb(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};function ib(a){try{var b;if(b=!!a&amp;&amp;a.location.href!=null)a:{try{sa(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}}function jb(a,b){b=b===void 0?!1:b;var c=c===void 0?x:c;for(var d=0;c&amp;&amp;d++<40&amp;&amp;(!b&amp;&amp;!ib(c)||!a(c));)a:{try{var e=c.parent;if(e&amp;&amp;e!=c){c=e;break a}}catch(g){}c=null}}function kb(){if(!globalThis.crypto)return Math.random();try{var a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch(b){return Math.random()}} function lb(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&amp;&amp;b(a[c],c,a)}function N(a,b){b=b===void 0?document:b;return b.createElement(String(a).toLowerCase())};var O=[];function mb(){var a=O;O=[];a=ma(a);for(var b=a.next();!b.done;b=a.next()){b=b.value;try{b()}catch(c){}}};function nb(a,b,c){typeof a.addEventListener===&quot;function&quot;&amp;&amp;a.addEventListener(b,c,!1)}function ob(a,b,c){typeof a.removeEventListener===&quot;function&quot;&amp;&amp;a.removeEventListener(b,c,!1)}function pb(a,b){a.readyState===&quot;complete&quot;||a.readyState===&quot;interactive&quot;?(O.push(b),O.length===1&amp;&amp;(window.Promise?Promise.resolve().then(mb):(a=window.setImmediate,typeof a===&quot;function&quot;?a(mb):setTimeout(mb,0)))):a.addEventListener(&quot;DOMContentLoaded&quot;,b)};function P(a,b,c,d){qb(x,a,b===void 0?null:b,!1,c===void 0?!1:c,d===void 0?!1:d)}function qb(a,b,c,d,e,g){g=g===void 0?!1:g;a.google_image_requests||(a.google_image_requests=[]);var f=N(&quot;IMG&quot;,a.document);if(c||e){var h=function(k){c&amp;&amp;c(k);if(e){k=a.google_image_requests;var l=qa(k,f);l>=0&amp;&amp;Array.prototype.splice.call(k,l,1)}ob(f,&quot;load&quot;,h);ob(f,&quot;error&quot;,h)};nb(f,&quot;load&quot;,h);nb(f,&quot;error&quot;,h)}d&amp;&amp;(f.referrerPolicy=&quot;no-referrer&quot;);g&amp;&amp;(f.attributionSrc=&quot;&quot;);f.src=b;a.google_image_requests.push(f)} var rb=hb(function(){return&quot;referrerPolicy&quot;in N(&quot;IMG&quot;)});function sb(){this.g={}}function Q(a){tb||(tb=new ub);var b=tb.g[a.key];if(a.valueType===&quot;proto&quot;){try{var c=JSON.parse(b);if(Array.isArray(c))return c}catch(d){}return a.defaultValue}return typeof b===typeof a.defaultValue?b:a.defaultValue};function ub(){this.g={};var a=document.currentScript;a=(a=(a=a===void 0?null:a)&amp;&amp;a.getAttribute(&quot;data-jc&quot;)===&quot;0&quot;?a:document.querySelector('[data-jc=&quot;0&quot;]'))&amp;&amp;a.getAttribute(&quot;data-jc-flags&quot;)||&quot;&quot;;try{var b=JSON.parse(a)[0];a=&quot;&quot;;for(var c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^&quot;\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003&quot;.charCodeAt(c%10));this.g=JSON.parse(a)}catch(d){}}var tb;r(ub,sb);function Bb(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||&quot;&quot;;this.id=b.id||&quot;jserror&quot;};function Cb(a){var b=a.toString();a.name&amp;&amp;b.indexOf(a.name)==-1&amp;&amp;(b+=&quot;: &quot;+a.name);a.message&amp;&amp;b.indexOf(a.message)==-1&amp;&amp;(b+=&quot;: &quot;+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&amp;&amp;(a=c+&quot;\n&quot;+a);for(var d;a!=d;)d=a,a=a.replace(RegExp(&quot;((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2&quot;),&quot;$1&quot;);b=a.replace(RegExp(&quot;\n *&quot;,&quot;g&quot;),&quot;\n&quot;);break a}catch(e){b=c;break a}b=void 0}return b};var Db=RegExp(&quot;^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)&quot;);function Eb(a,b){this.g=a;this.i=b}function Fb(a,b){this.url=a;this.g=!!b;this.depth=null};var Gb=null;function Hb(){var a=a===void 0?x:a;return(a=a.performance)&amp;&amp;a.now&amp;&amp;a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Ib(){var a=a===void 0?x:a;return(a=a.performance)&amp;&amp;a.now?a.now():null};function Jb(a,b){var c=Ib()||Hb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()};var R=x.performance,Kb=!!(R&amp;&amp;R.mark&amp;&amp;R.measure&amp;&amp;R.clearMarks),S=hb(function(){var a;if(a=Kb){var b=b===void 0?window:b;if(Gb===null){Gb=&quot;&quot;;try{a=&quot;&quot;;try{a=b.top.location.hash}catch(d){a=b.location.hash}if(a){var c=a.match(/\bdeid=([\d,]+)/);Gb=c?c[1]:&quot;&quot;}}catch(d){}}b=Gb;a=!!b.indexOf&amp;&amp;b.indexOf(&quot;1337&quot;)>=0}return a}); function Lb(){var a=window;this.i=[];this.j=a||x;var b=null;a&amp;&amp;(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=S()||(b!=null?b:Math.random()<1)}function Mb(a){a&amp;&amp;R&amp;&amp;S()&amp;&amp;(R.clearMarks(&quot;goog_&quot;+a.label+&quot;_&quot;+a.uniqueId+&quot;_start&quot;),R.clearMarks(&quot;goog_&quot;+a.label+&quot;_&quot;+a.uniqueId+&quot;_end&quot;))}Lb.prototype.start=function(a,b){if(!this.g)return null;a=new Jb(a,b);b=&quot;goog_&quot;+a.label+&quot;_&quot;+a.uniqueId+&quot;_start&quot;;R&amp;&amp;S()&amp;&amp;R.mark(b);return a}; Lb.prototype.end=function(a){if(this.g&amp;&amp;typeof a.value===&quot;number&quot;){a.duration=(Ib()||Hb())-a.value;var b=&quot;goog_&quot;+a.label+&quot;_&quot;+a.uniqueId+&quot;_end&quot;;R&amp;&amp;S()&amp;&amp;R.mark(b);!this.g||this.i.length>2048||this.i.push(a)}};function Nb(){this.j=&quot;&amp;&quot;;this.i={};this.l=0;this.g=[]}function Ob(a,b){var c={};c[a]=b;return[c]}function Pb(a,b,c,d,e){var g=[];lb(a,function(f,h){(f=Qb(f,b,c,d,e))&amp;&amp;g.push(h+&quot;=&quot;+f)});return g.join(b)} function Qb(a,b,c,d,e){if(a==null)return&quot;&quot;;b=b||&quot;&amp;&quot;;c=c||&quot;,$&quot;;typeof c===&quot;string&quot;&amp;&amp;(c=c.split(&quot;&quot;));if(a instanceof Array){if(d||(d=0),d<c.length){for(var g=[],f=0;f<a.length;f++)g.push(Qb(a[f],b,c,d+1,e));return g.join(c[d])}}else if(typeof a===&quot;object&quot;)return e||(e=0),e<2?encodeURIComponent(Pb(a,b,c,d,e+1)):&quot;...&quot;;return encodeURIComponent(String(a))} function Rb(a,b){var c=&quot;https://pagead2.googlesyndication.com&quot;+b,d=Sb(a)-b.length;if(d<0)return&quot;&quot;;a.g.sort(function(p,m){return p-m});b=null;for(var e=&quot;&quot;,g=0;g<a.g.length;g++)for(var f=a.g[g],h=a.i[f],k=0;k<h.length;k++){if(!d){b=b==null?f:b;break}var l=Pb(h[k],a.j,&quot;,$&quot;);if(l){l=e+l;if(d>=l.length){d-=l.length;c+=l;e=a.j;break}b=b==null?f:b}}a=&quot;&quot;;b!=null&amp;&amp;(a=e+&quot;trn=&quot;+b);return c+a}function Sb(a){var b=1,c;for(c in a.i)c.length>b&amp;&amp;(b=c.length);return 3997-b-a.j.length-1};var Tb=RegExp(&quot;^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$&quot;);function Ub(a,b,c,d){for(var e=c.length;(b=a.indexOf(c,b))>=0&amp;&amp;b<d;){var g=a.charCodeAt(b-1);if(g==38||g==63)if(g=a.charCodeAt(b+e),!g||g==61||g==38||g==35)return b;b+=e+1}return-1}var Vb=/#|$/,Wb=/[?&amp;]($|#)/;function Xb(a,b,c){this.o=a;this.u=b;this.g=c===void 0?null:c;this.i=null;this.j=!1;this.B=this.l}function Yb(a,b,c,d){try{if(a.g&amp;&amp;a.g.g){var e=a.g.start(b.toString(),3);var g=c();a.g.end(e)}else g=c()}catch(k){c=a.u;try{Mb(e),c=a.B(b,new Bb(k,{message:Cb(k)}),void 0,d)}catch(l){a.l(217,l)}if(c){var f,h;(f=window.console)==null||(h=f.error)==null||h.call(f,k)}else throw k;}return g} function Zb(a,b,c,d,e){return function(){var g=oa.apply(0,arguments);return Yb(a,b,function(){return c.apply(d,g)},e)}} Xb.prototype.l=function(a,b,c,d,e){e=e||&quot;jserror&quot;;var g=void 0;try{var f=new Nb;f.g.push(1);f.i[1]=Ob(&quot;context&quot;,a);b.error&amp;&amp;b.meta&amp;&amp;b.id||(b=new Bb(b,{message:Cb(b)}));if(b.msg){var h=b.msg.substring(0,512);f.g.push(2);f.i[2]=Ob(&quot;msg&quot;,h)}var k=b.meta||{};if(this.i)try{this.i(k)}catch(ea){}if(d)try{d(k)}catch(ea){}d=[k];f.g.push(3);f.i[3]=d;var l;if(!(l=t)){h=x;d=[];k=null;do{var p=h;if(ib(p)){var m=p.location.href;k=p.document&amp;&amp;p.document.referrer||null}else m=k,k=null;d.push(new Fb(m||&quot;&quot;));try{h= p.parent}catch(ea){h=null}}while(h&amp;&amp;p!==h);m=0;for(var q=d.length-1;m<=q;++m)d[m].depth=q-m;p=x;if(p.location&amp;&amp;p.location.ancestorOrigins&amp;&amp;p.location.ancestorOrigins.length===d.length-1)for(q=1;q<d.length;++q){var y=d[q];y.url||(y.url=p.location.ancestorOrigins[q-1]||&quot;&quot;,y.g=!0)}l=d}var t=l;var v=new Fb(x.location.href,!1);l=null;var w=t.length-1;for(p=w;p>=0;--p){var u=t[p];!l&amp;&amp;Db.test(u.url)&amp;&amp;(l=u);if(u.url&amp;&amp;!u.g){v=u;break}}u=null;var z=t.length&amp;&amp;t[w].url;v.depth!==0&amp;&amp;z&amp;&amp;(u=t[w]);g=new Eb(v,u); if(g.i){var K=g.i.url||&quot;&quot;;f.g.push(4);f.i[4]=Ob(&quot;top&quot;,K)}var F={url:g.g.url||&quot;&quot;};if(g.g.url){var Ia=g.g.url.match(Tb),vb=Ia[1],wb=Ia[3],xb=Ia[4];t=&quot;&quot;;vb&amp;&amp;(t+=vb+&quot;:&quot;);wb&amp;&amp;(t+=&quot;//&quot;,t+=wb,xb&amp;&amp;(t+=&quot;:&quot;+xb));var yb=t}else yb=&quot;&quot;;F=[F,{url:yb}];f.g.push(5);f.i[5]=F;$b(this.o,e,f,this.j,c)}catch(ea){try{var zb,Ab;$b(this.o,e,{context:&quot;ecmserr&quot;,rctx:a,msg:Cb(ea),url:(Ab=(zb=g)==null?void 0:zb.g.url)!=null?Ab:&quot;&quot;},this.j,c)}catch(td){}}return this.u};function ac(a){var b=&quot;M&quot;;if(a.M&amp;&amp;a.hasOwnProperty(b))return a.M;b=new a;return a.M=b};function bc(){};function cc(){this.path=&quot;/pagead/gen_204?id=&quot;;this.g=Math.random()}function dc(){var a=ec,b=window.google_srt;b>=0&amp;&amp;b<=1&amp;&amp;(a.g=b)}function $b(a,b,c,d,e){if(((d===void 0?0:d)?a.g:Math.random())<(e||.01))try{if(c instanceof Nb)var g=c;else g=new Nb,lb(c,function(h,k){var l=g,p=l.l++;h=Ob(k,h);l.g.push(p);l.i[p]=h});var f=Rb(g,a.path+b+&quot;&amp;&quot;);f&amp;&amp;P(f)}catch(h){}};var ec,fc,T=new Lb;function hc(){window.google_measure_js_timing||(T.g=!1,T.i!==T.j.google_js_reporting_queue&amp;&amp;(S()&amp;&amp;ra(T.i,Mb),T.i.length=0))}(function(a){ec=a!=null?a:new cc;typeof window.google_srt!==&quot;number&quot;&amp;&amp;(window.google_srt=Math.random());dc();fc=new Xb(ec,!0,T);fc.i=function(){};fc.j=!0;window.document.readyState===&quot;complete&quot;?hc():T.g&amp;&amp;nb(window,&quot;load&quot;,function(){hc()})})();var ic=[&quot;FRAME&quot;,&quot;IMG&quot;,&quot;IFRAME&quot;],jc=/^[01](px)?$/;function kc(){this.j=this.g=this.l=this.i=!1}function lc(){var a=new kc;a.i=!1;a.l=!0;return a}function mc(a,b){a.g=b;return a}function nc(a,b){a.j=b;return a}function oc(a){return typeof a===&quot;string&quot;?document.getElementById(a):a} function pc(a,b){b=b===void 0?!1:b;if(a.tagName===&quot;IMG&quot;){if(a.complete&amp;&amp;(!a.naturalWidth||!a.naturalHeight))return!0;var c;if(b&amp;&amp;((c=a.style)==null?void 0:c.display)===&quot;none&quot;)return!0}var d,e;return jc.test((d=a.getAttribute(&quot;width&quot;))!=null?d:&quot;&quot;)&amp;&amp;jc.test((e=a.getAttribute(&quot;height&quot;))!=null?e:&quot;&quot;)} function qc(a,b){if(a.tagName===&quot;IMG&quot;)return a.naturalWidth&amp;&amp;a.naturalHeight?!0:!1;try{if(a.readyState)var c=a.readyState;else{var d,e;c=(d=a.contentWindow)==null?void 0:(e=d.document)==null?void 0:e.readyState}return c===&quot;complete&quot;}catch(g){return b===void 0?!1:b}}function rc(a){a||(a=function(b,c,d){b.addEventListener(c,d)});return a}function sc(a){if(x.document.readyState===&quot;complete&quot;)a(5);else{var b=rc(b);b(x,&quot;load&quot;,function(){a(4)})}} function tc(a,b,c){c=c===void 0?new kc:c;if(a=oc(a)){var d=rc(d);for(var e=!1,g=function(w){e||(e=!0,b(w))},f,h=2,k=0;k<ic.length;++k)if(ic[k]===a.tagName){h=3;f=[a];break}f||(f=a.querySelectorAll(ic.join(&quot;,&quot;)));var l=0,p=0,m=!c.g,q=a=!1;k={};for(var y=0;y<f.length;k={C:void 0},y++){var t=f[y];if(!pc(t,c.g))if(k.C=t.tagName===&quot;IMG&quot;,qc(t,c.i))a=!0,k.C&amp;&amp;(m=!0);else{l++;var v=function(w){return function(u){l--;!l&amp;&amp;m&amp;&amp;g(h);w.C&amp;&amp;(u=u&amp;&amp;u.type===&quot;error&quot;,p--,u||(m=!0),!p&amp;&amp;q&amp;&amp;m&amp;&amp;g(h))}}(k);d(t,&quot;load&quot;,v);k.C&amp;&amp; (p++,d(t,&quot;error&quot;,v))}}p===0&amp;&amp;(m=!0);f=null;f=x.document.readyState===&quot;complete&quot;;if(c.j&amp;&amp;f){if(p>0){q=!0;return}h=5}else if(l===0&amp;&amp;!a&amp;&amp;f)h=5;else if(l||!a){d(x,&quot;load&quot;,function(){!c.l||!p&amp;&amp;m?g(4):q=!0});return}g(h)}};function uc(a,b,c){if(a)for(var d=0;a!=null&amp;&amp;d<500&amp;&amp;!c(a);++d)a=b(a)}function vc(a,b){uc(a,function(c){try{return c===c.parent?null:c.parent}catch(d){}return null},b)}function wc(a,b){if(a.tagName==&quot;IFRAME&quot;)b(a);else{a=a.querySelectorAll(&quot;IFRAME&quot;);for(var c=0;c<a.length&amp;&amp;!b(a[c]);++c);}}function xc(a){return(a=a.ownerDocument)&amp;&amp;(a.parentWindow||a.defaultView)||null} function yc(a,b,c){try{var d=JSON.parse(c.data)}catch(f){}if(typeof d===&quot;object&quot;&amp;&amp;d&amp;&amp;d.type===&quot;creativeLoad&quot;){var e=xc(a);if(c.source&amp;&amp;e){var g;vc(c.source,function(f){try{if(f.parent===e)return g=f,!0}catch(h){}});g&amp;&amp;wc(a,function(f){if(f.contentWindow===g)return b(d),!0})}}}function zc(a){return typeof a===&quot;string&quot;?document.getElementById(a):a} function Ac(a,b){var c=zc(a);if(c)if(c.onCreativeLoad)c.onCreativeLoad(b);else{var d=b?[b]:[],e=function(g){for(var f=0;f<d.length;++f)try{d[f](1,g)}catch(h){}d={push:function(h){h(1,g)}}};c.onCreativeLoad=function(g){d.push(g)};c.setAttribute(&quot;data-creative-load-listener&quot;,&quot;&quot;);c.addEventListener(&quot;creativeLoad&quot;,function(g){e(g.detail)});x.addEventListener(&quot;message&quot;,function(g){yc(c,e,g)})}};function U(a){var b=this;this.i=!1;this.g=[];a(function(c){Bc(b,c)})}function Bc(a,b){if(!a.i)if(b instanceof U)b.then(function(c){Bc(a,c)});else{a.i=!0;a.j=b;for(b=0;b<a.g.length;++b)Cc(a,a.g[b]);a.g=[]}}function Cc(a,b){a.i?b(a.j):a.g.push(b)}U.prototype.then=function(a){var b=this;return new U(function(c){Cc(b,function(d){c(a(d))})})}; function Dc(a){var b=a.length,c=0;return new U(function(d){if(b==0)d([]);else for(var e=[],g={A:0};g.A<b;g={A:g.A},++g.A)a[g.A].then(function(f){return function(h){e[f.A]=h;++c==b&amp;&amp;d(e)}}(g))})}function Ec(){var a,b=new U(function(c){a=c});return new Fc(b,a)}function Fc(a,b){this.promise=a;this.resolve=b};function Gc(a){if(a.prerendering)return 3;var b;return(b={visible:1,hidden:2,prerender:3,preview:4,unloaded:5,&quot;&quot;:0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||&quot;&quot;])!=null?b:0}function Hc(a){var b;a.visibilityState?b=&quot;visibilitychange&quot;:a.mozVisibilityState?b=&quot;mozvisibilitychange&quot;:a.webkitVisibilityState&amp;&amp;(b=&quot;webkitvisibilitychange&quot;);return b};function Ic(a,b,c){function d(k){try{var l=typeof k.data===&quot;object&quot;?k.data:JSON.parse(k.data);f===l.paw_id&amp;&amp;(window.clearTimeout(h),window.removeEventListener(&quot;message&quot;,d),l.signal?b(l.signal):l.error&amp;&amp;c(l.error))}catch(p){k={msg:&quot;postmessageError&quot;,err:p instanceof Error?p.message:&quot;nonError&quot;,data:k.data==null?&quot;null&quot;:k.data.length>500?k.data.substring(0,500):k.data},ac(bc),l=[],!k.eid&amp;&amp;l.length&amp;&amp;(k.eid=l.toString()),$b(ec,&quot;paw_sigs&quot;,k,!0)}}var e={},g=200;e=e===void 0?{}:e;b=b===void 0?function(){}: b;c=c===void 0?function(){}:c;g=g===void 0?200:g;var f=String(Math.floor(kb()*2147483647)),h=0;window.addEventListener(&quot;message&quot;,function(k){Zb(fc,903,function(){d(k)})()});a.postMessage(Object.assign({},{paw_id:f},e));h=window.setTimeout(function(){window.removeEventListener(&quot;message&quot;,d);c(&quot;PAW GMA postmessage timed out.&quot;)},g)} function Jc(){var a=window,b,c;if(a.gmaSdk||((b=a.webkit)==null?0:(c=b.messageHandlers)==null?0:c.getGmaViewSignals))return a;try{var d=window.parent,e,g;if(d.gmaSdk||((e=d.webkit)==null?0:(g=e.messageHandlers)==null?0:g.getGmaViewSignals))return d}catch(f){}return null};function Kc(a){this.m=G(a)}r(Kc,I);function Lc(a){this.m=G(a)}r(Lc,I);function Mc(a,b){return H(a,2,b)}function Nc(a,b){return H(a,3,b)}function Oc(a,b){return H(a,4,b)}function Pc(a,b){return H(a,5,b)}function Qc(a,b){return H(a,9,b)} function Rc(a,b){var c=b;Wa(a);b=a.m;var d=b[B]|0;if(c==null)Ya(b,d,10);else{for(var e=c===Ca?7:c[B]|0,g=e,f=!!(2&amp;e)&amp;&amp;!!(4&amp;e)||!!(256&amp;e),h=f||Object.isFrozen(c),k=!0,l=!0,p=0;p<c.length;p++){var m=c[p];f||(m=Ga(m),k&amp;&amp;(k=!m),l&amp;&amp;(l=m))}f||(e=k?13:5,e=l?e&amp;-4097:e|4096);h&amp;&amp;e===g||(c=Array.prototype.slice.call(c),g=0,e=(2&amp;d?e|2:e&amp;-3)&amp;-273);e!==g&amp;&amp;C(c,e);d=Ya(b,d,10,c);2&amp;e||!(4096&amp;e||16&amp;e)||(c=d,c===void 0&amp;&amp;(c=b[B]|0),c&amp;32&amp;&amp;!(c&amp;4096)&amp;&amp;C(b,c|4096))}return a} function Sc(a,b){return Xa(a,11,b==null?b:Oa(b))}function Tc(a,b){return H(a,1,b)}function Uc(a,b){return Xa(a,7,b==null?b:Oa(b))};var Vc=&quot;platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64&quot;.split(&quot; &quot;);function Wc(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Xc(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)===&quot;function&quot;} function Yc(a){if(!Xc(a))return null;var b=Wc(a);if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Vc).then(function(c){b.uach!=null||(b.uach=c);return c});return b.uach_promise=a} function Zc(a){var b;return Sc(Rc(Pc(Mc(Tc(Oc(Uc(Qc(Nc(new Lc,a.architecture||&quot;&quot;),a.bitness||&quot;&quot;),a.mobile||!1),a.model||&quot;&quot;),a.platform||&quot;&quot;),a.platformVersion||&quot;&quot;),a.uaFullVersion||&quot;&quot;),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new Kc;d=H(d,1,c.brand);return H(d,2,c.version)}))||[]),a.wow64||!1)}function $c(a){var b,c;return(c=(b=Yc(a))==null?void 0:b.then(function(d){return Zc(d)}))!=null?c:null};function ad(a){return[&quot;omid_v1_present&quot;,&quot;omid_v1_present_web&quot;,&quot;omid_v1_present_app&quot;].some(function(b){try{var c=a.frames&amp;&amp;!!a.frames[b]}catch(d){c=!1}return c})};function bd(){this.g=x.document;this.u=x;this.i=null;this.j=this.l=&quot;&quot;;this.v=cd();dd(this)} function dd(a){var b=[],c=Q(bb)||!!M.aub;if(c||M.aunb){var d=$c(a.u);d&amp;&amp;(d=d.then(function(m){var q=JSON.stringify(E(m));m=[];for(var y=0,t=0;t<q.length;t++){var v=q.charCodeAt(t);v>255&amp;&amp;(m[y++]=v&amp;255,v>>=8);m[y++]=v}q=3;q===void 0&amp;&amp;(q=0);if(!A)for(A={},y=&quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;.split(&quot;&quot;),t=[&quot;+/=&quot;,&quot;+/&quot;,&quot;-_=&quot;,&quot;-_.&quot;,&quot;-_&quot;],v=0;v<5;v++){var w=y.concat(t[v].split(&quot;&quot;));ta[v]=w;for(var u=0;u<w.length;u++){var z=w[u];A[z]===void 0&amp;&amp;(A[z]=u)}}q=ta[q];y=Array(Math.floor(m.length/ 3));t=q[64]||&quot;&quot;;for(v=w=0;w<m.length-2;w+=3){var K=m[w],F=m[w+1];z=m[w+2];u=q[K>>2];K=q[(K&amp;3)<<4|F>>4];F=q[(F&amp;15)<<2|z>>6];z=q[z&amp;63];y[v++]=u+K+F+z}u=0;z=t;switch(m.length-w){case 2:u=m[w+1],z=q[(u&amp;15)<<2]||t;case 1:m=m[w],y[v]=q[m>>2]+q[(m&amp;3)<<4|u>>4]+z+t}a.l=y.join(&quot;&quot;)}),c&amp;&amp;b.push(d))}if(Q(ab)||Q(db)){c=Jc();var e;if(c==null?0:(e=c.gmaSdk)==null?0:e.getViewSignals)(e=c.gmaSdk.getViewSignals())&amp;&amp;!Q(db)&amp;&amp;(a.j=&quot;&amp;ms=&quot;+e);else{var g,f;if(c==null?0:(g=c.webkit)==null?0:(f=g.messageHandlers)==null?0:f.getGmaViewSignals){var h, k;Ic(c==null?void 0:(h=c.webkit)==null?void 0:(k=h.messageHandlers)==null?void 0:k.getGmaViewSignals,function(m){Q(db)||(a.j=&quot;&amp;&quot;+m)},function(){})}}}M.umi&amp;&amp;(e=new U(function(m){a.i=m}),b.push(e));if(M.ebrpfa||Q($a)||M.imprtype==&quot;1&quot;||M.imprtype==&quot;2&quot;){var l=Ec();(M.imprtype==&quot;1&quot;?1:M.imprtype==&quot;2&quot;?2:0)!==2&amp;&amp;b.push(l.promise);pb(a.g,function(){tc(a.g.body,l.resolve)})}Gc(a.g)==3&amp;&amp;Gc(a.g)==3&amp;&amp;b.push(ed(a));if(M.opxdv&amp;&amp;a.v){var p=Ec();b.push(p.promise);e=x.omrhp;typeof e===&quot;function&quot;?e(p.resolve):(e=x.document.querySelector(&quot;script[data-jc='86']&quot;))&amp;&amp; e.addEventListener(&quot;load&quot;,function(){x.omrhp(p.resolve)})}a.o=Dc(b)} function fd(a){var b=a;var c=b.search(Vb),d=Ub(b,0,&quot;ase&quot;,c);if(d<0)b=null;else{var e=b.indexOf(&quot;&amp;&quot;,d);if(e<0||e>c)e=c;b=decodeURIComponent(b.slice(d+4,e!==-1?e:0).replace(/\+/g,&quot; &quot;))}if(b===(2).toString()||eb.test(a)){var g=x.document;g=g===void 0?document:g;var f;g=!((f=g.featurePolicy)==null||!f.allowedFeatures().includes(&quot;attribution-reporting&quot;));f=a.search(Vb);b=0;for(d=[];(c=Ub(a,b,&quot;nis&quot;,f))>=0;)d.push(a.substring(b,c)),b=Math.min(a.indexOf(&quot;&amp;&quot;,c)+1||f,f);d.push(a.slice(b));f=d.join(&quot;&quot;).replace(Wb, &quot;$1&quot;);a=g?6:5;(a=&quot;nis&quot;+(a!=null?&quot;=&quot;+encodeURIComponent(String(a)):&quot;&quot;))?(b=f.indexOf(&quot;#&quot;),b<0&amp;&amp;(b=f.length),c=f.indexOf(&quot;?&quot;),c<0||c>b?(c=b,d=&quot;&quot;):d=f.substring(c+1,b),f=[f.slice(0,c),d,f.slice(b)],b=f[1],f[1]=a?b?b+&quot;&amp;&quot;+a:a:b,a=f[0]+(f[1]?&quot;?&quot;+f[1]:&quot;&quot;)+f[2]):a=f;g=Q(cb)?g:!0;f=!1;f=f===void 0?!1:f;g=g===void 0?!1:g;x.fetch?(f={keepalive:!0,credentials:&quot;include&quot;,redirect:&quot;follow&quot;,method:&quot;get&quot;,mode:&quot;no-cors&quot;},g&amp;&amp;(f.mode=&quot;cors&quot;,&quot;setAttributionReporting&quot;in XMLHttpRequest.prototype?f.attributionReporting= {eventSourceEligible:&quot;true&quot;,triggerEligible:&quot;false&quot;}:f.headers={&quot;Attribution-Reporting-Eligible&quot;:&quot;event-source&quot;}),x.fetch(a,f)):P(a,void 0,f,g)}else if(M.atsb){g=g===void 0?!1:g;if(f=x.navigator)f=x.navigator.userAgent,f=/Chrome/.test(f)&amp;&amp;!/Edge/.test(f)?!0:!1;f&amp;&amp;typeof x.navigator.sendBeacon===&quot;function&quot;?x.navigator.sendBeacon(a):P(a,void 0,g)}else P(a)} function cd(){var a=L(x).omid3p,b=!!a&amp;&amp;typeof a.registerSessionObserver===&quot;function&quot;&amp;&amp;typeof a.addEventListener===&quot;function&quot;;b||jb(function(c){ad(c)&amp;&amp;(b=!0);return b},!0);return b}function ed(a){return new U(function(b){var c=Hc(a.g);if(c){var d=function(){Gc(a.g)!=3&amp;&amp;(ob(a.g,c,d),b())};nb(a.g,c,d)}})};function V(){return(new Date).getTime()}function gd(){try{var a=a===void 0?document:a;var b;return!((b=a.featurePolicy)==null||!b.features().includes(&quot;attribution-reporting&quot;))}catch(c){return!1}}var W=new Xb(new cc,!1);function X(a){a.umi=M.umi?1:0;a.eavp=M.eavp?1:0;a.ebrp=M.ebrp?1:0;a.bvst=M.bvst||&quot;n&quot;;a.opxb=M.opxb?1:0;a.aunb=M.aunb?1:0;a.aub=M.aub?1:0;a.fld=M.fld?1:0;a.uffp=M.uffp?1:0;a.espa=M.espa?1:0;a.aifbl=M.aifbl?1:0;a.lrsc=M.lrsc?1:0;Y&amp;&amp;(a.omid=Y.v?1:0)} function hd(a,b){this.L=a;this.u=this.o=this.i=!1;this.R=1;this.j=M.eavp?1:0;this.g=M.ebrp?1:0;this.U=!!M.opxb;this.H=this.B=null;this.K=Ec();this.P=V();this.O=null;this.Z=Math.random()<.001;this.V=M.eeid||&quot;&quot;;this.G=this.l=null;this.aa=(Math.random()+&quot;&quot;).slice(-5);this.v=!1;this.D=null;this.F=0;this.I=this.J=null;this.S=!!M.fld;this.X=M.adsg||&quot;&quot;;this.W=M.admetadata||&quot;&quot;;this.da=!!M.uffp;this.ca=!!M.espa;var c,d;this.Y=((c=x.navigator)==null?void 0:(d=c.userAgent)==null?void 0:d.indexOf(&quot;Firefox&quot;))!= -1;this.T=!!M.lrsc;this.N=!!M.btrb;this.ba=!!M.umi;id(this,b)}var Y;function jd(a,b){a.g!==0&amp;&amp;(b?(a.G=V()-a.P,kd(a,b).then(function(c){c&amp;&amp;(a.B=c);a.u=!0;Z(a)})):!a.ba&amp;&amp;a.T&amp;&amp;kd(a).then(function(){a.u=!0;Z(a)}))}function ld(){try{jb(function(a){a=a.document;if(fb.length&amp;&amp;a.head)for(var b=ma(fb),c=b.next();!c.done;c=b.next())if((c=c.value)&amp;&amp;a.head){var d=N(&quot;META&quot;);a.head.appendChild(d);d.httpEquiv=&quot;origin-trial&quot;;d.content=c}return!1},!1)}catch(a){}} function id(a,b){jd(a,b);ld();a.g!==0||a.j!==0?md(a)?nd(a).then(function(){od(a)}):od(a):a.D=1;ac(bd).o.then(function(){a.i=!0;Z(a)});pd(a);Z(a)}function kd(a,b){return new U(function(c){function d(g,f){a.H=g;c(f)}if(b){Ac(b,d);var e=nc(mc(lc(),a.Y),a.T);tc(b,d,e)}else sc(d)})}function md(a){a.v=cd();a.D=a.v?4:1;return a.v} function nd(a){return new U(function(b){var c=Zb(W,1184,function(e){a.D=3;e&amp;&amp;(a.J=e.src,a.I=e.sdk);b()},null,X),d=x.omrhp;typeof d===&quot;function&quot;?d(c):(d=x.document.querySelector(&quot;script[data-jc='86']&quot;))&amp;&amp;d.addEventListener(&quot;load&quot;,Zb(W,1183,function(){x.omrhp(c)},null,X))})}function od(a){a.o=!0;Z(a)}function qd(a){return!a.U&amp;&amp;a.N?a.i&amp;&amp;a.u:a.i&amp;&amp;a.o&amp;&amp;a.u} function Z(a){if(a.i){var b=null,c=0;a.R!==2?b=0:a.j===1&amp;&amp;a.i&amp;&amp;a.o?b=12:a.g===1&amp;&amp;qd(a)&amp;&amp;(b=11);if(b!=null)a:{if(b===0?a.g===1&amp;&amp;qd(a)?c=2:a.j===1&amp;&amp;a.i&amp;&amp;a.o&amp;&amp;(c=1):b===12&amp;&amp;a.g===1&amp;&amp;qd(a)&amp;&amp;(c=2),a.U&amp;&amp;(a.Z&amp;&amp;a.L!==&quot;&quot;&amp;&amp;a.v&amp;&amp;(a.l||b!==0||rd(a,!0),b!==12&amp;&amp;c!==1&amp;&amp;c!==2||rd(a,!1)),b===0&amp;&amp;c===0))break a;if(!a.N||b!==0||c===2){var d=V(),e=sd(a,b,c,d);b===0&amp;&amp;(a.R=2,a.O=d);if(b===12||c)a.j=2;if(b===11||c===2)a.g=2;if(a.S){e=new Za;H(e,1,a.X);e=JSON.stringify(E(e));d=a.W;if(b===0){var g=L(x),f;g==null||(f=g.fence)== null||f.reportEvent({eventType:&quot;impression&quot;,eventData:e,destination:[&quot;buyer&quot;]});var h;g==null||(h=g.fence)==null||h.reportEvent({eventType:&quot;impression&quot;,eventData:d,destination:[&quot;component-seller&quot;]})}if(b===11||c===2){var k,l;(k=L(x))==null||(l=k.fence)==null||l.reportEvent({eventType:&quot;b2rimpression&quot;,eventData:e,destination:[&quot;buyer&quot;]})}}else e&amp;&amp;(((f=a.ca&amp;&amp;gd())||a.da)&amp;&amp;x.fetch?(h={method:&quot;GET&quot;,keepalive:!0,credentials:&quot;include&quot;},f?&quot;setAttributionReporting&quot;in XMLHttpRequest.prototype?(h.attributionReporting= {eventSourceEligible:!0,triggerEligible:!1},h.mode=&quot;no-cors&quot;):h.headers={&quot;Attribution-Reporting-Eligible&quot;:&quot;event-source&quot;}:h.mode=&quot;no-cors&quot;,x.fetch(gb(e,&quot;&amp;ftch=1&quot;),h)):fd(e));b===0&amp;&amp;a.K.resolve()}}}}function rd(a,b){var c=null;b?a.l=V():c=a.l?V()-a.l:&quot;?&quot;;a=&quot;https://pagead2.googlesyndication.com/pagead/gen_204?id=opxhb&amp;evt=&quot;+(b?&quot;d&quot;:&quot;o&quot;)+(&quot;&amp;eid=&quot;+encodeURIComponent(a.V))+(c==null?&quot;&quot;:&quot;&amp;ttp=&quot;+c);fd(a)} function sd(a,b,c,d){if(a.L===&quot;&quot;)return null;var e={omid:a.v?1:0,rm:a.D,ctpt:d-a.P};b!==0&amp;&amp;(e.vt=b,e.dtpt=d-(a.O||0));c!==0&amp;&amp;(e.cbvp=c);a.H&amp;&amp;(e.dett=a.H);a.B&amp;&amp;(e.beid=a.B.eventId,e.vend=a.B.vendor);a.G!=null&amp;&amp;(e.cstd=a.G);(b=M.bvst)&amp;&amp;(e.cisv=b+(&quot;.&quot;+a.aa));a.F!==0&amp;&amp;(e.vwbs=a.F);a.J&amp;&amp;(e.oprs=a.J);a.I&amp;&amp;(e.opsd=a.I);(b=ac(bd).l)&amp;&amp;(e.uach=b);e.arae=Number(gd());var g=&quot;&quot;;lb(e,function(f,h){g+=&quot;&amp;&quot;+h+&quot;=&quot;+encodeURIComponent(f)});return gb(a.L,g)} function pd(a){var b=a.V;if(b&amp;&amp;(b=x[&quot;bllsn&quot;+b],typeof b===&quot;function&quot;))try{b(function(c){c&amp;&amp;(a.F=c.block?2:1)})}catch(c){}};L().btrp=function(a,b){return Yb(W,1183,function(){var c=Y=new hd(a,b);return Zb(W,1183,function(d){jd(c,d)},X)},X)}; L().pdib3=function(a,b){Yb(W,1185,function(){var c=Y;c.K.promise.then(function(){var d=a,e=L(x),g=gd();if(eb.test(d)){var f=d.lastIndexOf(&quot;;&quot;);d=f===-1?d:d.substring(0,f)+&quot;;tpsrc=cis&quot;+d.substring(f)}var h;c.S&amp;&amp;((h=e.fence)==null?0:h.reportEvent)?e.fence.reportEvent({destinationURL:d}):b?(e=!1,e=e===void 0?!1:e,g=g===void 0?!1:g,rb()?qb(window,d,null,!0,e,g):(f=x.document,f.body?(h=f.getElementById(&quot;goog-srcless-iframe&quot;),h||(h=N(&quot;IFRAME&quot;),h.style.display=&quot;none&quot;,h.id=&quot;goog-srcless-iframe&quot;,f.body.appendChild(h)), f=h):f=null,f&amp;&amp;f.contentWindow&amp;&amp;qb(f.contentWindow,d,null,!0,e,g))):P(d,null,!1,g)})},X)};L().vv=function(){var a=ac(bd);if(!a.i)throw Error(&quot;aiv::err&quot;);a.i()};L().sasrc=function(a){Y.K.promise.then(function(){var b=x.document.createElement(&quot;img&quot;);b.style.display=&quot;none&quot;;b.attributionSrc=a;x.document.body.appendChild(b)})};}).call(this);window.stcc = btrp(&quot;https://ad.doubleclick.net/pcs/view?xai\x3dAKAOjsuNiOfK5Sfk9HzVrqfsO49Jh4YEIHlxDtwWf2_lxCM_R7SPl_hKAqdL5pUG4Hh5Pp0LAV0pUTnlUJ14dmOTSBkNrhknAktiFko1Ogzyx334incXJP8d8jfmkXwW2LWQG8B7yaiiOSbrIL4kjK2eyJb5kDKcszlh6SXDX-_muPAKSVdRdj1gOWllOv8nzhyeZrT4KohcL0LuU3LPKeUONf7UzYxKhd8ovD6n0OiXKwNS29rBHbp03uaA2dNipwfXja4JXHtAiw6xknlKQshBKJKvGExHFlSs48qS4dKjMfc1X1nNfzTpVXaoKb3SDW2tuN1mZLRPH_cbs0b0mf_yB3dJbi4E4q3Da4woWzNJMQofhtH54koHJDBcuQ954qz6RUS5C-tMI_PC2r0NenngBDNO_Or_gJ4Y3qmAWDuIS35jiLFEYrXmGqjTOrQlTaCei6Kt9Tv6IQlTvoyRRD5WFohyQ12QuikLfZe5uC0Z_O_fQKRvp3d71OQ0VpmoYLWigzYZpgp8v3SY7PP2EJo22KVyd8KpjW9btyW_w1xMJEISINstKvoCi5dygvMwBO1Ob3j-MSJFLoNa0PrsWUiPChnmF6fad1AoBidhfi_E7M7MEIWDN3R-sqt1T_MumRbs10ZmqcntW_uRwS2zWby3hgM_NtAq9ekLxdhdhwCk9PIkY7CpQZj5ZawCoizENHgzx_xfe5Xb-Oi21_pM6A-HXCfqGokjYam30oqDgiyF4ldOgo1IWX6r6C55_tM4HWtk-Cak_Fyll0Hs3j8gMYgeuvsHipJ0bu4kbwEVZa9uJjqT81aJc3nmaVjhXCO3sIVhB-FSnudQyGAyatZNenLaY5ouw_9YHnteBgBVyHede3zyxocVgKsBmxmNno579S8mjczaKhbHchz7e4v8pCvmg1Pndru9LeyULP7yF1z0wyghydWTG3R1-zkuBKwFxdiPOCsuD_dw3vjSZ5kK8cuT0HlPAd4YniQ3mZycWPKiNTzIWQcRbmIyeL4f_8lF_Onv5k2AKx004PeK0nj_SAmT4VAWPL-FH0sSvSdVS_LyibfFo8k3WO_fFdZsoqJCeKEoq4MbP-tc8KBAeiTPy88_wK5oCphwcD-tmtk8cRZAtCo3N5D80vrIU_VJSi1Zo_8XH5xOd7vgW1nc53-_zu8bX4WdkN1bQAlV9BBYKqAzkwToHKg_vPlLy-Xw2-phAI0CFET6sJOIoTbnawsnWmYNbY9WxQdEHoN-WfN6VqSs7vuA26MjnDTdCa58rQkFBUOoi14JsNKryPDU3iJh19DGaubm5WztBs_WoRhv7lrGHQN0XbOuTe3NrRowEkGAG5YEjDbLakVHNk_yjZz38MpQJJJKxbfU-Jo-rVdphpXgruGWhmjAcJOSHAcnLOqB3raqMd2nmZG1l-L4GzhqkgGfXm8FqRzKilD-tOyvf-nEle3QYYRHy5rmn-Iysqw4RGIjj1U4McVL1D8jSvGYy6TACEOwmWSLIbMsYEu2RnLUTeitc0XBA9R_JiW6zKMT3BtxgNALA2xNbhk7IOAiNBpA1NKGDIXgB3e0XItlrRj1jhRI8In9e7bfwoUntYNXR0VL9o7Rhw_C_yQ2153veUksDQXlkRPGe4N3lDBizg38tP5HdX7gOmsBRvzgA3cUekSBwZyBMW6YF2yHx-XyJmgCgnriYBzAOjbV9DR2NGMaESbrhRh5mJTQy_wB8ErOdG96XBUeLMW3gDLi9SZftdSCfoM_B2rjHjAdV2b51V6DvHw4Y6whLcrsPlfU-g\x26sai\x3dAMfl-YTlddNeJ9Fod7ZJ5wH3rwVjoNxaxGzo52ZWZsxkAHeQmVBzelBLlmO34Kmpa2QDR9oDM6PMTvh3ca-jpCVDH4QLdvRqtKVGD9K1wPFH0vogcS6KNEDydOVZB1HBFmmddY7NcyP8Vcv1rSCVWSziZ9zXNwc5lz1lE84DWQX4ZmHUyBSos2MwUg-LzzatzUn7dlszr6nSBmyNeDxKl4S-IDoPy0vXXafG1YinMiYOfOfnBzUDiWOK18_eNoRUI4kzgQkOWWl5TkQWd8j6EcnDKJ6lcrEg7cX4UtAXZfl5557Mpwm3o494yEdO6Furlh0CNdSPRib29SJIZ--Qb1RU12HZFzOlswQAaBYLao0vXlH7Jf4B6TKAA7pKwfrweWGkvWwDwn3tKppeEhxdP9B4DxYYNFa1-TZuWKtH2U6Jrk3rnlsQpOlj\x26sig\x3dCg0ArKJSzEydFo5eZaHjEAE\x26uach_m\x3d%5BUACH%5D\x26crd\x3daHR0cHM6Ly93c2ouY29t\x26pr\x3dmissingexchangepricemacro\x26fbs_aeid\x3d%5Bgw_fbsaeid%5D\x26urlfix\x3d1\x26adurl\x3d&quot;, document.getElementById(&quot;gcc_Dv4-aMjJKrjcmtUP_9jKgQ8&quot;));})();(function() {if (!window.GoogleTyFxhY || typeof window.GoogleTyFxhY.push !== 'function') {window.GoogleTyFxhY = [];}window.GoogleTyFxhY.push({'_scs_': 'B0lUfDv4-aMjJKrjcmtUP_9jKgQ8AAAAAOAHgBAI','_bgu_': 'https://pagead2.googlesyndication.com/bg/99lcxn2YvEFuFPB90BzYKtdfgSsvSUIzwZpxI2siobo.js','_bgp_': '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','_ifr_': 'false','_isfl_': 'false'});var gsodar = document.createElement('script');gsodar.type = 'text/javascript';gsodar.async = true;gsodar.src = '//tpc.googlesyndication.com/sodar/Q12zgMmT.js';var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(gsodar, s);})();</script></div></div></div><script data-jc=&quot;22&quot; src=&quot;https://tpc.googlesyndication.com/pagead/js/r20250602/r20110914/client/window_focus_fy2021.js&quot; async data-jc-version=&quot;r20250602&quot; data-jcp-url=&quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai=CovuiDv4-aMjJKrjcmtUP_9jKgQ-Qwd-3f5Snp96HFImi1OOnQhABIMT8ip0BYMm2iYfMo8AXoAH2q8jLKsgBCagDAcgDmwSqBJACT9DK-stYv-zH7htr_oylwaGfbgLDx9zdnI9UnLlSwTlOR5f7g5Zhy2eQ6_gl-FK4maJTKHxo3z4o4qmNK-bvjuoLoyJYGzucvSrk2ELry6Xq4m41RaQWxmW4vmmowNCx4XVefZEE30YldFSFBHqZzYV0RESZIfSq7OPej6dlwqL83EXSMQBDIwS2vbsedG-RRzeiRzZ3Wp4dIJlYD-IyE-PE3S6wiO14GbUCJTbsfD0ppEpU3sCgbtVi6nhgGGGb7tC787K3zVP9-knHo2g_lrOVZW-NEHYCLg_FHqMLUp68kS1Dkmzzc8RnBztt9EwEbyq1G7cRx189j8JwDxWNgUfk09BJG2BPFI42P0j0ggDABKuv4cWPBeAEA4gF2sL9-lKQBgGgBkyAB_bjmKsFqAfVyRuoB9m2sQKoB6a-G6gHzM6xAqgH89EbqAeW2BuoB6qbsQKoB-C9sQKoB47OG6gHk9gbqAfw4BuoB-6WsQKoB_6esQKoB6--sQKoB5oGqAf_nrECqAffn7ECqAf4wrECqAf7wrEC2AcA0ggpCIBhEAEYnQEyAooCOg2AQIDAgICAgKiAAqADSL39wTpY4LTsgLPVjQOACgOYCwHICwGADAGqDQJVU-INEwjYhO2As9WNAxU4rqYEHX-sMvDqDRMIu__tgLPVjQMVOK6mBB1_rDLwsBP19_Ic2BMNiBQC2BQB0BUByhYCCgD4FgGAFwGyF0AYAio8LzIyOTYwMjEyMDkwL1NtYXJ0YmFja2dyb3VuZGNoZWNrc19TMlNfTWlkY29udGVudEJhbm5lcjJfUk9TshgJEgLyThhMIgEAshkBNQ&amp;amp;sigh=HQNHzN-79nY&amp;amp;cid=CAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bQ&quot; data-jcp-gws-id=&quot;&quot; data-jcp-qem-id=&quot;CMiI74Cz1Y0DFTiupgQdf6wy8A&quot;></script><iframe title=&quot;Blank&quot; scrolling=&quot;no&quot; frameborder=0 height=0 width=0 src=&quot;https://pagead2.googlesyndication.com/pagead/s/cookie_push_onload.html#aHR0cHM6Ly91bS5zaW1wbGkuZmkvZ3BfbWF0Y2g_Z29vZ2xlX3B1c2g9QVhjb09tUWgwVU4yLWlYSXNKVjNncm1vbUpsUmo2NW5RZ1hHbjhMcXQ1aXczMDBRNk1nWUQ4NnpIYnBpczVMZWgwRVh3ZVE2UXFiX1lIUXJ6QnZrdWJ3VFlHOGlaMEduQ1VZbVpGbGg5U2RyekZ3OGRGNEJuc2p6bFFvRHBDbWZfZVdndEFCb3FRZW9mbGtyM0paT1hMei1wZ0U=,aHR0cHM6Ly9tYXRjaC5hZHNydnIub3JnL3RyYWNrL2NtZi9nb29nbGU_Z29vZ2xlX3B1c2g9QVhjb09tUkxkVmNmRjllTXBRZlQ2V1p3dHoweE9OZXZOT2xLZThPbWlqa0gyMHV2T29TbS1OeFhTcmlvaUlfNFhMdktlVndRc3FjX1ZhRXUyVnF4Y044RnNtVEZJSlVORDVoSm14YWNpemNFT0p5bE8xbDZXbkFJZG5GeldkUnlWT3FoNW5iZjJPS3V3OFdLazhOSm1LTWxNSE0=,aHR0cHM6Ly9zeW5jLmlwcmVkaWN0aXZlLmNvbS9kL3N5bmMvY29va2llL2dlbmVyaWM_aHR0cHM6Ly9jbS5nLmRvdWJsZWNsaWNrLm5ldC9waXhlbD9nb29nbGVfbmlkPWFkZWxwaGljX21vYmlsZSZnb29nbGVfcHVzaD1BWGNvT21TX2FEbERPWEs2X3NKLWlIRmprWXpDTXhBYjF6d0NfNTMtbFItaUVkUzFJVXNDcHVJRGZSRkhUVDhhaTBZcGdPWmJneUpjem92Y1BLZm5mcHZRNWNpNXk5eVJNY1VEYlFRUTBBblFROGZzWU9sbTdDeDBCU1Y2YW5WbnUxaVd1VFBEMkc2OWt5aHZ5T0w3S2V2bUt3Jmdvb2dsZV9obT0ke0FERUxQSElDX0NVSURfQjY0fQ==,aHR0cHM6Ly94LmJpZHN3aXRjaC5uZXQvc3luYz9zc3A9Z29vZ2xlJmdvb2dsZV9wdXNoPUFYY29PbVRvVzQxWUE0QmNLNmhTSEZteTVIczRvVnNPbTNvaVpXd0tEc1VGcUZheTUzSndmU1RmMUdwX1J3WE1NWF9JOG1NR3hvNlFjUlI0eV9EVUpyZk9jRmFSb0JqSENKZC1WM1hVUkZQNXBjNENheGV4VDhkUmhtQW5HNzFyVVpSbEhSZDk5VFJtUDVSZk53Yk9QcWZ2Y1JF,aHR0cHM6Ly9zLmFkLnNtYWF0by5uZXQvYy8_YWRFeEluaXQ9ZyZnb29nbGVfcHVzaD1BWGNvT21UMUVSNEtJbzMya1JqZUVvSUJaOHZ4XzZqc2RfcXdjb3UyMmQtc0hTYkdYVjNobk5Ua05aOUZua19HdHA0ZzdoNWFOcEMxU0ZNcWl5ekJ1eU9EeXV5SldqYmo3eHF3bldXYzZmM2U3SDRWOWJ3NDJYQS1CWFlQRVQzODktdkdnLXRoUnViOVFub2cwYm5kWldqLVFadw==,aHR0cHM6Ly9tYXRjaC4zNjB5aWVsZC5jb20vbWF0Y2gvZWJkYT9nb29nbGVfcHVzaD1BWGNvT21STjJENzg1dDJ1dWhNTmJDd3ItaDNxUE1wVjBva1o2WTRnTGxQRUtGSzJRVXNqV0M5VGpObE1UUU5LRVpoV01HNm9RdHg4SjBGYXZPTXhaa09lMWM1NDdlMVcwV0RZQ004WE8zUWVkTXVrVXV3aFRRQzl2ekRTNjVOc0lsaWpJelNJT3cta1JXenhkS2VOOVdNZWx3,aHR0cHM6Ly9jcmVhdGl2ZWNkbi5jb20vY20tbm90aWZ5P3BpPWFkeGFiJmdvb2dsZV9uaWQ9cnRiX2hvdXNlX3VzJmdvb2dsZV9wdXNoPUFYY29PbVJBNjJXTnNjZExHTnAxcUhGeVBjbV9GWldWWkhzcWZVZjhIMzZ6TnpNN1N4bzF1QXB0OHJDY3dJNXRiZXRQRzJaTDBVbFotMVBRUEFpUmVhTzM1V1VpcGpxSUdIaDk5Tmg3Q2dtR1B3QXpOUzF5WS1uVDV4bTE5bXFzZkt3cG1IVjdaZkdfYWtLSXVra3V4RDZaSExSSQ==,aHR0cHM6Ly9jbS5nLmRvdWJsZWNsaWNrLm5ldC9waXhlbC9hdHRyP2Q9QUhORjEzTHBvaE9lckcwWHVvMDdyNUVYXzBNLXpOZlVKdGRTLU1uLTF4enJWMWNXaks2QXZMMzIwclVfbWZSV0FFUWctNDlRMGpXWHVB&quot;  style=&quot;position:absolute&quot; aria-hidden=&quot;true&quot;></iframe><script data-jc=&quot;23&quot; src=&quot;https://tpc.googlesyndication.com/pagead/js/r20250602/r20110914/client/qs_click_protection_fy2021.js&quot; data-jc-version=&quot;r20250602&quot; data-jcp-init-data=&quot;[[[[null,500,99,2,9,null,null,null,1]]]]&quot;></script><img src=&quot;//www.google.com/ads/measurement/l?ebcid=ALh7CaRPyqJ8EooMjSeHngE614BChGOHsL8YaN80hG6ikKuiJQSaJRIieZ8-Cm0inOb7fdzhS_QrKmHfNYnFYYkFpj_Dbi21OA&quot; style=&quot;display:none;&quot; alt=&quot;&quot;></img><div style=&quot;bottom:0;right:0;width:300px;height:250px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAACCSURBVBjTbZEBDsAgCAPrD/r/104oBUzmEg3xhIMB/+to4X5nIoB5e3dyRUEli2AcxQ0vF3s+qajprmAaa+/cY+I3adKLJB9v2ETmmbuKg5ZPqZYwaxNawbzhApVT0RKB+8WeGXw8/amA+DUzFXGlmaCluOn5Q3hnp+5KfyaI7hD4AEphBb6+2GXNAAAAAElFTkSuQmCC') !important;&quot;></div><script data-jc=&quot;103&quot; data-jc-version=&quot;r20250602&quot; data-jcp-base_url=&quot;https://googleads.g.doubleclick.net/pagead/conversion/?ai=CovuiDv4-aMjJKrjcmtUP_9jKgQ-Qwd-3f5Snp96HFImi1OOnQhABIMT8ip0BYMm2iYfMo8AXoAH2q8jLKsgBCagDAcgDmwSqBJACT9DK-stYv-zH7htr_oylwaGfbgLDx9zdnI9UnLlSwTlOR5f7g5Zhy2eQ6_gl-FK4maJTKHxo3z4o4qmNK-bvjuoLoyJYGzucvSrk2ELry6Xq4m41RaQWxmW4vmmowNCx4XVefZEE30YldFSFBHqZzYV0RESZIfSq7OPej6dlwqL83EXSMQBDIwS2vbsedG-RRzeiRzZ3Wp4dIJlYD-IyE-PE3S6wiO14GbUCJTbsfD0ppEpU3sCgbtVi6nhgGGGb7tC787K3zVP9-knHo2g_lrOVZW-NEHYCLg_FHqMLUp68kS1Dkmzzc8RnBztt9EwEbyq1G7cRx189j8JwDxWNgUfk09BJG2BPFI42P0j0ggDABKuv4cWPBeAEA4gF2sL9-lKQBgGgBkyAB_bjmKsFqAfVyRuoB9m2sQKoB6a-G6gHzM6xAqgH89EbqAeW2BuoB6qbsQKoB-C9sQKoB47OG6gHk9gbqAfw4BuoB-6WsQKoB_6esQKoB6--sQKoB5oGqAf_nrECqAffn7ECqAf4wrECqAf7wrEC2AcA0ggpCIBhEAEYnQEyAooCOg2AQIDAgICAgKiAAqADSL39wTpY4LTsgLPVjQOACgOYCwHICwGADAGqDQJVU-INEwjYhO2As9WNAxU4rqYEHX-sMvDqDRMIu__tgLPVjQMVOK6mBB1_rDLwsBP19_Ic2BMNiBQC2BQB0BUByhYCCgD4FgGAFwGyF0AYAio8LzIyOTYwMjEyMDkwL1NtYXJ0YmFja2dyb3VuZGNoZWNrc19TMlNfTWlkY29udGVudEJhbm5lcjJfUk9TshgJEgLyThhMIgEAshkBNQ&amp;amp;sigh=HQNHzN-79nY&quot; data-jcp-cpu_label=&quot;heavy_ad_intervention_cpu&quot; data-jcp-net_label=&quot;heavy_ad_intervention_network&quot;>(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function n(a){h.setTimeout(()=>{throw a;},0)};let p=void 0;function u(a,b=!1){return b&amp;&amp;Symbol.for&amp;&amp;a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var v=u(),w=u(&quot;m_m&quot;,!0);const x=u(&quot;jas&quot;,!0);var y={};function z(a,b){return b===void 0?a.h!==A&amp;&amp;!!(2&amp;(a.g[x]|0)):!!(2&amp;b)&amp;&amp;a.h!==A}const A={};const B=BigInt(Number.MIN_SAFE_INTEGER),C=BigInt(Number.MAX_SAFE_INTEGER);function D(a){return a};function E(a,b,e,c){var d=c!==void 0;c=!!c;const l=[];var f=a.length;let g,k=**********,H=!1;const r=!!(b&amp;64),q=r?b&amp;128?0:-1:void 0;b&amp;1||(g=f&amp;&amp;a[f-1],g!=null&amp;&amp;typeof g===&quot;object&quot;&amp;&amp;g.constructor===Object?(f--,k=f):g=void 0,!r||b&amp;128||d||(H=!0,k=(F??D)(k-q,q,a,g)+q));b=void 0;for(d=0;d<f;d++){let m=a[d];if(m!=null&amp;&amp;(m=e(m,c))!=null)if(r&amp;&amp;d>=k){const t=d-q;(b??(b={}))[t]=m}else l[d]=m}if(g)for(let m in g){a=g[m];if(a==null||(a=e(a,c))==null)continue;f=+m;let t;r&amp;&amp;!Number.isNaN(f)&amp;&amp;(t=f+q)<k?l[t]=a:(b?? (b={}))[m]=a}b&amp;&amp;(H?l.push(b):l[k]=b);return l}function G(a){switch(typeof a){case &quot;number&quot;:return Number.isFinite(a)?a:&quot;&quot;+a;case &quot;bigint&quot;:return a>=B&amp;&amp;a<=C?Number(a):&quot;&quot;+a;case &quot;boolean&quot;:return a?1:0;case &quot;object&quot;:if(Array.isArray(a)){const b=a[x]|0;return a.length===0&amp;&amp;b&amp;1?void 0:E(a,b,G)}if(a!=null&amp;&amp;a[w]===y)return I(a);return}return a}let F;function I(a){a=a.g;return E(a,a[x]|0,G)};function J(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error(&quot;narr&quot;);b=a[x]|0;2048&amp;b&amp;&amp;!(2&amp;b)&amp;&amp;K();if(b&amp;256)throw Error(&quot;farr&quot;);if(b&amp;64)return b&amp;2048||(a[x]=b|2048),a;var e=a;b|=64;var c=e.length;if(c){var d=c-1;c=e[d];if(c!=null&amp;&amp;typeof c===&quot;object&quot;&amp;&amp;c.constructor===Object){const l=b&amp;128?0:-1;d-=l;if(d>=1024)throw Error(&quot;pvtlmt&quot;);for(const f in c){const g=+f;if(g<d)e[g+l]=c[f],delete c[f];else break}b=b&amp;-8380417|(d&amp;1023)<<13}}}a[x]=b|2112;return a} function K(){if(v!=null){var a=p??(p={});var b=a[v]||0;b>=5||(a[v]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity=&quot;incident&quot;,n(a))}};function L(a,b){if(typeof a!==&quot;object&quot;)return a;if(Array.isArray(a)){var e=a[x]|0;a.length===0&amp;&amp;e&amp;1?a=void 0:e&amp;2||(!b||4096&amp;e||16&amp;e?a=M(a,e,!1,b&amp;&amp;!(e&amp;16)):(a[x]|=34,e&amp;4&amp;&amp;Object.freeze(a)));return a}if(a!=null&amp;&amp;a[w]===y){e=a.g;const c=e[x]|0;z(a,c)||(c&amp;2?b=!0:c&amp;32&amp;&amp;!(c&amp;4096)?(e[x]=c|2,a.h=A,b=!0):b=!1,b?(a=new a.constructor(e),a.m=A):a=M(e,c));return a}}function M(a,b,e,c){c??(c=!!(34&amp;b));a=E(a,b,L,c);c=32;e&amp;&amp;(c|=2);b=b&amp;8380609|c;a[x]=b;return a};function N(a,b,e){if(e!=null&amp;&amp;typeof e!==&quot;string&quot;)throw Error();if(a.h===A){var c=a.g;c=M(c,c[x]|0);c[x]|=2048;a.g=c;a.h=void 0;a.m=void 0;c=!0}else c=!1;if(!c&amp;&amp;z(a,a.g[x]|0))throw Error();a=a.g;a:{var d=a[x]|0;c=b+-1;const l=a.length-1;if(l>=0&amp;&amp;c>=l){const f=a[l];if(f!=null&amp;&amp;typeof f===&quot;object&quot;&amp;&amp;f.constructor===Object){f[b]=e;break a}}c<=l?a[c]=e:e!==void 0&amp;&amp;(d=(d??a[x]|0)>>13&amp;1023||536870912,b>=d?e!=null&amp;&amp;(a[d+-1]={[b]:e}):a[c]=e)}};var O=class{constructor(a){this.g=J(a)}toJSON(){return I(this)}};O.prototype[w]=y;O.prototype.toString=function(){return this.g.toString()};var P=class extends O{};function Q(a=window){return a};var R=/#|$/;const S=function(a,b=null){return b&amp;&amp;b.getAttribute(&quot;data-jc&quot;)===String(a)?b:document.querySelector(`[${&quot;data-jc&quot;}=&quot;${a}&quot;]`)}(103,document.currentScript);if(S==null)throw Error(&quot;JSC not found 103&quot;);const T={},U=S.attributes;for(let a=U.length-1;a>=0;a--){const b=U[a].name;b.indexOf(&quot;data-jcp-&quot;)===0&amp;&amp;(T[b.substring(9)]=U[a].value)} (function(a,b,e){var c=window;a&amp;&amp;b&amp;&amp;e&amp;&amp;c.ReportingObserver&amp;&amp;c.fetch&amp;&amp;(new c.ReportingObserver((d,l)=>{d=d[0];if(d?.body?.id===&quot;HeavyAdIntervention&quot;){d=(d.body.message?.indexOf(&quot;network&quot;)||0)>0?e:b;var f=a.search(R);var g;b:{for(g=0;(g=a.indexOf(&quot;ad_signals&quot;,g))>=0&amp;&amp;g<f;){var k=a.charCodeAt(g-1);if(k==38||k==63)if(k=a.charCodeAt(g+10),!k||k==61||k==38||k==35)break b;g+=11}g=-1}k=g;if(k<0)f=null;else{g=a.indexOf(&quot;&amp;&quot;,k);if(g<0||g>f)g=f;f=decodeURIComponent(a.slice(k+11,g!==-1?g:0).replace(/\+/g,&quot; &quot;))}f? (navigator.sendBeacon(&quot;https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&amp;label=&quot;+d),d={i:f,label:d},f=new P,d!=null&amp;&amp;(d.i!=null&amp;&amp;N(f,1,d.i),d.s!=null&amp;&amp;N(f,3,d.s),d.label!=null&amp;&amp;N(f,6,d.label),d.l!=null&amp;&amp;N(f,7,d.l),d.j!=null&amp;&amp;N(f,8,d.j),d.o!=null&amp;&amp;N(f,11,d.o)),Q(h).fence?.reportEvent({eventType:&quot;interaction&quot;,eventData:JSON.stringify(I(f)),destination:[&quot;buyer&quot;]})):c.fetch(`${a}&amp;label=${d}`,{keepalive:!0,method:&quot;get&quot;,mode:&quot;no-cors&quot;});l.disconnect()}},{types:[&quot;intervention&quot;], buffered:!0})).observe()})(T.base_url,T.cpu_label,T.net_label);}).call(this);</script><script id=&quot;googleActiveViewDisplayScript&quot; src=&quot;https://pagead2.googlesyndication.com/pagead/managed/js/activeview/current/ufs_web_display.js&quot;></script><script type=&quot;text/javascript&quot;>osdlfm();</script></body></html>{&quot;uid&quot;:&quot;2&quot;,&quot;hostPeerName&quot;:&quot;https://www.smartbackgroundchecks.com&quot;,&quot;initialGeometry&quot;:&quot;{\&quot;windowCoords_t\&quot;:-16000,\&quot;windowCoords_r\&quot;:-15958,\&quot;windowCoords_b\&quot;:-15986,\&quot;windowCoords_l\&quot;:-16000,\&quot;frameCoords_t\&quot;:730.796875,\&quot;frameCoords_r\&quot;:681,\&quot;frameCoords_b\&quot;:980.796875,\&quot;frameCoords_l\&quot;:381,\&quot;styleZIndex\&quot;:\&quot;auto\&quot;,\&quot;allowedExpansion_t\&quot;:0,\&quot;allowedExpansion_r\&quot;:0,\&quot;allowedExpansion_b\&quot;:0,\&quot;allowedExpansion_l\&quot;:0,\&quot;xInView\&quot;:0,\&quot;yInView\&quot;:0}&quot;,&quot;permissions&quot;:&quot;{\&quot;expandByOverlay\&quot;:false,\&quot;expandByPush\&quot;:false,\&quot;readCookie\&quot;:false,\&quot;writeCookie\&quot;:false}&quot;,&quot;metadata&quot;:&quot;{\&quot;shared\&quot;:{\&quot;sf_ver\&quot;:\&quot;1-0-45\&quot;,\&quot;ck_on\&quot;:1,\&quot;flash_ver\&quot;:\&quot;0\&quot;}}&quot;,&quot;reportCreativeGeometry&quot;:false,&quot;isDifferentSourceWindow&quot;:false,&quot;goog_safeframe_hlt&quot;:{}}" scrolling="no" marginwidth="0" marginheight="0" width="300" height="250" data-is-safeframe="true" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" allow="private-state-token-redemption;attribution-reporting" aria-label="Advertisement" tabindex="0" data-google-container-id="2" style="border: 0px; vertical-align: bottom;"></iframe></div></div></div><br><h3 class="titleBox">(************* - Who Owns This Number:</h3><div class="py-3"><div class="faq-question-new">Who currently owns the phone number (*************?</div><div class="faq-answer">The current owner for (************* is <a href="https://www.smartbackgroundchecks.com/people/brenda-mccorvey/EmRjBGt0ZGH1ZQN1ZwHlBQL4AmH">Brenda Mccorvey</a> who lives in Royal Palm Beach, FL.</div><br><br><div class="faq-question-new">Is (************* a wireless or landline phone?</div><div class="faq-answer">(************* is a Wireless phone.</div><br><br><div class="faq-question-new">What carrier or phone company provides service for (*************?</div><div class="faq-answer">MetroPCS Inc is the current provider for (************* in Royal Palm Beach, FL.</div><br><br><div class="faq-question-new">Is the phone (************* active or disconnected?</div><div class="faq-answer">(************* appears to be currently connected and working.</div><br><br><div class="faq-question-new">Who else has used the phone (************* in the past?</div><div class="faq-answer">Previous owners of (************* include <a href="https://www.smartbackgroundchecks.com/people/john-mcphee/El0lZQL3AwtlZmxlZGt2AmV4ZwV4">John Mcphee</a>.</div><br><br></div></div></div><br><div id="wam_placeholder" style="min-height:400px"><br><div class="row no-gutters m-0 p-1 mw-100 card-block cardhoverbold card-normal" style="background-color: #FBFBFB"><div class="p-1 mw-100" style="line-height: 20px">Public Records for <strong>(*************</strong><br>Paid Results Sponsored by <strong>TruthFinder.com</strong><br><div class="ml-3 widgets row">								  <div class="col-md-4 col-sm-4 ml-2" style="width:300px;padding-top:5px;padding-bottom:5px"><b>John B Mcphee</b></div>								  <div class="col-md-3 col-sm-4" style="width:250px;padding-top:5px;padding-bottom:5px">Lantana, FL</div>								  <div class="col-md-2 col-sm-4" style="width:75px;padding-top:5px;padding-bottom:5px">Age:29</div>								  <div class="col-md-3 col-sm-12" style="width:100%;padding-top:5px;padding-bottom:5px; text-align:right"><button type="button" rel="nofollow noindex sponsored" onclick="logClick('aHR0cHM6Ly90cmFja2luZy50cnV0aGZpbmRlci5jb20vP2E9MTA2OSZvYz0yNyZjPTI4OCZzdWJ0aGVtZT1iYWNrZ3JvdW5kJnMxPUNNLVNtYXJ0QmFja2dyb3VuZENoZWNrcyZzMj1BRy1QaG9uZSZzMz1DUkUtKyZzND1MUC0yODgmczU9JmZuYW1lPUpvaG4mbG5hbWU9TWNwaGVlJnN0YXRlPUZMJmNpdHk9TGFudGFuYQ==','eyJJbXByZXNzaW9uSWQiOiAiMzA1MzA2MDY5MzMiLCJTaXRlSWQiOiAzMiwiVXRtU291cmNlIjogInBlb3BsZWZpbmRlcnMiLCJVdG1DYW1wYWlnbiI6ICIiLCJGcm9tUGFnZSI6ICIvYWpheF93YW1fd2lkZ2V0cy5waHA/bGFuZz0mc2VhcmNoUGFybT01NjE5MzI0MjE3Jmxhbmc9JnR5cGU9UGhvbmUlMjBSZXN1bHRzJmRhdGE9VzNzaWRHRm9iMlZwWkNJNklrY3hNRGs0TkRFMU5UQXdOVEkxTWpnMk9EYzFJaXdpWm1seWMzUmZibUZ0WlNJNklrSnlaVzVrWVNJc0ltMXBaR1JzWlY5dVlXMWxJam9pSWl3aWJHRnpkRjl1WVcxbElqb2lUV05qYjNKMlpYa2lMQ0p6ZEhKbFpYUWlPaUl4TWpNZ1MybHVaM01nVjJGNUlpd2lZMmwwZVNJNklsSnZlV0ZzSUZCaGJHMGdRbVZoWTJnaUxDSnpkR0YwWlNJNklrWk1JaXdpWVdkbElqbzJNQ3dpY0dodmJtVWlPaUlvTlRZeEtTQXlPVE10TURNMk1DSjlMSHNpZEdGb2IyVnBaQ0k2SWtjdE1qQTJOelk0TWpNNU1qRTROamN5T0RJeU9DSXNJbVpwY25OMFgyNWhiV1VpT2lKS2IyaHVJaXdpYldsa1pHeGxYMjVoYldVaU9pSkNJaXdpYkdGemRGOXVZVzFsSWpvaVRXTndhR1ZsSWl3aWMzUnlaV1YwSWpvaU1qQXhJRkJzZFcwZ1ZISmxaU0JFY2lJc0ltTnBkSGtpT2lKTVlXNTBZVzVoSWl3aWMzUmhkR1VpT2lKR1RDSXNJbUZuWlNJNk1qa3NJbkJvYjI1bElqb2lLRFUyTVNrZ09ETTRMVE14TlRFaWZWMD0mXz0xNzQ4OTU4NzExNjczIiwiUmVxdWVzdElkIjogIiIsIldpZGdldCI6IHsiUGFydG5lcklkIjogIjkiLCJTbG90SWQiOiAxMTY4LCJVbmlxdWVHdWlkIjogIjFmY2MxODY4LTFkYjctNDNiNC1iYzJkLTQ3ZjNhMWZiOTE5NyIsIlNsb3ROdW1iZXIiOiAiMSIsIkRGUFVybCI6ICIiLCJSZWRpcmVjdFVybCI6ICJodHRwczovL3RyYWNraW5nLnRydXRoZmluZGVyLmNvbS8/YT0xMDY5Jm9jPTI3JmM9Mjg4JnN1YnRoZW1lPWJhY2tncm91bmQmczE9Q00tU21hcnRCYWNrZ3JvdW5kQ2hlY2tzJnMyPUFHLVBob25lJnMzPUNSRS0rJnM0PUxQLTI4OCZzNT0mZm5hbWU9Sm9obiZsbmFtZT1NY3BoZWUmc3RhdGU9RkwmY2l0eT1MYW50YW5hIn19')" class="btn btn-primary btn-sm wam_hover1" style="width:100%">VIEW DETAILS <img src="/images/arrow-circle-right-1-16.png" alt="VIEW DETAILS" width="16" height="16"></button></div>								  </div><table><tbody></tbody></table></div></div><br><div class="row no-gutters m-0 p-1 mw-100 card-block cardhoverbold card-normal" style="background-color: #FBFBFB"><div class="p-1 mw-100" style="line-height: 20px">Public Records for <strong>(*************</strong><br>Paid Results Sponsored by <strong>InstantCheckmate.com</strong><br><div class="ml-3 widgets row">								  <div class="col-md-4 col-sm-4 ml-2" style="width:300px;padding-top:5px;padding-bottom:5px"><b>John B Mcphee</b></div>								  <div class="col-md-3 col-sm-4" style="width:250px;padding-top:5px;padding-bottom:5px">Lantana, FL</div>								  <div class="col-md-2 col-sm-4" style="width:75px;padding-top:5px;padding-bottom:5px">Age:29</div>								  <div class="col-md-3 col-sm-12" style="width:100%;padding-top:5px;padding-bottom:5px; text-align:right"><button type="button" rel="nofollow noindex sponsored" onclick="logClick('aHR0cHM6Ly90cmFja2luZy5pbnN0YW50Y2hlY2ttYXRlLmNvbS8/YT0xMDY5Jm9jPTEmYz0yNDEmc3VidGhlbWU9cHVibGljLXJlY29yZHMmczE9Q00tU21hcnRCYWNrZ3JvdW5kQ2hlY2tzJnMyPUFHLVBob25lJnMzPUNSRS1XaWRnZXQrJnM0PUxQLTI0MVBSJnM1PSZmbmFtZT1Kb2huJmxuYW1lPU1jcGhlZSZzdGF0ZT1GTCZjaXR5PUxhbnRhbmE=','eyJJbXByZXNzaW9uSWQiOiAiMzA1MzA2MDY5MzkiLCJTaXRlSWQiOiAzMiwiVXRtU291cmNlIjogInBlb3BsZWZpbmRlcnMiLCJVdG1DYW1wYWlnbiI6ICIiLCJGcm9tUGFnZSI6ICIvYWpheF93YW1fd2lkZ2V0cy5waHA/bGFuZz0mc2VhcmNoUGFybT01NjE5MzI0MjE3Jmxhbmc9JnR5cGU9UGhvbmUlMjBSZXN1bHRzJmRhdGE9VzNzaWRHRm9iMlZwWkNJNklrY3hNRGs0TkRFMU5UQXdOVEkxTWpnMk9EYzFJaXdpWm1seWMzUmZibUZ0WlNJNklrSnlaVzVrWVNJc0ltMXBaR1JzWlY5dVlXMWxJam9pSWl3aWJHRnpkRjl1WVcxbElqb2lUV05qYjNKMlpYa2lMQ0p6ZEhKbFpYUWlPaUl4TWpNZ1MybHVaM01nVjJGNUlpd2lZMmwwZVNJNklsSnZlV0ZzSUZCaGJHMGdRbVZoWTJnaUxDSnpkR0YwWlNJNklrWk1JaXdpWVdkbElqbzJNQ3dpY0dodmJtVWlPaUlvTlRZeEtTQXlPVE10TURNMk1DSjlMSHNpZEdGb2IyVnBaQ0k2SWtjdE1qQTJOelk0TWpNNU1qRTROamN5T0RJeU9DSXNJbVpwY25OMFgyNWhiV1VpT2lKS2IyaHVJaXdpYldsa1pHeGxYMjVoYldVaU9pSkNJaXdpYkdGemRGOXVZVzFsSWpvaVRXTndhR1ZsSWl3aWMzUnlaV1YwSWpvaU1qQXhJRkJzZFcwZ1ZISmxaU0JFY2lJc0ltTnBkSGtpT2lKTVlXNTBZVzVoSWl3aWMzUmhkR1VpT2lKR1RDSXNJbUZuWlNJNk1qa3NJbkJvYjI1bElqb2lLRFUyTVNrZ09ETTRMVE14TlRFaWZWMD0mXz0xNzQ4OTU4NzExNjczIiwiUmVxdWVzdElkIjogIiIsIldpZGdldCI6IHsiUGFydG5lcklkIjogIjEwIiwiU2xvdElkIjogMTE2OSwiVW5pcXVlR3VpZCI6ICI2NWI3MGY5MS01ZWNjLTQyM2ItOThiZS0zMTIwZjFlMTQ1NzAiLCJTbG90TnVtYmVyIjogIjIiLCJERlBVcmwiOiAiIiwiUmVkaXJlY3RVcmwiOiAiaHR0cHM6Ly90cmFja2luZy5pbnN0YW50Y2hlY2ttYXRlLmNvbS8/YT0xMDY5Jm9jPTEmYz0yNDEmc3VidGhlbWU9cHVibGljLXJlY29yZHMmczE9Q00tU21hcnRCYWNrZ3JvdW5kQ2hlY2tzJnMyPUFHLVBob25lJnMzPUNSRS1XaWRnZXQrJnM0PUxQLTI0MVBSJnM1PSZmbmFtZT1Kb2huJmxuYW1lPU1jcGhlZSZzdGF0ZT1GTCZjaXR5PUxhbnRhbmEifX0=')" class="btn btn-primary btn-sm wam_hover1" style="width:100%">VIEW DETAILS <img src="/images/arrow-circle-right-1-16.png" alt="VIEW DETAILS" width="16" height="16"></button></div>								  </div><table><tbody></tbody></table></div></div><br><div class="row no-gutters m-0 p-1 mw-100 card-block cardhoverbold card-normal" style="background-color: #FBFBFB"><div class="p-1 mw-100" style="line-height: 20px">Public Records for <strong>(*************</strong><br>Paid Results Sponsored by <strong>PrivateRecords.net</strong><br><div class="ml-3 widgets row">								  <div class="col-md-4 col-sm-4 ml-2" style="width:300px;padding-top:5px;padding-bottom:5px"><b>John B Mcphee</b></div>								  <div class="col-md-3 col-sm-4" style="width:250px;padding-top:5px;padding-bottom:5px">Lantana, FL</div>								  <div class="col-md-2 col-sm-4" style="width:75px;padding-top:5px;padding-bottom:5px">Age:29</div>								  <div class="col-md-3 col-sm-12" style="width:100%;padding-top:5px;padding-bottom:5px; text-align:right"><button type="button" rel="nofollow noindex sponsored" onclick="logClick('aHR0cHM6Ly93d3cucHJpdmF0ZXJlY29yZHMubmV0L3Bob25lL2xhbmRpbmc/dXhjPTYzZDlhZjg0NDI3ZTQyYzhlODAzYjc1OSZ1dG1fcmVmZXJfcGZzdWI9U0JDX1Bob25lX1RlYXNlcl9XaWRnZXQmdWlfcGhvbmU9NTYxODM4MzE1MSZza2lwPXRydWU=','eyJJbXByZXNzaW9uSWQiOiAiMzA1MzA2MDY5NDMiLCJTaXRlSWQiOiAzMiwiVXRtU291cmNlIjogInBlb3BsZWZpbmRlcnMiLCJVdG1DYW1wYWlnbiI6ICIiLCJGcm9tUGFnZSI6ICIvYWpheF93YW1fd2lkZ2V0cy5waHA/bGFuZz0mc2VhcmNoUGFybT01NjE5MzI0MjE3Jmxhbmc9JnR5cGU9UGhvbmUlMjBSZXN1bHRzJmRhdGE9VzNzaWRHRm9iMlZwWkNJNklrY3hNRGs0TkRFMU5UQXdOVEkxTWpnMk9EYzFJaXdpWm1seWMzUmZibUZ0WlNJNklrSnlaVzVrWVNJc0ltMXBaR1JzWlY5dVlXMWxJam9pSWl3aWJHRnpkRjl1WVcxbElqb2lUV05qYjNKMlpYa2lMQ0p6ZEhKbFpYUWlPaUl4TWpNZ1MybHVaM01nVjJGNUlpd2lZMmwwZVNJNklsSnZlV0ZzSUZCaGJHMGdRbVZoWTJnaUxDSnpkR0YwWlNJNklrWk1JaXdpWVdkbElqbzJNQ3dpY0dodmJtVWlPaUlvTlRZeEtTQXlPVE10TURNMk1DSjlMSHNpZEdGb2IyVnBaQ0k2SWtjdE1qQTJOelk0TWpNNU1qRTROamN5T0RJeU9DSXNJbVpwY25OMFgyNWhiV1VpT2lKS2IyaHVJaXdpYldsa1pHeGxYMjVoYldVaU9pSkNJaXdpYkdGemRGOXVZVzFsSWpvaVRXTndhR1ZsSWl3aWMzUnlaV1YwSWpvaU1qQXhJRkJzZFcwZ1ZISmxaU0JFY2lJc0ltTnBkSGtpT2lKTVlXNTBZVzVoSWl3aWMzUmhkR1VpT2lKR1RDSXNJbUZuWlNJNk1qa3NJbkJvYjI1bElqb2lLRFUyTVNrZ09ETTRMVE14TlRFaWZWMD0mXz0xNzQ4OTU4NzExNjczIiwiUmVxdWVzdElkIjogIiIsIldpZGdldCI6IHsiUGFydG5lcklkIjogIjI5IiwiU2xvdElkIjogMTU4MCwiVW5pcXVlR3VpZCI6ICI3ZTUyYjk0ZS05ZmVkLTQ4NWItOGNmMy0yYTQ5NWJhMTIwNDMiLCJTbG90TnVtYmVyIjogIjMiLCJERlBVcmwiOiAiIiwiUmVkaXJlY3RVcmwiOiAiaHR0cHM6Ly93d3cucHJpdmF0ZXJlY29yZHMubmV0L3Bob25lL2xhbmRpbmc/dXhjPTYzZDlhZjg0NDI3ZTQyYzhlODAzYjc1OSZ1dG1fcmVmZXJfcGZzdWI9U0JDX1Bob25lX1RlYXNlcl9XaWRnZXQmdWlfcGhvbmU9NTYxODM4MzE1MSZza2lwPXRydWUifX0=')" class="btn btn-primary btn-sm wam_hover1" style="width:100%">VIEW DETAILS <img src="/images/arrow-circle-right-1-16.png" alt="VIEW DETAILS" width="16" height="16"></button></div>								  </div><table><tbody></tbody></table></div></div></div></div>

<div name="rightPanel" class="col ad-sidebar break-word hidden-sm-down d-none d-lg-block" style="max-width:160px"><div id="bsa-zone_1743779232373-3_123456" data-google-query-id="CNqE7YCz1Y0DFTiupgQdf6wy8A"><div id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_RightSidebar_ROS_0__container__" style="border: 0pt none; margin: auto; text-align: center;"><iframe id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_RightSidebar_ROS_0" name="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_RightSidebar_ROS_0" title="3rd party ad content" width="160" height="600" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" aria-label="Advertisement" tabindex="0" allow="private-state-token-redemption;attribution-reporting" data-load-complete="true" data-google-container-id="4" style="border: 0px; vertical-align: bottom;"></iframe></div></div></div></div></div>

    		<div class="row" style="padding: 15px">
			<div class="col-12 text-center">
				<div class="footer pb-4" style="line-height:150%">
					<a href="https://www.smartbackgroundchecks.com/names/a" title="Last Names That Start With A" class="btn footer link-underline font-weight-bold">&nbsp;A&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/b" title="Last Names That Start With B" class="btn footer link-underline font-weight-bold">&nbsp;B&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/c" title="Last Names That Start With C" class="btn footer link-underline font-weight-bold">&nbsp;C&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/d" title="Last Names That Start With D" class="btn footer link-underline font-weight-bold">&nbsp;D&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/e" title="Last Names That Start With E" class="btn footer link-underline font-weight-bold">&nbsp;E&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/f" title="Last Names That Start With F" class="btn footer link-underline font-weight-bold">&nbsp;F&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/g" title="Last Names That Start With G" class="btn footer link-underline font-weight-bold">&nbsp;G&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/h" title="Last Names That Start With H" class="btn footer link-underline font-weight-bold">&nbsp;H&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/i" title="Last Names That Start With I" class="btn footer link-underline font-weight-bold">&nbsp;I&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/j" title="Last Names That Start With J" class="btn footer link-underline font-weight-bold">&nbsp;J&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/k" title="Last Names That Start With K" class="btn footer link-underline font-weight-bold">&nbsp;K&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/l" title="Last Names That Start With L" class="btn footer link-underline font-weight-bold">&nbsp;L&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/m" title="Last Names That Start With M" class="btn footer link-underline font-weight-bold">&nbsp;M&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/n" title="Last Names That Start With N" class="btn footer link-underline font-weight-bold">&nbsp;N&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/o" title="Last Names That Start With O" class="btn footer link-underline font-weight-bold">&nbsp;O&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/p" title="Last Names That Start With P" class="btn footer link-underline font-weight-bold">&nbsp;P&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/q" title="Last Names That Start With Q" class="btn footer link-underline font-weight-bold">&nbsp;Q&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/r" title="Last Names That Start With R" class="btn footer link-underline font-weight-bold">&nbsp;R&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/s" title="Last Names That Start With S" class="btn footer link-underline font-weight-bold">&nbsp;S&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/t" title="Last Names That Start With T" class="btn footer link-underline font-weight-bold">&nbsp;T&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/u" title="Last Names That Start With U" class="btn footer link-underline font-weight-bold">&nbsp;U&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/v" title="Last Names That Start With V" class="btn footer link-underline font-weight-bold">&nbsp;V&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/w" title="Last Names That Start With W" class="btn footer link-underline font-weight-bold">&nbsp;W&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/x" title="Last Names That Start With X" class="btn footer link-underline font-weight-bold">&nbsp;X&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/y" title="Last Names That Start With Y" class="btn footer link-underline font-weight-bold">&nbsp;Y&nbsp;</a><a href="https://www.smartbackgroundchecks.com/names/z" title="Last Names That Start With Z" class="btn footer link-underline font-weight-bold">&nbsp;Z&nbsp;</a><br><a href="/phones" title="Phone Directory" class="btn footer link-underline font-weight-bold">Phone Directory:</a> <a href="https://www.smartbackgroundchecks.com/phones/2" title="Phones starting with 2" class="btn footer link-underline font-weight-bold">2</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/3" title="Phones starting with 3" class="btn footer link-underline font-weight-bold">3</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/4" title="Phones starting with 4" class="btn footer link-underline font-weight-bold">4</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/5" title="Phones starting with 5" class="btn footer link-underline font-weight-bold">5</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/6" title="Phones starting with 6" class="btn footer link-underline font-weight-bold">6</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/7" title="Phones starting with 7" class="btn footer link-underline font-weight-bold">7</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/8" title="Phones starting with 8" class="btn footer link-underline font-weight-bold">8</a>&nbsp;<a href="https://www.smartbackgroundchecks.com/phones/9" title="Phones starting with 9" class="btn footer link-underline font-weight-bold">9</a>&nbsp;                </div>
				<br>
				<div>					
					<br><br>
					<h2 class="h1Title">NEED MORE DATA IN REAL-TIME?</h2>
					<div style="width:100px;border-top:3px solid #ccc;margin:10px auto"></div>
					<h3><strong>
					Get access to our partner Endato’s fast Developer API for Contact Enrichment, Sales and Marketing Intelligence.  
					</strong></h3>
					<a class="btn btn-danger" href="https://endato.com/sign-up/smartbc/?utm_medium=affiliate&amp;utm_source=smartbc&amp;utm_campaign=footer_message" role="button">Start Free Trial</a>
				</div>
				<br><br><br>
				<div class="footer pb-4">
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/" title="Person Name Search">Name Search</a> | 
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/address" title="Address Search">Address Search</a> | 
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/phone" title="Reverse Phone Search">Phone Search</a> | 
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/names" title="Full Name Directory">Directory</a> | 
                    <a class="link-underline" href="https://www.smartbackgroundchecks.com/phones" title="Phone Directory">Phone Directory</a>
					<br>
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/terms" title="Terms of Use">Terms</a> | 
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/privacy-rights" title="Privacy Notice">Privacy Notice</a> | 
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/notice-at-collection" title="Notice at Collection">Notice at Collection</a> | 
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/do-not-sell" title="Do Not Sell or Share My Personal Information">Do Not Sell or Share My Personal Information</a>                     
					<br>
					<a class="link-underline" href="https://www.smartbackgroundchecks.com/contact" title="How to Contact Us">Contact</a> | 
                    <a class="link-underline" rel="sponsored" href="https://endato.com/sign-up/smartbc/?utm_medium=affiliate&amp;utm_source=smartbc&amp;utm_campaign=footer_link " target="_blank">SmartBackgroundChecks API</a> |
										
					<div class="p-4">&nbsp;&nbsp;&nbsp;<a href="/es/phone/5619324217" title="Ver esta página en Español">Ver en español</a></div>
					© SmartBackgroundChecks.com - 2024<br>
				</div><br>
                <small>SmartBackgroundChecks.com is not a Consumer Reporting Agency (CRA) as defined by the Fair Credit Reporting Act (<a href="https://en.wikipedia.org/wiki/Fair_Credit_Reporting_Act">FCRA</a>).<br>This site can't be used for employment, credit or tenant screening, or any related purpose.</small>
				<br>
			</div>
		</div>	</div>
</div>
<div id="gdpr-cookie-footer" style="display:none"><button id="button-gdpr-agree" class="btn btn-sm btn-success" onclick="setGDPRCookie()">I Agree</button>To provide you with an optimal experience on this website, we use cookies. If you continue to use this website, you agree to accept our use of cookies. To learn more, read our <a href="/privacy">Privacy Policy</a>, and our <a href="/terms">Terms of Use</a></div>
<script src="/vendor/jquery-3.5.1.min.js"></script>
<script defer="" src="/vendor/bootstrap441_min.js"></script>
<script async="" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************" crossorigin="anonymous" data-checked-head="true"></script>
<script async="" src="https://cdn.adapex.io/hb/aaw.sbc3.js"></script>
<script defer="" src="https://www.googletagservices.com/tag/js/gpt.js"></script>
<script defer="" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<script defer="" src="https://ad.doubleclick.net/ddm/trackimpj/N9037.838836IMEDIAAUDIENCES/*********.342249574;dc_trk_aid=533853368;dc_trk_cid=175480050;ord=;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;gdpr=$;gdpr_consent=$;ltd=?"></script>

<script>
$(document).ready(function() {
	$('#inputFirstName').bind('keypress', function(e) { if(e.keyCode==13){ validateSearchForm() } });
	$('#inputMiddleName').bind('keypress', function(e) { if(e.keyCode==13){ validateSearchForm() } });
	$('#inputLastName').bind('keypress', function(e) { if(e.keyCode==13){ validateSearchForm() } });
	$('#inputCityState').bind('keypress', function(e) { if(e.keyCode==13){ validateSearchForm() } });
	$('#inputStreet').bind('keypress', function(e) { if(e.keyCode==13){ validateSearchForm() } });
	$('#inputPhone').bind('keypress', function(e) { if(e.keyCode==13){ validateSearchForm() } });	

	//Show optout box if there is a cookie
	if (document.cookie.indexOf("allow_optout") > -1) {
		$('#optoutbox').show();
	}
		$.getScript('/ajax_wam_widgets.php?lang=&searchParm=5619324217&lang=&type=Phone%20Results&data=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', function( data, textStatus, jqxhr ) {});
	showSearchform('phone');
  showMap();
});

function showSearchform(formNum,fName,mName,lName,street,cityState,phone) {
	//Search tabs
	$(".searchByPhone").hide();
	$(".searchByAddress").hide();
	$(".searchByName").hide();
	
	//Set the form values
	if (fName) 		{ $("#inputFirstName").val(fName); }
	if (mName) 		{ $("#inputMiddleName").val(mName); }
	if (lName) 		{ $("#inputLastName").val(lName); }
	if (street) 	{ $("#inputStreet").val(street); }
	if (cityState) 	{ $("#inputCityState").val(cityState); }
	if (phone) 		{ $("#inputPhone").val(phone); }
	
	//Show the right tab
	switch(formNum) {
		case '0':
			$("#tab_name").tab('show')
			$(".searchByName").show();
			$("#searchByType").val("people");
			break;
		case 'name':
			$("#tab_name").tab('show')
			$(".searchByName").show();
			$("#searchByType").val("people");
			break;
		case 'person':
			$("#tab_name").tab('show')
			$(".searchByName").show();
			$("#searchByType").val("people");
			break;
		case '1':
			$("#tab_phone").tab('show');
			$(".searchByPhone").show();
			$("#searchByType").val("phone");
			break;		
		case 'phone':
			$("#tab_phone").tab('show');
			$(".searchByPhone").show();
			$("#searchByType").val("phone");
			break;		
		case '2':
			$("#tab_address").tab('show')
			$(".searchByAddress").show();
			$("#searchByType").val("address");
			break;
		case 'address':
			$("#tab_address").tab('show')
			$(".searchByAddress").show();
			$("#searchByType").val("address");
			break;
		default:
			$("#tab_name").tab('show')
			$(".searchByName").show();
			$("#searchByType").val("people");
			break;		
	}
}
	
function validateSearchForm() {
	//Determine method we need to validate
	var formType = $("#searchByType").val();
	$("#formError").text("");
	$("#formErrorRow").hide();
	
	//Check for minimum values based on form type
	var errMessage = "";
	
	switch(formType) {
		case "people":
			var searchName = ($("#inputFirstName").val() + $("#inputMiddleName").val() + $("#inputLastName").val()).trim();
			var fName      = ($("#inputFirstName").val()).trim();
			var lName      = ($("#inputLastName").val()).trim();
			var searchCS   = ($("#inputCityState").val()).trim();
			if (searchName.length < 4 || searchCS.length == 1 || fName.length == 0 || lName.length == 0) {
				//errMessage = "Please provide a longer name or location";
			}
			break;
			
		case "address":
			var searchStreet = ($("#inputStreet").val()).trim();
			var searchCS     = ($("#inputCityState").val()).trim();
			if (searchStreet.length < 4 || searchCS.length < 2) {
				errMessage = "Please provide a street address and a city or state";
			}
			
			if (searchStreet.length > 4 && searchCS.length < 2) {
				errMessage = "Please provide a state";
			}
			break;
			
		case "phone":
			var searchPhone = ($("#inputPhone").val()).replace(/\D/g,'');
			if (searchPhone.length != 10) {
				errMessage = "Please provide a valid phone number";
			}
			break;
	}
	
	if (!errMessage) {
		$("#searchForm").submit();
	} else {
		//Show the error message
		$("#formError").text(errMessage);
		$("#formErrorRow").show();
	}
}
    
function setGDPRCookie() {
    var date = new Date();
    date.setTime(date.getTime() + (365*24*60*60*1000));
    document.cookie = "gdpr_accept=true; expires="+date.toUTCString();
    $("#gdpr-cookie-footer").hide();
}
	
function newSearch() {
	document.location.href = "https://www.smartbackgroundchecks.com/";
}


function loadWidgets(wamType,wamData) {
	$.post("/ajax_wam.php",{type:wamType,data:wamData,lang:'',rand:Math.random(),button:0}).done(function(data) { $("#wam_placeholder").html(data);});
}

function loadButton(wamType,wamData,nameData,wamPct,WamDivName,slotId) {
	$.post("/ajax_wam.php",{type:wamType,data:wamData,name:nameData,pct:wamPct,slot:slotId,lang:'',rand:Math.random(),button:1}).done(function(data) { $("#"+WamDivName).html(data);});
}

function logClick(id,addclick) {
	url = "/utilityWAM.php?tracking_id="+id+"&add_click="+addclick;
	window.open(url);
}
    
function logButton(campaign,action,page,button) {
    url = "/ajax_buttonTrack.php?campaign="+campaign+"&action="+action+"&page="+page+"&button="+button;
    $.post(url,{});
}
    
function gotoNonFCRA(utmcampaign,pagesrc='') {
	var winNonFCRA      = window.open();
	winNonFCRA.opener 	= null;
	winNonFCRA.location = ''+'&utm_campaign='+utmcampaign+'&page_src='+pagesrc;
}
//t=k for sat view
function showMap() {
    $('#mapouter').html('<div class="gmap_canvas"><iframe title="Area Code Map" width="100%" height="300" id="gmap_canvas" src="https://maps.google.com/maps?q=Royal+Palm+Beach%2C+FL&t=&z=10&ie=UTF8&iwloc=&output=embed" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe></div>');
    $('#mapouter').attr('class', 'mapouter');
	
		
		
	
	
}
</script>
<script data-ad-client="ca-pub-****************" async="" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" data-checked-head="true"></script>
<!-- Clarity tracking code for https://www.smartbackgroundchecks.com/ -->
<script>    (function(c,l,a,r,i,t,y){
c[a]=c[a]||function(){
(c[a].q=c[a].q||[]).push(arguments)};
t=l.createElement(r);
t.async=1;
t.src="https://www.clarity.ms/tag/"+i;
y=l.getElementsByTagName(r)[0];
y.parentNode.insertBefore(t,y);
}
)(window, document, "clarity", "script", "45wgqybilp");
</script>
<script type="application/ld+json">{"@context":"http://schema.org","@type":"BreadcrumbList","itemListElement": [{"@type": "ListItem","position": 1,"item":"https://www.smartbackgroundchecks.com/","name": "Search"},{"@type": "ListItem","position": 2,"item":"https://www.smartbackgroundchecks.com/phones/561","name": "561 Area Code"},{"@type": "ListItem","position": 3,"item":"https://www.smartbackgroundchecks.com/phone/5619324217","name": "People With Phone 5619324217"}]}</script>
<script type="application/ld+json">{"@context":"http://schema.org","@type":"Person","@id":"https://www.smartbackgroundchecks.com/phone/5619324217","URL":"https://www.smartbackgroundchecks.com/phone/5619324217","name":"Brenda Mccorvey","honorrificPrefix":"","givenName":"Brenda","familyName":"Mccorvey","additionalName":["Brenda L Mccorvey","Brenda Mcorvey","Brenda Lee Mccorvey","Brenda Mcphee","Lee B Mccorvey","Brenda L Mccorey","Lee Mccorvey Renda","Brenda Mccovery","Brenda Mcforvey"],"homeLocation":{"@type":"Place","@id":"/address/123-kings-way/royal-palm-beach/fl","url":"/address/123-kings-way/royal-palm-beach/fl","description":"Current home address for Brenda Mccorvey","address":{"@type":"PostalAddress","streetAddress":"123 Kings Way","addressLocality":"Royal Palm Beach","addressRegion":"FL","postalCode":"33411"},"geo":{"@type":"GeoCoordinates","latitude":"26.702346","longitude":"-80.244713"}},"relatedTo":[{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/barbara-mccorvey/EmVjBQRkBQRjZmHmAQZkBQtkZmV","URL":"https://www.smartbackgroundchecks.com/people/barbara-mccorvey/EmVjBQRkBQRjZmHmAQZkBQtkZmV","name":"Barbara O Mccorvey"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/john-mcphee/El0lZQL3AwtlZmxlZGt2AmV4ZwV4","URL":"https://www.smartbackgroundchecks.com/people/john-mcphee/El0lZQL3AwtlZmxlZGt2AmV4ZwV4","name":"John Bethel Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/jonathan-mcphee/EmV0AQp5AGRjZmRlZGD0Amt0BGt","URL":"https://www.smartbackgroundchecks.com/people/jonathan-mcphee/EmV0AQp5AGRjZmRlZGD0Amt0BGt","name":"Jonathan Ivan Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/lauren-mcphee/El01AmpmZmNjBQplAQR4BGD2AQLm","URL":"https://www.smartbackgroundchecks.com/people/lauren-mcphee/El01AmpmZmNjBQplAQR4BGD2AQLm","name":"Lauren M Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/annie-mcphee/El03ZGDjZQV2ZwVjAwN3ZGD5Awpl","URL":"https://www.smartbackgroundchecks.com/people/annie-mcphee/El03ZGDjZQV2ZwVjAwN3ZGD5Awpl","name":"Annie Pearl Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/annie-mcphee/El03ZGD5Zwx0AmN5BQH2BQx0AwV","URL":"https://www.smartbackgroundchecks.com/people/annie-mcphee/El03ZGD5Zwx0AmN5BQH2BQx0AwV","name":"Annie Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmxlAmtlAGDlZQN3ZmV2ZwNmBN","URL":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmxlAmtlAGDlZQN3ZmV2ZwNmBN","name":"Charles C Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/evelyn-garrett/El0mZmHkAQNkZQDkAGZ0ZQL2AGD4","URL":"https://www.smartbackgroundchecks.com/people/evelyn-garrett/El0mZmHkAQNkZQDkAGZ0ZQL2AGD4","name":"Evelyn M Garrett"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/alison-kuhl-mc-phee/El01AmD4BQZ1AmD3ZwLlZwZ2AwD2","URL":"https://www.smartbackgroundchecks.com/people/alison-kuhl-mc-phee/El01AmD4BQZ1AmD3ZwLlZwZ2AwD2","name":"Alison Kuhl Mc Phee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/alison-mcphee/El03AGLlZQH5AQV4ZGL5BGL4AmZ1","URL":"https://www.smartbackgroundchecks.com/people/alison-mcphee/El03AGLlZQH5AQV4ZGL5BGL4AmZ1","name":"Alison Kuhl Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/anneta-barnes/EmtkZmZ4ZQH3AmR1AwRjZGt4Amp","URL":"https://www.smartbackgroundchecks.com/people/anneta-barnes/EmtkZmZ4ZQH3AmR1AwRjZGt4Amp","name":"Anneta Felecia Barnes"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/anthony-polson/EmH4BGD1AGp2ZQL5ZmR0BQD2AQR","URL":"https://www.smartbackgroundchecks.com/people/anthony-polson/EmH4BGD1AGp2ZQL5ZmR0BQD2AQR","name":"Anthony Mcray Polson"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/austin-mcphee/EmR5ZmV5ZGH4AGN0ZGpjZQR3AmH","URL":"https://www.smartbackgroundchecks.com/people/austin-mcphee/EmR5ZmV5ZGH4AGN0ZGpjZQR3AmH","name":"Austin M Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/c-barnes/El0lZwt5AGt5ZGH0BGZ2AQL4BQZ1","URL":"https://www.smartbackgroundchecks.com/people/c-barnes/El0lZwt5AGt5ZGH0BGZ2AQL4BQZ1","name":"C L Barnes"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/catherine-reese/El0mAQZ2ZwV0ZQD3ZmR2AGp3Awpj","URL":"https://www.smartbackgroundchecks.com/people/catherine-reese/El0mAQZ2ZwV0ZQD3ZmR2AGp3Awpj","name":"Catherine A Reese"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/chante-garrett/EmH1ZQD1AwH2AGtlZQR5ZGNjBQZ","URL":"https://www.smartbackgroundchecks.com/people/chante-garrett/EmH1ZQD1AwH2AGtlZQR5ZGNjBQZ","name":"Chante Marie Garrett"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmV4Zmt0AGtjBQtkBQR1Zmx5AmZ","URL":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmV4Zmt0AGtjBQtkBQR1Zmx5AmZ","name":"Charles Angus Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmR5BQZ5BGRlZQD0ZQR2AGN1Amx","URL":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmR5BQZ5BGRlZQD0ZQR2AGN1Amx","name":"Charles Albert Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/charles-mcphee/El0mBGx3AwHkAmNmBGV3BQN2Awx0","URL":"https://www.smartbackgroundchecks.com/people/charles-mcphee/El0mBGx3AwHkAmNmBGV3BQN2Awx0","name":"Charles O Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/charles-mcphee/El0mZwV4AQDlAwp5BGN5AQH2AwHl","URL":"https://www.smartbackgroundchecks.com/people/charles-mcphee/El0mZwV4AQDlAwp5BGN5AQH2AwHl","name":"Charles B Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmR2AGt3ZQV4BQx3ZGV2AwL2Zwp","URL":"https://www.smartbackgroundchecks.com/people/charles-mcphee/EmR2AGt3ZQV4BQx3ZGV2AwL2Zwp","name":"Charles Albert Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/christine-mcphee/EmxkBQD5BGtlAwZmZQN1Zwp3BGt","URL":"https://www.smartbackgroundchecks.com/people/christine-mcphee/EmxkBQD5BGtlAwZmZQN1Zwp3BGt","name":"Christine Hunt Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/david-barnes/El0kZGR5Awp5Amx0AGt1ZGp1AGH0","URL":"https://www.smartbackgroundchecks.com/people/david-barnes/El0kZGR5Awp5Amx0AGt1ZGp1AGH0","name":"David Barnes"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/deonta-greenwood/EmZlAGxmAwp1ZwZ0ZQVkZGH1AGL","URL":"https://www.smartbackgroundchecks.com/people/deonta-greenwood/EmZlAGxmAwp1ZwZ0ZQVkZGH1AGL","name":"Deonta R Greenwood"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/edith-bradley/EmtlAQp3ZwDlAmRmAmVlBQVmZGL","URL":"https://www.smartbackgroundchecks.com/people/edith-bradley/EmtlAQp3ZwDlAmRmAmVlBQVmZGL","name":"Edith M Bradley"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/gwendolyn-garrett/EmV5ZQHlZGxjAwHkAmV5ZmplZD","URL":"https://www.smartbackgroundchecks.com/people/gwendolyn-garrett/EmV5ZQHlZGxjAwHkAmV5ZmplZD","name":"Gwendolyn Sue Garrett"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/heather-polson/El03BQx1AwHjZwtlZQD1BGp4ZwHj","URL":"https://www.smartbackgroundchecks.com/people/heather-polson/El03BQx1AwHjZwtlZQD1BGp4ZwHj","name":"Heather Cheyenne Polson"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/ishmell-bradley/EmR5BGp0AQD4Amp1ZGD1AwL3AGV","URL":"https://www.smartbackgroundchecks.com/people/ishmell-bradley/EmR5BGp0AQD4Amp1ZGD1AwL3AGV","name":"Ishmell C Bradley"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/joseph-keating/EmL4ZQHlZQH0ZQVmBGt1ZGVlBGN","URL":"https://www.smartbackgroundchecks.com/people/joseph-keating/EmL4ZQHlZQH0ZQVmBGt1ZGVlBGN","name":"Joseph Donald Keating"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/julie-king/El0lZwR3AGN4ZmRjZQH1ZGV3ZQZm","URL":"https://www.smartbackgroundchecks.com/people/julie-king/El0lZwR3AGN4ZmRjZQH1ZGV3ZQZm","name":"Julie E King"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/kate-mcphee/El0lAmV2AmZ2Awx2ZQZ2AGRmZGp0","URL":"https://www.smartbackgroundchecks.com/people/kate-mcphee/El0lAmV2AmZ2Awx2ZQZ2AGRmZGp0","name":"Kate Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/kathryn-keating/EmV5AQLlAmD4BGx0ZQZmZmZlZwx","URL":"https://www.smartbackgroundchecks.com/people/kathryn-keating/EmV5AQLlAmD4BGx0ZQZmZmZlZwx","name":"Kathryn M Keating"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/keia-greenwood/El02ZGp4ZwNjAQx0AQV1ZGtmAmH4","URL":"https://www.smartbackgroundchecks.com/people/keia-greenwood/El02ZGp4ZwNjAQx0AQV1ZGtmAmH4","name":"Keia Latrice Helen Greenwood"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/kelly-mcphee/EmH4ZwD2Zwt3ZQZ5AGLmZGV5ZQZ","URL":"https://www.smartbackgroundchecks.com/people/kelly-mcphee/EmH4ZwD2Zwt3ZQZ5AGLmZGV5ZQZ","name":"Kelly E Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/lauren-sherry/El00BQt1AQL3ZGZkAQR3BGxmZGZk","URL":"https://www.smartbackgroundchecks.com/people/lauren-sherry/El00BQt1AQL3ZGZkAQR3BGxmZGZk","name":"Lauren H Sherry"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/marcia-mcphee/El04AGV3BQx0Zwp0ZGNmAmL3AQD5","URL":"https://www.smartbackgroundchecks.com/people/marcia-mcphee/El04AGV3BQx0Zwp0ZGNmAmL3AQD5","name":"Marcia S Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/michael-garrett/El0lBQHmAGV3ZQpkZmp5AmD4AQN2","URL":"https://www.smartbackgroundchecks.com/people/michael-garrett/El0lBQHmAGV3ZQpkZmp5AmD4AQN2","name":"Michael Jerome Garrett"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/natasha-bradley/El03ZmZ2AGDkZmpkAmx2BGDjZGZ2","URL":"https://www.smartbackgroundchecks.com/people/natasha-bradley/El03ZmZ2AGDkZmpkAmx2BGDjZGZ2","name":"Natasha H Bradley"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/patrick-callahan/EmH0BQZ3BQHjBQZ2Zmx3BQpmZwp","URL":"https://www.smartbackgroundchecks.com/people/patrick-callahan/EmH0BQZ3BQHjBQZ2Zmx3BQpmZwp","name":"Patrick Callahan"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/richard-reese/El02AmZmZwp3AmN2AwN3ZmpkBGDl","URL":"https://www.smartbackgroundchecks.com/people/richard-reese/El02AmZmZwp3AmN2AwN3ZmpkBGDl","name":"Richard L Reese"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/robert-mcphee/El0kZmVmBQZ0AwRlBGtkAwp2ZQH5","URL":"https://www.smartbackgroundchecks.com/people/robert-mcphee/El0kZmVmBQZ0AwRlBGtkAwp2ZQH5","name":"Robert Lloyd Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/suellen-mcphee/El03BQVmZGxkBGpkBQV5AGR5ZGp2","URL":"https://www.smartbackgroundchecks.com/people/suellen-mcphee/El03BQVmZGxkBGpkBQV5AGR5ZGp2","name":"Suellen M Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/tiffany-bradley/Emt4BGV1ZwR5ZwN1AmDmZQVlBQt","URL":"https://www.smartbackgroundchecks.com/people/tiffany-bradley/Emt4BGV1ZwR5ZwN1AmDmZQVlBQt","name":"Tiffany Bradley"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/william-mcphee/El02Amp0ZGt5ZGH2AQRmBQN5AmRk","URL":"https://www.smartbackgroundchecks.com/people/william-mcphee/El02Amp0ZGt5ZGH2AQRmBQN5AmRk","name":"William C Mcphee"},{"@type":"Person","@id":"https://www.smartbackgroundchecks.com/people/william-mcphee/El03AmDkBGR1AmL5ZGp5AGVlZwp4","URL":"https://www.smartbackgroundchecks.com/people/william-mcphee/El03AmDkBGR1AmL5ZGp5AGVlZwp4","name":"William Charles Mcphee"}]}</script>
<script type="application/ld+json">{"@context":"http://schema.org","@type":"WebPage","name":"Who owns the phone number (561)932-4217","@id": "https://www.smartbackgroundchecks.com/phone/5619324217","url": "https://www.smartbackgroundchecks.com/phone/5619324217"}</script>
<script type="application/ld+json">{"@context":"http://schema.org","@type":"FAQPage","mainEntity":[{"@type":"Question","name":"Who currently owns the phone number (*************?","acceptedAnswer":{"@type":"Answer","text":"The current owner for (************* is <a href='https://www.smartbackgroundchecks.com/people/brenda-mccorvey/EmRjBGt0ZGH1ZQN1ZwHlBQL4AmH'>Brenda Mccorvey</a> who lives in Royal Palm Beach, FL."}},{"@type":"Question","name":"Is (************* a wireless or landline phone?","acceptedAnswer":{"@type":"Answer","text":"(************* is a Wireless phone."}},{"@type":"Question","name":"What carrier or phone company provides service for (*************?","acceptedAnswer":{"@type":"Answer","text":"MetroPCS Inc is the current provider for (************* in Royal Palm Beach, FL."}},{"@type":"Question","name":"Is the phone (************* active or disconnected?","acceptedAnswer":{"@type":"Answer","text":"(************* appears to be currently connected and working."}},{"@type":"Question","name":"Who else has used the phone (************* in the past?","acceptedAnswer":{"@type":"Answer","text":"Previous owners of (************* include <a href='https://www.smartbackgroundchecks.com/people/john-mcphee/El0lZQL3AwtlZmxlZGt2AmV4ZwV4'>John Mcphee</a>."}}]}</script>
<script type="application/ld+json">{"@graph":[{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com","url":"https://www.smartbackgroundchecks.com","name":"Home Page"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com","url":"https://www.smartbackgroundchecks.com","name":"People Search"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/address","url":"https://www.smartbackgroundchecks.com/address","name":"Address Lookup"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/phone","url":"https://www.smartbackgroundchecks.com/phone","name":"Reverse Phone Search"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/terms","url":"https://www.smartbackgroundchecks.com/terms","name":"Terms and Conditions"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/privacy","url":"https://www.smartbackgroundchecks.com/privacy","name":"Privacy Policy"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/contact","url":"https://www.smartbackgroundchecks.com/contact","name":"Contact"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/a","url":"https://www.smartbackgroundchecks.com/names/a","name":"Name directory for last name starting in a"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/b","url":"https://www.smartbackgroundchecks.com/names/b","name":"Name directory for last name starting in b"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/c","url":"https://www.smartbackgroundchecks.com/names/c","name":"Name directory for last name starting in c"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/d","url":"https://www.smartbackgroundchecks.com/names/d","name":"Name directory for last name starting in d"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/e","url":"https://www.smartbackgroundchecks.com/names/e","name":"Name directory for last name starting in e"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/f","url":"https://www.smartbackgroundchecks.com/names/f","name":"Name directory for last name starting in f"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/g","url":"https://www.smartbackgroundchecks.com/names/g","name":"Name directory for last name starting in g"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/h","url":"https://www.smartbackgroundchecks.com/names/h","name":"Name directory for last name starting in h"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/i","url":"https://www.smartbackgroundchecks.com/names/i","name":"Name directory for last name starting in i"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/j","url":"https://www.smartbackgroundchecks.com/names/j","name":"Name directory for last name starting in j"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/k","url":"https://www.smartbackgroundchecks.com/names/k","name":"Name directory for last name starting in k"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/l","url":"https://www.smartbackgroundchecks.com/names/l","name":"Name directory for last name starting in l"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/m","url":"https://www.smartbackgroundchecks.com/names/m","name":"Name directory for last name starting in m"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/n","url":"https://www.smartbackgroundchecks.com/names/n","name":"Name directory for last name starting in n"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/o","url":"https://www.smartbackgroundchecks.com/names/o","name":"Name directory for last name starting in o"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/p","url":"https://www.smartbackgroundchecks.com/names/p","name":"Name directory for last name starting in p"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/q","url":"https://www.smartbackgroundchecks.com/names/q","name":"Name directory for last name starting in q"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/r","url":"https://www.smartbackgroundchecks.com/names/r","name":"Name directory for last name starting in r"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/s","url":"https://www.smartbackgroundchecks.com/names/s","name":"Name directory for last name starting in s"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/t","url":"https://www.smartbackgroundchecks.com/names/t","name":"Name directory for last name starting in t"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/u","url":"https://www.smartbackgroundchecks.com/names/u","name":"Name directory for last name starting in u"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/v","url":"https://www.smartbackgroundchecks.com/names/v","name":"Name directory for last name starting in v"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/w","url":"https://www.smartbackgroundchecks.com/names/w","name":"Name directory for last name starting in w"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/x","url":"https://www.smartbackgroundchecks.com/names/x","name":"Name directory for last name starting in x"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/y","url":"https://www.smartbackgroundchecks.com/names/y","name":"Name directory for last name starting in y"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names/z","url":"https://www.smartbackgroundchecks.com/names/z","name":"Name directory for last name starting in z"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/names","url":"https://www.smartbackgroundchecks.com/names","name":"Name Directory"},{"@context":"https://schema.org/","@type":"SiteNavigationElement","@id":"https://www.smartbackgroundchecks.com/phones","url":"https://www.smartbackgroundchecks.com/phones","name":"Phone Directory"}]}</script>
<div style="height:80px"></div>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'949fa8a13a91e66f',t:'MTc0ODk1ODYxOC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;"></iframe><script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;949fa8a13a91e66f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.5.0&quot;,&quot;token&quot;:&quot;625d7a205e064738ab8bbcfb7947e7fa&quot;}" crossorigin="anonymous"></script>

<div class="bsa_fixed-leaderboard" data-hidden-by="automatic-enable-fixed-leaderboard" style=""><div id="bsa-zone_1743502348758-4_123456" data-hidden-by="automatic-enable-fixed-leaderboard" style="" data-google-query-id="CMaI74Cz1Y0DFTiupgQdf6wy8A"><div id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_Fixedfooter_ROS_0__container__" style="border: 0pt none; margin: auto; text-align: center; width: 728px; height: 90px;"><iframe frameborder="0" src="https://e0d235542a55b49e6f47dae4abe4b5a2.safeframe.googlesyndication.com/safeframe/1-0-45/html/container.html" id="google_ads_iframe_/22960212090,413673328/Smartbackgroundchecks_S2S_Fixedfooter_ROS_0" title="3rd party ad content" name="1-0-45;55706;<!doctype html><html><head><script>var jscVersion = 'r20250602';</script><script>var google_casm=[];</script></head><body leftMargin=&quot;0&quot; topMargin=&quot;0&quot; marginwidth=&quot;0&quot; marginheight=&quot;0&quot;><script>window.dicnf = {};</script><script data-jc=&quot;42&quot; data-jc-version=&quot;r20250602&quot; data-jc-flags=&quot;[&amp;quot;x%278446&amp;#39;9efotm(&amp;amp;20067;&amp;gt;8&amp;amp;&amp;gt;`dopb/%&amp;lt;1732261!=|vqc)!7201061?&amp;#39;9efotm(&amp;amp;20723;&amp;gt;:&amp;amp;&amp;gt;`dopb/%&amp;lt;1245;05!=nehu`/!361:&amp;lt;320!9sqrm(&amp;amp;2057?61&amp;lt;&amp;amp;&amp;gt;`dopb~&amp;quot;]&quot;>(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var r=this||self;function aa(a){r.setTimeout(()=>{throw a;},0)};function v(a){v[&quot; &quot;](a);return a}v[&quot; &quot;]=function(){};var ba={},w=null;let ca=void 0;function A(a,b=!1){return b&amp;&amp;Symbol.for&amp;&amp;a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var B=A(),E=A(&quot;m_m&quot;,!0);const F=A(&quot;jas&quot;,!0);var da;const ea=[];ea[F]=7;da=Object.freeze(ea);var G={};function H(a,b){return b===void 0?a.g!==I&amp;&amp;!!(2&amp;(a.j[F]|0)):!!(2&amp;b)&amp;&amp;a.g!==I}const I={};const fa=BigInt(Number.MIN_SAFE_INTEGER),ha=BigInt(Number.MAX_SAFE_INTEGER);function ia(a){if(typeof a!==&quot;boolean&quot;){var b=typeof a;throw Error(`Expected boolean but got ${b!=&quot;object&quot;?b:a?Array.isArray(a)?&quot;array&quot;:b:&quot;null&quot;}: ${a}`);}return a};function ka(a){return a};function J(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let l,h=**********,m=!1;const k=!!(b&amp;64),n=k?b&amp;128?0:-1:void 0;b&amp;1||(l=g&amp;&amp;a[g-1],l!=null&amp;&amp;typeof l===&quot;object&quot;&amp;&amp;l.constructor===Object?(g--,h=g):l=void 0,!k||b&amp;128||e||(m=!0,h=(la??ka)(h-n,n,a,l)+n));b=void 0;for(e=0;e<g;e++){let p=a[e];if(p!=null&amp;&amp;(p=c(p,d))!=null)if(k&amp;&amp;e>=h){const q=e-n;(b??(b={}))[q]=p}else f[e]=p}if(l)for(let p in l){a=l[p];if(a==null||(a=c(a,d))==null)continue;g=+p;let q;k&amp;&amp;!Number.isNaN(g)&amp;&amp;(q=g+n)<h?f[q]=a: (b??(b={}))[p]=a}b&amp;&amp;(m?f.push(b):f[h]=b);return f}function ma(a){switch(typeof a){case &quot;number&quot;:return Number.isFinite(a)?a:&quot;&quot;+a;case &quot;bigint&quot;:return a>=fa&amp;&amp;a<=ha?Number(a):&quot;&quot;+a;case &quot;boolean&quot;:return a?1:0;case &quot;object&quot;:if(Array.isArray(a)){const b=a[F]|0;return a.length===0&amp;&amp;b&amp;1?void 0:J(a,b,ma)}if(a!=null&amp;&amp;a[E]===G)return K(a);return}return a}let la;function K(a){a=a.j;return J(a,a[F]|0,ma)};function na(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error(&quot;narr&quot;);b=a[F]|0;2048&amp;b&amp;&amp;!(2&amp;b)&amp;&amp;oa();if(b&amp;256)throw Error(&quot;farr&quot;);if(b&amp;64)return b&amp;2048||(a[F]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&amp;&amp;typeof d===&quot;object&quot;&amp;&amp;d.constructor===Object){const f=b&amp;128?0:-1;e-=f;if(e>=1024)throw Error(&quot;pvtlmt&quot;);for(const g in d){const l=+g;if(l<e)c[l+f]=d[g],delete d[g];else break}b=b&amp;-8380417|(e&amp;1023)<<13}}}a[F]=b|2112;return a} function oa(){if(B!=null){var a=ca??(ca={});var b=a[B]||0;b>=5||(a[B]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity=&quot;incident&quot;,aa(a))}};function pa(a,b){if(typeof a!==&quot;object&quot;)return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&amp;&amp;c&amp;1?a=void 0:c&amp;2||(!b||4096&amp;c||16&amp;c?a=L(a,c,!1,b&amp;&amp;!(c&amp;16)):(a[F]|=34,c&amp;4&amp;&amp;Object.freeze(a)));return a}if(a!=null&amp;&amp;a[E]===G){c=a.j;const d=c[F]|0;H(a,d)||(d&amp;2?b=!0:!(d&amp;32)||d&amp;4096?b=!1:(c[F]=d|2,a.g=I,b=!0),b?(a=new a.constructor(c),a.i=I):a=L(c,d));return a}}function L(a,b,c,d){d??(d=!!(34&amp;b));a=J(a,b,pa,d);d=32;c&amp;&amp;(d|=2);b=b&amp;8380609|d;a[F]=b;return a} function qa(a){if(a.g===I){var b=a.j;b=L(b,b[F]|0);b[F]|=2048;a.j=b;a.g=void 0;a.i=void 0;b=!0}else b=!1;if(!b&amp;&amp;H(a,a.j[F]|0))throw Error();};function ra(a,b,c){qa(a);const d=a.j;sa(d,d[F]|0,b,c);return a}function sa(a,b,c,d){const e=c+-1;var f=a.length-1;if(f>=0&amp;&amp;e>=f){const g=a[f];if(g!=null&amp;&amp;typeof g===&quot;object&quot;&amp;&amp;g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&amp;&amp;(f=(b??(b=a[F]|0))>>13&amp;1023||536870912,c>=f?d!=null&amp;&amp;(a[f+-1]={[c]:d}):a[e]=d);return b}function M(a,b,c){if(c!=null&amp;&amp;typeof c!==&quot;string&quot;)throw Error();return ra(a,b,c)};var N=class{constructor(a){this.j=na(a)}toJSON(){return K(this)}};N.prototype[E]=G;N.prototype.toString=function(){return this.j.toString()};var O=class{constructor(a,b=!1){this.key=a;this.defaultValue=b;this.valueType=&quot;boolean&quot;}};var ta=new O(&quot;45368259&quot;),ua=new O(&quot;45357156&quot;,!0),va=new O(&quot;45350890&quot;),wa=new O(&quot;45414892&quot;),xa=new O(&quot;45620832&quot;),ya=new O(&quot;45648564&quot;);const za=RegExp(&quot;ad\\.doubleclick\\.net/(ddm/trackimp|pcs/view)&quot;);var P=(a,b)=>a.substring(a.length-7)==&quot;&amp;adurl=&quot;?a.substring(0,a.length-7)+b+&quot;&amp;adurl=&quot;:a+b;function Aa(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Ba(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&amp;&amp;b(a[c],c,a)}function Ca(a=document){return a.createElement(&quot;img&quot;)};function R(a,b,c){typeof a.addEventListener===&quot;function&quot;&amp;&amp;a.addEventListener(b,c,!1)}function Da(a,b,c){typeof a.removeEventListener===&quot;function&quot;&amp;&amp;a.removeEventListener(b,c,!1)};function Ea(a,b=null){Ja(a,b)}function Ja(a,b){r.google_image_requests||(r.google_image_requests=[]);const c=Ca(r.document);if(b){const d=e=>{b&amp;&amp;b(e);Da(c,&quot;load&quot;,d);Da(c,&quot;error&quot;,d)};R(c,&quot;load&quot;,d);R(c,&quot;error&quot;,d)}c.src=a;r.google_image_requests.push(c)};let Ka=0;function La(a,b=null){return b&amp;&amp;b.getAttribute(&quot;data-jc&quot;)===String(a)?b:document.querySelector(`[${&quot;data-jc&quot;}=&quot;${a}&quot;]`)};function S(a){Ma||(Ma=new Na);const b=Ma.g[a.key];if(a.valueType===&quot;proto&quot;){try{const c=JSON.parse(b);if(Array.isArray(c))return c}catch(c){}return a.defaultValue}return typeof b===typeof a.defaultValue?b:a.defaultValue}var Oa=class{constructor(){this.g={}}};var Na=class extends Oa{constructor(){super();var a=La(Ka,document.currentScript);a=a&amp;&amp;a.getAttribute(&quot;data-jc-flags&quot;)||&quot;&quot;;try{const b=JSON.parse(a)[0];a=&quot;&quot;;for(let c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^&quot;\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003&quot;.charCodeAt(c%10));this.g=JSON.parse(a)}catch(b){}}},Ma;var Pa=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||&quot;&quot;;this.id=b.id||&quot;jserror&quot;}};function Qa(a){let b=a.toString();a.name&amp;&amp;b.indexOf(a.name)==-1&amp;&amp;(b+=&quot;: &quot;+a.name);a.message&amp;&amp;b.indexOf(a.message)==-1&amp;&amp;(b+=&quot;: &quot;+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&amp;&amp;(a=c+&quot;\n&quot;+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp(&quot;((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2&quot;),&quot;$1&quot;);b=a.replace(RegExp(&quot;\n *&quot;,&quot;g&quot;),&quot;\n&quot;);break a}catch(d){b=c;break a}b=void 0}return b};const Ra=RegExp(&quot;^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)&quot;);var Sa=class{constructor(a,b){this.g=a;this.i=b}},Ta=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let T=null;function Ua(){const a=r.performance;return a&amp;&amp;a.now&amp;&amp;a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Va(){const a=r.performance;return a&amp;&amp;a.now?a.now():null};var Wa=class{constructor(a,b){var c=Va()||Ua();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const U=r.performance,Xa=!!(U&amp;&amp;U.mark&amp;&amp;U.measure&amp;&amp;U.clearMarks),V=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=Xa){var b;a=window;if(T===null){T=&quot;&quot;;try{let c=&quot;&quot;;try{c=a.top.location.hash}catch(d){c=a.location.hash}c&amp;&amp;(T=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:&quot;&quot;)}catch(c){}}b=T;a=!!b.indexOf&amp;&amp;b.indexOf(&quot;1337&quot;)>=0}return a});function Ya(a){a&amp;&amp;U&amp;&amp;V()&amp;&amp;(U.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),U.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function W(a,b){const c={};c[a]=b;return[c]}function Za(a,b,c,d,e){const f=[];Ba(a,(g,l)=>{(g=$a(g,b,c,d,e))&amp;&amp;f.push(`${l}=${g}`)});return f.join(b)} function $a(a,b,c,d,e){if(a==null)return&quot;&quot;;b=b||&quot;&amp;&quot;;c=c||&quot;,$&quot;;typeof c===&quot;string&quot;&amp;&amp;(c=c.split(&quot;&quot;));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push($a(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a===&quot;object&quot;)return e||(e=0),e<2?encodeURIComponent(Za(a,b,c,d,e+1)):&quot;...&quot;;return encodeURIComponent(String(a))}function ab(a){let b=1;for(const c in a.i)c.length>b&amp;&amp;(b=c.length);return 3997-b-a.l.length-1} function bb(a,b){let c=&quot;https://pagead2.googlesyndication.com&quot;+b,d=ab(a)-b.length;if(d<0)return&quot;&quot;;a.g.sort((f,g)=>f-g);b=null;let e=&quot;&quot;;for(let f=0;f<a.g.length;f++){const g=a.g[f],l=a.i[g];for(let h=0;h<l.length;h++){if(!d){b=b==null?g:b;break}let m=Za(l[h],a.l,&quot;,$&quot;);if(m){m=e+m;if(d>=m.length){d-=m.length;c+=m;e=a.l;break}b=b==null?g:b}}}a=&quot;&quot;;b!=null&amp;&amp;(a=`${e}${&quot;trn&quot;}=${b}`);return c+a}var cb=class{constructor(){this.l=&quot;&amp;&quot;;this.i={};this.m=0;this.g=[]}};var db=RegExp(&quot;^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$&quot;);function eb(a,b,c,d){const e=c.length;for(;(b=a.indexOf(c,b))>=0&amp;&amp;b<d;){var f=a.charCodeAt(b-1);if(f==38||f==63)if(f=a.charCodeAt(b+e),!f||f==61||f==38||f==35)return b;b+=e+1}return-1}var fb=/#|$/; function gb(a){const b=a.search(fb);let c=eb(a,0,&quot;ase&quot;,b);if(c<0)return null;let d=a.indexOf(&quot;&amp;&quot;,c);if(d<0||d>b)d=b;return decodeURIComponent(a.slice(c+4,d!==-1?d:0).replace(/\+/g,&quot; &quot;))}var hb=/[?&amp;]($|#)/; function ib(a,b){var c=a.search(fb),d=0,e;const f=[];for(;(e=eb(a,d,&quot;nis&quot;,c))>=0;)f.push(a.substring(d,e)),d=Math.min(a.indexOf(&quot;&amp;&quot;,e)+1||c,c);f.push(a.slice(d));a=f.join(&quot;&quot;).replace(hb,&quot;$1&quot;);(b=&quot;nis&quot;+(b!=null?&quot;=&quot;+encodeURIComponent(String(b)):&quot;&quot;))?(c=a.indexOf(&quot;#&quot;),c<0&amp;&amp;(c=a.length),d=a.indexOf(&quot;?&quot;),d<0||d>c?(d=c,e=&quot;&quot;):e=a.substring(d+1,c),a=[a.slice(0,d),e,a.slice(c)],c=a[1],a[1]=b?c?c+&quot;&amp;&quot;+b:b:c,b=a[0]+(a[1]?&quot;?&quot;+a[1]:&quot;&quot;)+a[2]):b=a;return b};function jb(a,b,c,d){let e,f;try{a.g&amp;&amp;a.g.g?(f=a.g.start(b.toString(),3),e=c(),a.g.end(f)):e=c()}catch(g){c=!0;try{Ya(f),c=a.v(b,new Pa(g,{message:Qa(g)}),void 0,d)}catch(l){a.m(217,l)}if(c)window.console?.error?.(g);else throw g;}return e}function kb(a,b,c,d){var e=X;return(...f)=>jb(e,a,()=>b.apply(c,f),d)} var mb=class{constructor(a=null){this.u=Y;this.g=a;this.i=null;this.l=!1;this.v=this.m}m(a,b,c,d,e){e=e||&quot;jserror&quot;;let f=void 0;try{const C=new cb;var g=C;g.g.push(1);g.i[1]=W(&quot;context&quot;,a);b.error&amp;&amp;b.meta&amp;&amp;b.id||(b=new Pa(b,{message:Qa(b)}));g=b;if(g.msg){b=C;var l=g.msg.substring(0,512);b.g.push(2);b.i[2]=W(&quot;msg&quot;,l)}var h=g.meta||{};l=h;if(this.i)try{this.i(l)}catch(z){}if(d)try{d(l)}catch(z){}d=C;h=[h];d.g.push(3);d.i[3]=h;var m;if(!(m=q)){d=r;h=[];let z;l=null;do{var k=d;try{var n;if(n=!!k&amp;&amp;k.location.href!= null)b:{try{v(k.foo);n=!0;break b}catch(x){}n=!1}var p=n}catch{p=!1}p?(z=k.location.href,l=k.document&amp;&amp;k.document.referrer||null):(z=l,l=null);h.push(new Ta(z||&quot;&quot;));try{d=k.parent}catch(x){d=null}}while(d&amp;&amp;k!==d);for(let x=0,Fa=h.length-1;x<=Fa;++x)h[x].depth=Fa-x;k=r;if(k.location&amp;&amp;k.location.ancestorOrigins&amp;&amp;k.location.ancestorOrigins.length===h.length-1)for(p=1;p<h.length;++p){const x=h[p];x.url||(x.url=k.location.ancestorOrigins[p-1]||&quot;&quot;,x.g=!0)}m=h}var q=m;let Q=new Ta(r.location.href,!1);m= null;const ja=q.length-1;for(k=ja;k>=0;--k){var t=q[k];!m&amp;&amp;Ra.test(t.url)&amp;&amp;(m=t);if(t.url&amp;&amp;!t.g){Q=t;break}}t=null;const pb=q.length&amp;&amp;q[ja].url;Q.depth!==0&amp;&amp;pb&amp;&amp;(t=q[ja]);f=new Sa(Q,t);if(f.i){q=C;var u=f.i.url||&quot;&quot;;q.g.push(4);q.i[4]=W(&quot;top&quot;,u)}var D={url:f.g.url||&quot;&quot;};if(f.g.url){const z=f.g.url.match(db);var y=z[1],Ga=z[3],Ha=z[4];u=&quot;&quot;;y&amp;&amp;(u+=y+&quot;:&quot;);Ga&amp;&amp;(u+=&quot;//&quot;,u+=Ga,Ha&amp;&amp;(u+=&quot;:&quot;+Ha));var Ia=u}else Ia=&quot;&quot;;y=C;D=[D,{url:Ia}];y.g.push(5);y.i[5]=D;lb(this.u,e,C,this.l,c)}catch(C){try{lb(this.u,e,{context:&quot;ecmserr&quot;, rctx:a,msg:Qa(C),url:f?.g.url??&quot;&quot;},this.l,c)}catch(Q){}}return!0}};class nb{};function lb(a,b,c,d=!1,e,f){if((d?a.g:Math.random())<(e||.01))try{let g;c instanceof cb?g=c:(g=new cb,Ba(c,(h,m)=>{var k=g;const n=k.m++;h=W(m,h);k.g.push(n);k.i[n]=h}));const l=bb(g,&quot;/pagead/gen_204?id=&quot;+b+&quot;&amp;&quot;);l&amp;&amp;(typeof f!==&quot;undefined&quot;?Ea(l,f):Ea(l))}catch(g){}}function ob(){var a=Y,b=window.google_srt;b>=0&amp;&amp;b<=1&amp;&amp;(a.g=b)}var qb=class{constructor(){this.g=Math.random()}};let Y,X; const Z=new class{constructor(a,b){this.i=[];this.l=b||r;let c=null;b&amp;&amp;(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.i=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=V()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new Wa(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;U&amp;&amp;V()&amp;&amp;U.mark(b);return a}end(a){if(this.g&amp;&amp;typeof a.value===&quot;number&quot;){a.duration=(Va()||Ua())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;U&amp;&amp;V()&amp;&amp;U.mark(b);!this.g||this.i.length> 2048||this.i.push(a)}}}(1,window);function rb(){window.google_measure_js_timing||(Z.g=!1,Z.i!==Z.l.google_js_reporting_queue&amp;&amp;(V()&amp;&amp;Array.prototype.forEach.call(Z.i,Ya,void 0),Z.i.length=0))} (function(a){Y=a??new qb;typeof window.google_srt!==&quot;number&quot;&amp;&amp;(window.google_srt=Math.random());ob();X=new mb(Z);X.i=b=>{var c=Ka;c!==0&amp;&amp;(b.jc=String(c),c=(c=La(c,document.currentScript))&amp;&amp;c.getAttribute(&quot;data-jc-version&quot;)||&quot;unknown&quot;,b.shv=c)};X.l=!0;window.document.readyState===&quot;complete&quot;?rb():Z.g&amp;&amp;R(window,&quot;load&quot;,()=>{rb()})})();function sb(a,b,c,d){return kb(a,b,c,d)} function tb(a,b,c,d){var e=nb;var f=&quot;o&quot;;e.o&amp;&amp;e.hasOwnProperty(f)||(f=new e,e.o=f);e=[];!b.eid&amp;&amp;e.length&amp;&amp;(b.eid=e.toString());lb(Y,a,b,!0,c,d)};function ub(a){let b;a.visibilityState?b=&quot;visibilitychange&quot;:a.mozVisibilityState?b=&quot;mozvisibilitychange&quot;:a.webkitVisibilityState&amp;&amp;(b=&quot;webkitvisibilitychange&quot;);return b};function vb(){const a=window;if(a.gmaSdk||a.webkit?.messageHandlers?.getGmaViewSignals)return a;try{const b=window.parent;if(b.gmaSdk||b.webkit?.messageHandlers?.getGmaViewSignals)return b}catch(b){}return null} function wb(a,b={},c=()=>{},d=()=>{},e=200,f,g){const l=String(Math.floor(Aa()*2147483647));let h=0;const m=k=>{try{const n=typeof k.data===&quot;object&quot;?k.data:JSON.parse(k.data);l===n.paw_id&amp;&amp;(window.clearTimeout(h),window.removeEventListener(&quot;message&quot;,m),n.signal?c(n.signal):n.error&amp;&amp;d(n.error))}catch(n){g(&quot;paw_sigs&quot;,{msg:&quot;postmessageError&quot;,err:n instanceof Error?n.message:&quot;nonError&quot;,data:k.data==null?&quot;null&quot;:k.data.length>500?k.data.substring(0,500):k.data})}};window.addEventListener(&quot;message&quot;,k=>{f(903, ()=>{m(k)})()});a.postMessage({paw_id:l,...b});h=window.setTimeout(()=>{window.removeEventListener(&quot;message&quot;,m);d(&quot;PAW GMA postmessage timed out.&quot;)},e)};function xb(a=document){return!!a.featurePolicy?.allowedFeatures().includes(&quot;attribution-reporting&quot;)};var yb=class extends N{};function zb(a,b){return M(a,2,b)}function Ab(a,b){return M(a,3,b)}function Bb(a,b){return M(a,4,b)}function Cb(a,b){return M(a,5,b)}function Db(a,b){return M(a,9,b)} function Eb(a,b){{var c=b;qa(a);const k=a.j;b=k[F]|0;if(c==null)sa(k,b,10);else{var d=c===da?7:c[F]|0,e=d,f=!!(2&amp;d)&amp;&amp;!!(4&amp;d)||!!(256&amp;d),g=f||Object.isFrozen(c),l=!0,h=!0;for(let n=0;n<c.length;n++){var m=c[n];f||(m=H(m),l&amp;&amp;(l=!m),h&amp;&amp;(h=m))}f||(d=l?13:5,d=h?d&amp;-4097:d|4096);g&amp;&amp;d===e||(c=[...c],e=0,d=2&amp;b?d|2:d&amp;-3,d&amp;=-273);d!==e&amp;&amp;(c[F]=d);b=sa(k,b,10,c);2&amp;d||!(4096&amp;d||16&amp;d)||(c=k,b===void 0&amp;&amp;(b=c[F]|0),b&amp;32&amp;&amp;!(b&amp;4096)&amp;&amp;(c[F]=b|4096))}}return a}function Fb(a,b){return ra(a,11,b==null?b:ia(b))} function Gb(a,b){return M(a,1,b)}function Hb(a,b){return ra(a,7,b==null?b:ia(b))}var Ib=class extends N{};const Jb=&quot;platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64&quot;.split(&quot; &quot;);function Kb(){var a=window;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!==&quot;function&quot;)return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Jb).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} function Lb(a){return Fb(Eb(Cb(zb(Gb(Bb(Hb(Db(Ab(new Ib,a.architecture||&quot;&quot;),a.bitness||&quot;&quot;),a.mobile||!1),a.model||&quot;&quot;),a.platform||&quot;&quot;),a.platformVersion||&quot;&quot;),a.uaFullVersion||&quot;&quot;),a.fullVersionList?.map(b=>{var c=new yb;c=M(c,1,b.brand);return M(c,2,b.version)})||[]),a.wow64||!1)}function Mb(){return Kb()?.then(a=>Lb(a))??null};class Nb{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};window.viewReq=[];function Ob(a,b){b?(b=Ca(),b.src=a.replace(&quot;&amp;amp;&quot;,&quot;&amp;&quot;),b.attributionSrc=&quot;&quot;,window.viewReq.push(b)):(b=new Image,b.src=a.replace(&quot;&amp;amp;&quot;,&quot;&amp;&quot;),window.viewReq.push(b))} function Pb(a,b){const c={keepalive:!0,credentials:&quot;include&quot;,redirect:&quot;follow&quot;,method:&quot;get&quot;,mode:&quot;no-cors&quot;};b&amp;&amp;(c.mode=&quot;cors&quot;,&quot;setAttributionReporting&quot;in XMLHttpRequest.prototype?c.attributionReporting={eventSourceEligible:&quot;true&quot;,triggerEligible:&quot;false&quot;}:c.headers={&quot;Attribution-Reporting-Eligible&quot;:&quot;event-source&quot;});fetch(a,c).catch(()=>{Ob(a,b)})}function Qb(a,b){window.fetch?Pb(a,b):Ob(a,b)} function Rb(){const a=r.document;return new Promise(b=>{const c=ub(a);if(c){var d=()=>{(a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,&quot;&quot;:0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||&quot;&quot;]??0)!==3&amp;&amp;(Da(a,c,d),b())};R(a,c,d)}})}Ka=42; window.vu=a=>{var b=S(ua)||S(wa);const c=vb();if(b&amp;&amp;c?.gmaSdk?.getViewSignals){var d=c.gmaSdk.getViewSignals();d&amp;&amp;!S(wa)&amp;&amp;(a=P(a,&quot;&amp;ms=&quot;+d))}S(ta)&amp;&amp;&quot;__google_lidar_radf_&quot;in window&amp;&amp;(a=P(a,&quot;&amp;avradf=1&quot;));const e=[];d=()=>{const l=new Nb;e.push(l.promise);return l.resolve};if(S(ya)){var f=Rb();if(f!=null){const l=d();f.then(()=>{a=P(a,&quot;&amp;sbtr=1&quot;);l()})}}S(xa)&amp;&amp;(a=P(a,&quot;&amp;sbtr=1&quot;));if(S(va)&amp;&amp;(f=Mb(),f!=null)){const l=d();f.then(h=>{var m=JSON.stringify(K(h));h=[];var k=0;for(var n=0;n<m.length;n++){var p= m.charCodeAt(n);p>255&amp;&amp;(h[k++]=p&amp;255,p>>=8);h[k++]=p}m=3;m===void 0&amp;&amp;(m=0);if(!w)for(w={},k=&quot;ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789&quot;.split(&quot;&quot;),n=[&quot;+/=&quot;,&quot;+/&quot;,&quot;-_=&quot;,&quot;-_.&quot;,&quot;-_&quot;],p=0;p<5;p++){var q=k.concat(n[p].split(&quot;&quot;));ba[p]=q;for(var t=0;t<q.length;t++){var u=q[t];w[u]===void 0&amp;&amp;(w[u]=t)}}m=ba[m];k=Array(Math.floor(h.length/3));n=m[64]||&quot;&quot;;for(p=q=0;q<h.length-2;q+=3){var D=h[q],y=h[q+1];u=h[q+2];t=m[D>>2];D=m[(D&amp;3)<<4|y>>4];y=m[(y&amp;15)<<2|u>>6];u=m[u&amp;63];k[p++]=t+D+y+u}t= 0;u=n;switch(h.length-q){case 2:t=h[q+1],u=m[(t&amp;15)<<2]||n;case 1:h=h[q],k[p]=m[h>>2]+m[(h&amp;3)<<4|t>>4]+u+n}h=k.join(&quot;&quot;);h.length>0&amp;&amp;(a=P(a,&quot;&amp;uach=&quot;+h));l()})}if(b&amp;&amp;c?.webkit?.messageHandlers?.getGmaViewSignals){const l=d();wb(c.webkit.messageHandlers.getGmaViewSignals,{},h=>{S(wa)||(a=P(a,&quot;&amp;&quot;+h));l()},()=>{l()},200,sb,tb)}const g=gb(a)===(2).toString()||za.test(a);g&amp;&amp;(b=xb(window.document)?6:5,a=ib(a,b));e.length>0?Promise.all(e).then(()=>{Qb(a,g)}):Qb(a,g)};}).call(this);</script><script>vu(&quot;https://securepubads.g.doubleclick.net/pagead/adview?ai\x3dCFEtEDv4-aMbJKrjcmtUP_9jKgQ_xi56QXPKJ5eG5BcCNtwEQASAAYMm2iYfMo8AXggEXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjfIAQngAgCoAwHIAwKqBLYCT9AS-UkRWfXd1lGCyR39LGwK7yybxkrizr3WBJDnN28za5oGaZ1FY3w6E23FPrm7FsCjFRXOqLYaaP1ypQAZyL8nPO1y-J0AQrlW7KJSocuDKSWPp1V44EBfEmHZ_fTsomOC8QQUguUXnG6v77oCckaJIOL1gi723sEzs3U83MoCK2hxenAdO5O2-rsAJzn3G9YK76JMUrUWdwLC34V_JsqTsVhQQNPlmRZ-0CY2Zo_4UGn7QkN9GGqH6dcpNQLY7ynFTLRmPK0qIJT8U8cYK_WeM68ZFnLpdu0kWjpvTON8wEIQBWs8LachftnSkmfUip34i5iPuevW0uxqKMCO4kuVaZfnkJQBbRLpqxt6EoG5HgfSNuUwa8ZpOPw-0-m2-JnVaHmyl3_SEhNJ4UYLotgPjAS94eAEAYAG_rXW8qGq9dLoAaAGIagHpr4bqAeW2BuoB6qbsQKoB_-esQKoB9-fsQKoB62-sQLYBwDSCCYIgGEQATICigI6DYBAgMCAgICAqIACoANIvf3BOljgtOyAs9WNA4AKA_oLAggBgAwBqg0CVVPiDRMI14TtgLPVjQMVOK6mBB1_rDLw6g0TCLr_7YCz1Y0DFTiupgQdf6wy8NAVAYAXAbIXKwobEhRwdWItOTk2MTgxNDgyMzkzMDk2Nxj__ZUBGAsqCjQxNjAzNDU3ODU\x26sigh\x3dc8FhB6Yn_yE\x26uach_m\x3d%5BUACH%5D\x26cid\x3dCAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bRgB\x26tpd\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&quot;)</script><div class=&quot;GoogleActiveViewInnerContainer&quot;id=&quot;avic_CMaI74Cz1Y0DFTiupgQdf6wy8A&quot;style=&quot;left:0px;top:0px;width:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;&quot;></div><div style=&quot;display:inline&quot;class=&quot;GoogleActiveViewElement&quot;data-google-av-cxn=&quot;https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjsu3Rtl7sBJSrfQxUFJZz44T0EfEfHc3Dxy1RbJ5Yf6jCtVADqE8og96yhheNZHua2cDweFpADIZwIUS2loCwT1hbE_Bxw2TLvvigPS4_P5Q0Ys-bAX3fEPSQ2oYKwjBL04e8NP3OjealXS1msfSTPWs-b4VntgZCmrGwr-nBZk&amp;amp;sig=Cg0ArKJSzP6jK2zjnXHVEAE&quot;data-google-av-adk=&quot;1969639227&quot;data-google-av-metadata=&quot;la=0&amp;amp;xdi=0&amp;amp;&quot;data-google-av-ufs-integrator-metadata=&quot;CpkBCkFtb2RlbF9wZXJzb25fY291bnRyeV9jb2RlX1VTX3BlcnNvbl9yZWdpb25fY29kZV80MzRmNWYzNzM1MzEuanNvbhIaQ01hSTc0Q3oxWTBERlRpdXBnUWRmNnd5OEEYASIeCJgWEJKlARjV9T0g1fU9KAIwAjgBXc3MzD1gr6AMKMz38un4_____wEwzPfyaTgBQAFIAFABEpECCoQCaHR0cHM6Ly9wYWdlYWQyLmdvb2dsZXN5bmRpY2F0aW9uLmNvbS9wY3MvYWN0aXZldmlldz94YWk9QUtBT2pzdTNSdGw3c0JKU3JmUXhVRkpaejQ0VDBFZkVmSGMzRHh5MVJiSjVZZjZqQ3RWQURxRThvZzk2eWhoZU5aSHVhMmNEd2VGcEFESVp3SVVTMmxvQ3dUMWhiRV9CeHcyVEx2dmlnUFM0X1A1UTBZcy1iQVgzZkVQU1Eyb1lLd2pCTDA0ZThOUDNPamVhbFhTMW1zZlNUUFdzLWI0Vm50Z1pDbXJHd3ItbkJaayZzaWc9Q2cwQXJLSlN6UDZqSzJ6am5YSFZFQUUSABoAIAEoADAEGh4KGkNNYUk3NEN6MVkwREZUaXVwZ1FkZjZ3eThBEAU&quot;data-google-av-override=&quot;-1&quot;data-google-av-dm=&quot;2&quot;data-google-av-aid=&quot;0&quot;data-google-av-naid=&quot;1&quot;data-google-av-slift=&quot;&quot;data-google-av-cpmav=&quot;&quot;data-google-av-btr=&quot;&quot;data-google-av-itpl=&quot;20&quot;data-google-av-rs=&quot;4&quot;data-google-av-flags=&quot;[&amp;quot;x%278440&amp;#39;9efotm(&amp;amp;753374%2bejvf/%27844&amp;gt;&amp;#39;9wuvb$&amp;amp;56533&amp;gt;!=|vqc)!273794&amp;amp;&amp;lt;qqvb/%&amp;lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;amp;&amp;lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&amp;#39;76463;21$?ebkpb$&amp;amp;0366717&amp;gt;*&amp;gt;bgipf+!3=712363%9aihwc)!7202&amp;lt;217&amp;#39;9efotm(&amp;amp;20061;48&amp;amp;&amp;gt;`dopb/%&amp;lt;1707200!=8(&amp;amp;2005575?&amp;amp;&amp;gt;`dopb/%&amp;lt;170642?!=|vqc)!7201;=50&amp;#39;9wuvb$&amp;amp;03641654*&amp;gt;bgipf+!3=731103%9aihwc)!7200?073&amp;#39;9efotm(&amp;amp;2004?51;&amp;amp;&amp;gt;`dopb/%&amp;lt;17&amp;gt;474&amp;gt;!=nehu`/!36406412!9abk{a($167745;=&amp;amp;&amp;lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&amp;gt;7&amp;amp;&amp;lt;qqvb/%&amp;lt;104=460!=nehu`/!363;42&amp;gt;7!9abk{a($1656;3?&amp;lt;&amp;amp;&amp;lt;cbotf+*01011776%2bejvf/%72&amp;gt;17266!=efdwa*&amp;#39;7616?=&amp;lt;=$?ebkpb$&amp;amp;0335225&amp;gt;*&amp;gt;bgipfz&amp;quot;]&quot;><div id=&quot;mnet-vtgt-302e9011555ac5b96f15826165a613e4&quot;><script>(function(j){try{var a=j-1748958735012;var i=Math.random();var c=false;var k=window.mraid;function f(){try{return !!window.top.location.href}catch(l){return false}}function g(e){if(a>0){e+=&quot;&amp;utime=&quot;+a}if(typeof k!==&quot;undefined&quot;){e+=&quot;&amp;mraid_version=&quot;+k.getVersion()}e+=&quot;&amp;sf=&quot;+(f()?0:1);e+=&quot;&amp;cpr=&quot;+i;e+=&quot;&amp;audit_scanning=aD7-DgAKpMYEpq44ADKsf24E_7zChh3UNurAPg&quot;;e+=&quot;&amp;audit_cur=&quot;;return e.replace(&quot;&amp;&quot;,&quot;&amp;pixel_len_bucket=&quot;+e.length+&quot;&amp;&quot;)}var d=&quot;https://hblg.media.net/log?logid=kfke&amp;evtid=plutol1&amp;__q=AcYEewKELAQCEAABAIAAAgAAAABAAAEABgAAQIABAAgAINCYCXDGAVA2NjQzMzkyMzExMzE2XzEyOTIyMDgwMDdfMjgyMTQ3Mjk5MjUxMV8wQGQ0MjBhMjg0ODUxOTVkNDU5NjQ2ZGJjYjBkNzVlZDZlAMbniY0C9gMcX3tmSYDCP-Olm8QgsMI_bGh0dHBzOi8vd3d3LnNtYXJ0YmFja2dyb3VuZGNoZWNrcy5jb20vcGhvbmUvNTYxOTMyNDIxNwRVUzJzbWFydGJhY2tncm91bmRjaGVja3MuY29tEjhDVU1ETlQwMggMNzI4eDkwEDAuMTE3Njc5FHNjaHdhYi5jb20OZWFzdF9zYxoxMzRfNjE3NzcwODg2CEVCREEIBmFkbQAAAAAAAEBUQL6qg-HmZQIwAAAAwCm4GT80cnRiLWViZGEtY2M2NTg5ODYtam1yMmYuU0MCEDdjY2EzYTBkAmQCCGViZGEiMTMzMTAyNF82MTc3NzA4ODZAMzAyZTkwMTE1NTVhYzViOTZmMTU4MjYxNjVhNjEzZTQCCgACAQACMQYxMzQyc21hcnRiYWNrZ3JvdW5kY2hlY2tzLmNvbQAABjIuOA&quot;;function b(){(new Image()).src=g(d);c=true}b()}catch(h){(new Image()).src=&quot;//hblg.media.net/log?logid=kfke&amp;evtid=plutoevents&amp;__q=AVzjwLwAACAGU1NYJlJBX1NDUklQVF9FWENFUFRJT04yc21hcnRiYWNrZ3JvdW5kY2hlY2tzLmNvbRI4Q1VNRE5UMDISMjgyMTQ3Mjk5NHJ0Yi1lYmRhLWNjNjU4OTg2LWptcjJmLlNDDmVhc3Rfc2MCQGQ0MjBhMjg0ODUxOTVkNDU5NjQ2ZGJjYjBkNzVlZDZlCEVCREECZBA3Y2NhM2EwZAYyLjg&amp;error=&quot;+h.message}})(new Date().getTime());</script> <noscript> <img style=&quot;display:none&quot; src=&quot;//hblg.media.net/log?logid=kfke&amp;evtid=plutoevents&amp;__q=AVzjwLwAACAGU1NYMFNDUklQVF9UQUdfTk9UX1NVUFBPUlRFRDJzbWFydGJhY2tncm91bmRjaGVja3MuY29tEjhDVU1ETlQwMhIyODIxNDcyOTk0cnRiLWViZGEtY2M2NTg5ODYtam1yMmYuU0MOZWFzdF9zYwJAZDQyMGEyODQ4NTE5NWQ0NTk2NDZkYmNiMGQ3NWVkNmUIRUJEQQJkEDdjY2EzYTBkBjIuOA&quot;> </noscript><DIV STYLE=&quot;position: absolute; left: 0px; top: 0px; visibility: hidden;&quot;><IMG SRC=&quot;https://pagead2.googlesyndication.com/pagead/gen_204?id=xbid&amp;dbm_b=AKAmf-BFrIf9xQO9OU0FrOixf6M5toeY9o0i3JP1_33s5rm21gKlECKhII_2JALSnnmLl1slqmrZNaEaJ4PueQo6lU_t9SWryAd_DHGAS8or5swoX4xDhLw&quot; BORDER=0 WIDTH=1 HEIGHT=1 ALT=&quot;&quot; STYLE=&quot;display:none&quot;></DIV><iframe title=&quot;Blank&quot; src=&quot;https://googleads.g.doubleclick.net/xbbe/pixel?d=CIYBEM3zNhiG38mmAjAB&amp;v=APEucNXlPtZO1rpzHpRkMSvffGoHfMfvDYu7EXVXP5c5CvkMmH9eOctZK_hmBt5_aOeFAh3Y7h1_xMZG6ovKd-Lm0fdiCVO2LtB2YgN-91gegL0WvCQPzUI&quot; style=&quot;display:none&quot; aria-hidden=&quot;true&quot;></iframe><div><div style=&quot;position:absolute;&quot;><script>(function() {var u = 'https://googleads.g.doubleclick.net/dbm/ad?dbm_c=AKAmf-Cq0HOVu5Xyb3pJdXkNuqBniThcuS_dfBl03z6GSkPlkmzLMjoJ27YCwbt394tRC2QnhoO83_Wux1fybh46iCCV00k3bjqSRDpqQb95XinBvvtOiMWAVky7Wtuw5nsO3RYQYBtoKp_p_B-DiMaZ8gc6B6hnMZFgkCrO5a9hLKgInuH4ucUusmd2SN1FslbFhKVO35Ys2yKHPgN-hDKbiAbgKiPPLdibXkoSq46bQHhvaawElIOlGeTB9edB5gIBdYu4siLn1hEhVtVM0rpgqjbA1-YlJw&amp;dbm_d=AKAmf-APnokSIlgdJIeb7wHZIjKEEfzIPhNS1WTTbOxUoi-vki7yIi3X6neFfJ66u64H7MO_DGEAA_DVY-czh4HhoTn0MvBzVMEbUx9fjjaFGneY51dxuZxlsXi6i3XIDZba7R5GrUIPqrK0hpLnEMU6Ps3ho1fyQuPZkEtLj4RJmvy-2YOvIbgOYy8L5EjedzJqZA24GRuhvicu-G5kWFkTXHnPD6CMzV6hJQbe6Tl_gg3dse5PvG2EwGOmhhXOhp5r4qVVGh8ShPQfDq9Y0sVvXZ2xn1OrWF6M5RxMLNgzAN0BHAz8wFfE3f5n8eQB6BYYg56rq3tMigLdRGJt2bA4j-jaIf2lrT96gk0FyvEwiE8sLVkuSAmEcom0mxAw6I0OUGON9uA9SdcjqBqYAD4rS3zJtS3aBUXrIFIlrFleSFfs02nZmpFGT-VP3l5LWjfk9RtkmAw2BOKRdqnr8bt07HpnE35ikIiQ2lINYa1tfg5eItOOxP9trfh5o9KIaQnajZYFWP60Pj8wcjIWovwb6HtA_mEFmUFG8D5DMnvOSYdr1BotQ-h67MhHa9pegAba8IFIvOJhv_yMVRTvlafiN1zloQU9Uc-hdLOpro24WvFQmuXssgi3wR88ZIUvAXwB9T-BXoi-0HowVGuUYeDQlD6DpLR3qn19XBKX57gJxwmhxP0wp-5Yr-a852nu-tVqh8_Bdg2ucW3jFaqXpANLV6XkdWjGzY1LVeOh383rQGuBcE84SYtd4AU5kkfj8EJ4LwpBXz0xPwDfMO5Flm2Yv2JRVCjOezduNS_Q8TBumOuU8necCvszuskRG1ZMZ8NLB6J1wznRq_rE8eoUpTDgInbjsoz40ZTFK7SuIm25N_PcKk2J1CRsWJV6ltF4aOhaQ3rFAwZLg9Fk7mOrzv31KJU8N6hAHlB3UWW5e0Se4013Iez-DCtuGyF5I57_zi56pB8o-foX_WkdgL1V9TGDoyAxXP4ldU42SKoL_kcXqBLZ3W2lkRiQNk_62CMiOqC7wVpXH_bxlVM0BpFHIE3Vqyo-9b6_lFlkCFUIO0Wg2F27dEiwSUpYRXrBnVB6aa1ZoAZ-ENlq54haR-FzFVfcb4-2lPwa-1Wr7mksvmh3k2mZjgtKcwlUIJOfB1XjfFd3VDJXPlUzjTmzB7yybCPuEnAR431qO4_TehS3zMUrwqzH0BP7bPeiRz1k0l4D9FnRvN66bQ9isdnHmYd8BlRXYcqYEtCG9FnBwdyv47DDYJWRZQBe2z9ur-jCGY2c0oLA5qc7clKEvU6fQW1Vjm0m8OGwKdcB8yreCTE-ZNv4Cf8Z58f3ZNSouR2ry1YdrjgdAFXoRuz4kg5MLrAzglcPgUCukLn7V-JwIVBvrDnYlEZIVu-u7dSW3PXLnB3kvTtSsB0RRcWk4NqlkhWpa9v217OUF2JeycJ1_6k5pMa29f29IZ9hQqK3KSMeDM5O8dX-Qyf7KIdQm9yP1M_ih0zBDExuhFf5a1AA1FrFzsBavt8uqTVbMftBBio7NtZgKmkUsAaZDgXuM3PS1pimbVjonHh-FwG_Nai5iGrhmU6P1bzDAXG1__Q3-Ir64ZCxxdGE_ZGKLepp5cwIeu611Yp98QOcXvPqZYLakQ5STKmAASho_146hTl2zQlqmwsS_JTOz3S7MCxK2eZijqfAHgo7zLtv557WwzJ1aaCT9QFtMB_BqhJSQLMh4m_I60S82h2k2oXW6hONCMXF3wKV2xGv_tRzFupCcnuwNMGcZIFRA3FH_rkt3KzpxoUhIkUcckA_ReY_5XGiFRM-ZO4zokxkIWjm8FcKeFkThybhJ9TwVM9IyzpVcc-IfEHcCHCjG3ibhypgovGf6N-LUGJephD71ecoO9TDFDz5Q7dDKzaGCDGgUIUKDpijQWWO8xBX14imuTxU-6W4EjOu-lixMA587f7YUm67eb-_BqB8CHywUxT7G-tfM-Rl3nsIGeyQ-lM45UVB-uYUgEVI_CtD3IuKH1Ry8sbKj0bJ5a4_FYogJ3S0b2maNDKFay5C5Aaqsrm7kbbGxYrzZhbZOoEeiQ8pavXjA2Up8RVoC5U8Y-mh6GY9EFsB6EptqL6Zj_gU44XQa26lA-70ZldU9Je-TqbnUpogHUhXLxmk-L7Yu8Zid5JH9HqUPMyAIeaUZvxNWL4xTVLAxhqHmpsrQlhgI-JG_AOOOHMk63kgzO8sWLFpuTQzn3Tb1Wuw8UAwLa40aHsncyKqzkmyTuzQpzLSHvdRrx4fYOjuE7u0NQs8ZB11ykEwP32oRIcSinR9PZCTFJHfEyk9a5bvUiQILsSZuKKUghg9e-7h8_lkCnrngSZI32YaecAt2zdJ4n64D42iRinAlP2zZHuYTtA9yvztkJ8BH0XFqN0H9NS7oTm_YN3YRFdb5ZSjJZW6r7PTaBwKs7eKC0ZBUbNWXA8MNIyKW1pbC9TptEu9HRmhjAeKhfoZOqbQ3SPf9w5RRc0eN8qTo4OKbko19Z4pk_y-AFwqPRwsoVh8OiTg82yTQxzK4QBJ_tWeCY11w6CXTVuP45ZIPUgLInXeGLHgWwQI8WTWvthd-Kln0lMnBg3nZvExHelA7tlcl11wDxbZK6FJFzGEXxKQfq63nFW5cVQuf-UHgSKGAfZf3W_SmWcKmzyq0KDBtnp2CL8Ea3Bg_vgXuWUme6iKQnOg3wwUHRMxd3a_IWTS0IViwDoRw4OKf-6VfjZn9NDYT62Ldz1HnvKkvR8WqNF5X-GrQy484Gu2Y_8tgobTW0ToRheUlaS7Bdyvk8fX5rWxSflYoXt6h21R9Gz9UgHaenZqJqYkL7a2bD5g6wR6kR9KKCfegdNYQgYdGbYbdBBfueGbISmSOdYqhgT7SP3G5o5ApHD45CC3pATc3fOBUQbPaBqnlayOWMw6lRjHmCCsI4NfDQWDBYxmRsqK4qbT-X9INGHozqMEMnayxGj-ZR4vCZztxqkyjfIrF-ZvmWvxgROncX-4we4j7DBjYQ-PgK1g5QmicdlKb2R-Su85ofNmwAkmLf6XcqTofmkOnwevUXkozttS1IGTWKS2lX3ad7asaJX0_GtSvx4rJvpF64-HecwsfLme5JPTFR8rgQ5x8DIRWX2Aw1XBONnFJTpsVoqCSEWgzB6TfhPM3SxVOnAwxMUVypaWnM3kDND7626oGNAxMBhfydFXaH2aANK0YhS0D9__946nv78a2Hy8EGuUwclZ2_H6jIYewy5layYhxxAKv2qTm2Tm7nmcSkq4H5vA1dMeDGLMhPcf3e1cW8bX_kHFrD0uya-lU_AAlJeR0WSe9R9epQwMb8JjxPWLsutqRWZIA6tMzrsezxtPcDH7uiIYyndc6cEQB6O0pwU2WmGknhiofIMZuFvs9yCBuZ2KS43TkhKnn0ONWoE7yZuMgRWVH5vij8Nnd-oT7p4_2VB_Hom434D2tf-aGqIwrCRCKUtR42Tz6gEBRgHNH3s_GajLuTtP--1DaaWQi4S5s8H0MURznWGydBze4mAZw_3kGUfecVC821DmCqov_5BbaHdyOwgS3IArcHzK5Xo5-oSZD68DWWjMzExWzIsf8kYOzlUNLIDXALdEWbyDje7OUDtpYYbqAyv4l2YpjupelP_Kzb0ZEJaBl7byJ7t42WzrK41ZQRIbXw7-74C1N7mbN3KnJvhpfJi2GyjzJxCpS3oM33CnQo43KnJAJnLqpuklgCDH8YgVM_3D-rEQUhxVxK_iSX2tn3AAJ7GzmXv9qLryiXIGpKvlLIP8dKNVcnwlXfEwU5g23rGkkfIaQ3wvvnu_gkxbV2tNOfIXwwlWiUy0mS9oo3TKc0TQpUrj1pz6qCviCgEdYYKhNTI4GrdxNqdTcbdLEngFz7Z3tfIwQJOiR8aFs-2qctnkzeRUPfqVx3UfNHMdEeiVjmhWyVwLOkTCQh7sHtY-dbV9ldRlgY1p7M33zhsbxSoW82be2ftDGKqZ&amp;cid=CAQSSQDZpuyzR6yMSpXusFXnXOgsvuZ_LbMwdfvYjAjayS4F6g8z-uZHi8VRSgoQQaFFwLe2wWmmEm1YxpgIG-EUon1Bj50tMZqpeGcYAQ';window.dv3Utw = {u: u,w: function() {document.write('<script src=&quot;' + u + '&amp;flb=1&quot;></s' + 'cript>');}};})();</script><script src=&quot;https://pagead2.googlesyndication.com/pagead/js/dv3.js&quot; data-dv3-creative-fetch=&quot;https://googleads.g.doubleclick.net/dbm/ad?dbm_c=AKAmf-Cq0HOVu5Xyb3pJdXkNuqBniThcuS_dfBl03z6GSkPlkmzLMjoJ27YCwbt394tRC2QnhoO83_Wux1fybh46iCCV00k3bjqSRDpqQb95XinBvvtOiMWAVky7Wtuw5nsO3RYQYBtoKp_p_B-DiMaZ8gc6B6hnMZFgkCrO5a9hLKgInuH4ucUusmd2SN1FslbFhKVO35Ys2yKHPgN-hDKbiAbgKiPPLdibXkoSq46bQHhvaawElIOlGeTB9edB5gIBdYu4siLn1hEhVtVM0rpgqjbA1-YlJw&amp;dbm_d=AKAmf-APnokSIlgdJIeb7wHZIjKEEfzIPhNS1WTTbOxUoi-vki7yIi3X6neFfJ66u64H7MO_DGEAA_DVY-czh4HhoTn0MvBzVMEbUx9fjjaFGneY51dxuZxlsXi6i3XIDZba7R5GrUIPqrK0hpLnEMU6Ps3ho1fyQuPZkEtLj4RJmvy-2YOvIbgOYy8L5EjedzJqZA24GRuhvicu-G5kWFkTXHnPD6CMzV6hJQbe6Tl_gg3dse5PvG2EwGOmhhXOhp5r4qVVGh8ShPQfDq9Y0sVvXZ2xn1OrWF6M5RxMLNgzAN0BHAz8wFfE3f5n8eQB6BYYg56rq3tMigLdRGJt2bA4j-jaIf2lrT96gk0FyvEwiE8sLVkuSAmEcom0mxAw6I0OUGON9uA9SdcjqBqYAD4rS3zJtS3aBUXrIFIlrFleSFfs02nZmpFGT-VP3l5LWjfk9RtkmAw2BOKRdqnr8bt07HpnE35ikIiQ2lINYa1tfg5eItOOxP9trfh5o9KIaQnajZYFWP60Pj8wcjIWovwb6HtA_mEFmUFG8D5DMnvOSYdr1BotQ-h67MhHa9pegAba8IFIvOJhv_yMVRTvlafiN1zloQU9Uc-hdLOpro24WvFQmuXssgi3wR88ZIUvAXwB9T-BXoi-0HowVGuUYeDQlD6DpLR3qn19XBKX57gJxwmhxP0wp-5Yr-a852nu-tVqh8_Bdg2ucW3jFaqXpANLV6XkdWjGzY1LVeOh383rQGuBcE84SYtd4AU5kkfj8EJ4LwpBXz0xPwDfMO5Flm2Yv2JRVCjOezduNS_Q8TBumOuU8necCvszuskRG1ZMZ8NLB6J1wznRq_rE8eoUpTDgInbjsoz40ZTFK7SuIm25N_PcKk2J1CRsWJV6ltF4aOhaQ3rFAwZLg9Fk7mOrzv31KJU8N6hAHlB3UWW5e0Se4013Iez-DCtuGyF5I57_zi56pB8o-foX_WkdgL1V9TGDoyAxXP4ldU42SKoL_kcXqBLZ3W2lkRiQNk_62CMiOqC7wVpXH_bxlVM0BpFHIE3Vqyo-9b6_lFlkCFUIO0Wg2F27dEiwSUpYRXrBnVB6aa1ZoAZ-ENlq54haR-FzFVfcb4-2lPwa-1Wr7mksvmh3k2mZjgtKcwlUIJOfB1XjfFd3VDJXPlUzjTmzB7yybCPuEnAR431qO4_TehS3zMUrwqzH0BP7bPeiRz1k0l4D9FnRvN66bQ9isdnHmYd8BlRXYcqYEtCG9FnBwdyv47DDYJWRZQBe2z9ur-jCGY2c0oLA5qc7clKEvU6fQW1Vjm0m8OGwKdcB8yreCTE-ZNv4Cf8Z58f3ZNSouR2ry1YdrjgdAFXoRuz4kg5MLrAzglcPgUCukLn7V-JwIVBvrDnYlEZIVu-u7dSW3PXLnB3kvTtSsB0RRcWk4NqlkhWpa9v217OUF2JeycJ1_6k5pMa29f29IZ9hQqK3KSMeDM5O8dX-Qyf7KIdQm9yP1M_ih0zBDExuhFf5a1AA1FrFzsBavt8uqTVbMftBBio7NtZgKmkUsAaZDgXuM3PS1pimbVjonHh-FwG_Nai5iGrhmU6P1bzDAXG1__Q3-Ir64ZCxxdGE_ZGKLepp5cwIeu611Yp98QOcXvPqZYLakQ5STKmAASho_146hTl2zQlqmwsS_JTOz3S7MCxK2eZijqfAHgo7zLtv557WwzJ1aaCT9QFtMB_BqhJSQLMh4m_I60S82h2k2oXW6hONCMXF3wKV2xGv_tRzFupCcnuwNMGcZIFRA3FH_rkt3KzpxoUhIkUcckA_ReY_5XGiFRM-ZO4zokxkIWjm8FcKeFkThybhJ9TwVM9IyzpVcc-IfEHcCHCjG3ibhypgovGf6N-LUGJephD71ecoO9TDFDz5Q7dDKzaGCDGgUIUKDpijQWWO8xBX14imuTxU-6W4EjOu-lixMA587f7YUm67eb-_BqB8CHywUxT7G-tfM-Rl3nsIGeyQ-lM45UVB-uYUgEVI_CtD3IuKH1Ry8sbKj0bJ5a4_FYogJ3S0b2maNDKFay5C5Aaqsrm7kbbGxYrzZhbZOoEeiQ8pavXjA2Up8RVoC5U8Y-mh6GY9EFsB6EptqL6Zj_gU44XQa26lA-70ZldU9Je-TqbnUpogHUhXLxmk-L7Yu8Zid5JH9HqUPMyAIeaUZvxNWL4xTVLAxhqHmpsrQlhgI-JG_AOOOHMk63kgzO8sWLFpuTQzn3Tb1Wuw8UAwLa40aHsncyKqzkmyTuzQpzLSHvdRrx4fYOjuE7u0NQs8ZB11ykEwP32oRIcSinR9PZCTFJHfEyk9a5bvUiQILsSZuKKUghg9e-7h8_lkCnrngSZI32YaecAt2zdJ4n64D42iRinAlP2zZHuYTtA9yvztkJ8BH0XFqN0H9NS7oTm_YN3YRFdb5ZSjJZW6r7PTaBwKs7eKC0ZBUbNWXA8MNIyKW1pbC9TptEu9HRmhjAeKhfoZOqbQ3SPf9w5RRc0eN8qTo4OKbko19Z4pk_y-AFwqPRwsoVh8OiTg82yTQxzK4QBJ_tWeCY11w6CXTVuP45ZIPUgLInXeGLHgWwQI8WTWvthd-Kln0lMnBg3nZvExHelA7tlcl11wDxbZK6FJFzGEXxKQfq63nFW5cVQuf-UHgSKGAfZf3W_SmWcKmzyq0KDBtnp2CL8Ea3Bg_vgXuWUme6iKQnOg3wwUHRMxd3a_IWTS0IViwDoRw4OKf-6VfjZn9NDYT62Ldz1HnvKkvR8WqNF5X-GrQy484Gu2Y_8tgobTW0ToRheUlaS7Bdyvk8fX5rWxSflYoXt6h21R9Gz9UgHaenZqJqYkL7a2bD5g6wR6kR9KKCfegdNYQgYdGbYbdBBfueGbISmSOdYqhgT7SP3G5o5ApHD45CC3pATc3fOBUQbPaBqnlayOWMw6lRjHmCCsI4NfDQWDBYxmRsqK4qbT-X9INGHozqMEMnayxGj-ZR4vCZztxqkyjfIrF-ZvmWvxgROncX-4we4j7DBjYQ-PgK1g5QmicdlKb2R-Su85ofNmwAkmLf6XcqTofmkOnwevUXkozttS1IGTWKS2lX3ad7asaJX0_GtSvx4rJvpF64-HecwsfLme5JPTFR8rgQ5x8DIRWX2Aw1XBONnFJTpsVoqCSEWgzB6TfhPM3SxVOnAwxMUVypaWnM3kDND7626oGNAxMBhfydFXaH2aANK0YhS0D9__946nv78a2Hy8EGuUwclZ2_H6jIYewy5layYhxxAKv2qTm2Tm7nmcSkq4H5vA1dMeDGLMhPcf3e1cW8bX_kHFrD0uya-lU_AAlJeR0WSe9R9epQwMb8JjxPWLsutqRWZIA6tMzrsezxtPcDH7uiIYyndc6cEQB6O0pwU2WmGknhiofIMZuFvs9yCBuZ2KS43TkhKnn0ONWoE7yZuMgRWVH5vij8Nnd-oT7p4_2VB_Hom434D2tf-aGqIwrCRCKUtR42Tz6gEBRgHNH3s_GajLuTtP--1DaaWQi4S5s8H0MURznWGydBze4mAZw_3kGUfecVC821DmCqov_5BbaHdyOwgS3IArcHzK5Xo5-oSZD68DWWjMzExWzIsf8kYOzlUNLIDXALdEWbyDje7OUDtpYYbqAyv4l2YpjupelP_Kzb0ZEJaBl7byJ7t42WzrK41ZQRIbXw7-74C1N7mbN3KnJvhpfJi2GyjzJxCpS3oM33CnQo43KnJAJnLqpuklgCDH8YgVM_3D-rEQUhxVxK_iSX2tn3AAJ7GzmXv9qLryiXIGpKvlLIP8dKNVcnwlXfEwU5g23rGkkfIaQ3wvvnu_gkxbV2tNOfIXwwlWiUy0mS9oo3TKc0TQpUrj1pz6qCviCgEdYYKhNTI4GrdxNqdTcbdLEngFz7Z3tfIwQJOiR8aFs-2qctnkzeRUPfqVx3UfNHMdEeiVjmhWyVwLOkTCQh7sHtY-dbV9ldRlgY1p7M33zhsbxSoW82be2ftDGKqZ&amp;cid=CAQSSQDZpuyzR6yMSpXusFXnXOgsvuZ_LbMwdfvYjAjayS4F6g8z-uZHi8VRSgoQQaFFwLe2wWmmEm1YxpgIG-EUon1Bj50tMZqpeGcYAQ&quot; data-dv3-width=&quot;728&quot; data-dv3-height=&quot;90&quot; data-dv3-render-mode=&quot;script&quot; data-dv3-meta-data=&quot;[104,76,14958005297673538208]&quot;></script><script data-jc=&quot;112&quot; data-jc-version=&quot;r20250602&quot;>(function(){'use strict';const d=/[&amp;?]dbm_c=([^ &amp;]+)/;function e(a){return(a=d.exec(a))?a[1]:null}function f(a,b,c){a=a.getElementsByTagName(b);for(b=0;b<a.length;++b)if(e(a[b].src)===c)return!0;return!1};const g=window;if(g.dv3Utw){var h=g.dv3Utw.u,k=g.dv3Utw.w;g.dv3Utw=void 0;var l;a:{const a=e(h);if(!a||f(document,&quot;script&quot;,a)||f(document,&quot;iframe&quot;,a))l=!0;else{var m=document.getElementsByClassName(&quot;dv3-asfrm&quot;);for(let b=0;b<m.length;++b){const c=m[b];if(c.contentWindow&amp;&amp;f(c.contentWindow.document,&quot;script&quot;,a)){l=!0;break a}}l=!1}}l||k()};}).call(this);</script></div></div><iframe width='0' height='0' style='display:none' src='https://contextual.media.net/checksync.php?vsSync=1&amp;cs=6&amp;cv=31&amp;https=1&amp;cid=8CUMDNT02&amp;prvid=2034%2C2033%2C3022%2C2030%2C3020%2C590%2C2073%2C251%2C273%2C2009%2C178%2C255%2C2028%2C3018%2C2027%2C3017%2C2025%2C117%2C3014%2C459%2C636%2C99%2C77%2C38%2C3011%2C182%2C3010%2C261%2C141%2C262%2C461%2C222%2C201%2C3007%2C246%2C301%2C4%2C203%2C225%2C10000%2C404%2C80%2C229%2C9&amp;itype=EBDA&amp;purpose1=1&amp;gdprconsent=1&amp;gdpr=0&amp;usp_status=0&amp;usp_consent=1'></iframe></div><script src=&quot;https://warp.media.net/rtb/resources/release-20250527-200-adperformance.js&quot; async></script> <script>window._adp=window._adp||[];window.adPerformancePixel=&quot;https://hblg.media.net/log?logid=kfke&amp;evtid=adpvlog&amp;__q=AYwGcgCAjASAAAiAAABAgAEAAAAIAAAAAAEAAAAAAgEEAAAIAAAAAAEAAAAAAAxQwAQEEEBkNDIwYTI4NDg1MTk1ZDQ1OTY0NmRiY2IwZDc1ZWQ2ZcbniY0C9gMcX3tmSYDCPwRVUzJzbWFydGJhY2tncm91bmRjaGVja3MuY29tEjhDVU1ETlQwMj5ic2Etem9uZV8xNzQzNTAyMzQ4NzU4LTRfMTIzNDU2DDcyOHg5MBAwLjExNzY3ORRzY2h3YWIuY29tDmVhc3Rfc2MaMTM0XzYxNzc3MDg4NgQyMwhFQkRBEjhQUkw0RTdOMwA-YnNhLXpvbmVfMTc0MzUwMjM0ODc1OC00XzEyMzQ1NgIwNHJ0Yi1lYmRhLWNjNjU4OTg2LWptcjJmLlNDBmVjcAIxAjAABAAQRVhDSEFOR0UCAmRAMzAyZTkwMTE1NTVhYzViOTZmMTU4MjYxNjVhNjEzZTQGMi44&quot;;window._adp.push(function(){window.adPerf.track(&quot;mnet-vtgt-302e9011555ac5b96f15826165a613e4&quot;,[&quot;VIEW&quot;,&quot;CLICK&quot;])});</script></div><script data-jc=&quot;22&quot; src=&quot;https://tpc.googlesyndication.com/pagead/js/r20250602/r20110914/client/window_focus_fy2021.js&quot; async data-jc-version=&quot;r20250602&quot; data-jcp-url=&quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai=CrwynDv4-aMbJKrjcmtUP_9jKgQ_xi56QXPKJ5eG5BcCNtwEQASAAYMm2iYfMo8AXggEXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjfIAQngAgCoAwHIAwKqBLkCT9AS-UkRWfXd1lGCyR39LGwK7yybxkrizr3WBJDnN28za5oGaZ1FY3w6E23FPrm7FsCjFRXOqLYaaP1ypQAZyL8nPO1y-J0AQrlW7KJSocuDKSWPp1V44EBfEmHZ_fTsomOC8QQUguUXnG6v77oCckaJIOL1gi723sEzs3U83MoCK2hxenAdO5O2-rsAJzn3G9YK76JMUrUWdwLC34V_JsqTsVhQQNPlmRZ-0CY2Zo_4UGn7QkN9GGqH6dcpNQLY7ynFTLRmPK0qIJT8U8cYK_WeM68ZFnLpdu0kWjpvTON8wEIQBWs8LachftnSkmfUip34i5iPuevW0uxqKMCO4kuVaZfnkJQBbRLpqxt6EoH7HCZAhBOyQwDrBF-P3E8vj47ftHCcj9tmuxzvcu0VjsC_X56VJ6w1gOAEAYAG_rXW8qGq9dLoAaAGIagHpr4bqAeW2BuoB6qbsQKoB_-esQKoB9-fsQKoB62-sQKoB7_TsQLYBwDSCCYIgGEQATICigI6DYBAgMCAgICAqIACoANIvf3BOljgtOyAs9WNA_oLAggBgAwBqg0CVVPiDRMI14TtgLPVjQMVOK6mBB1_rDLw6g0TCLr_7YCz1Y0DFTiupgQdf6wy8NAVAYAXAbIXDhgLKgo0MTYwMzQ1Nzg1&amp;amp;sigh=xcU3A9K9Vao&amp;amp;cid=CAQSPADZpuyznI9Ib5iPPvNVcLxoKSWM91Oy0WkaXitM1WdFT4dshDZJ5c5nBYKCHFJnrhqYgwOXHQj6H4X3bQ&quot; data-jcp-gws-id=&quot;&quot; data-jcp-qem-id=&quot;CMaI74Cz1Y0DFTiupgQdf6wy8A&quot;></script><iframe title=&quot;Blank&quot; scrolling=&quot;no&quot; frameborder=0 height=0 width=0 src=&quot;https://pagead2.googlesyndication.com/pagead/s/cookie_push_onload.html#aHR0cHM6Ly91bS5zaW1wbGkuZmkvZ3BfbWF0Y2g_Z29vZ2xlX3B1c2g9QVhjb09tUUlSclpXbjNYS3J6YkRWZV83SUQzWEZSY0xfOVNzN0M5WjhRQXR6RjM5WjV1R0RJcmU2TnVVUS1XM2NTYkcwbEN6MkpCZ0M5R2lnNU8xS0x0OTlpMXZhcHAwZG5NNW9FM2tJSG95WmlxWGhkQVQ5bnZERzhNWnZuVG9UWVp4YzZ3SDE4dlNPcGk2OWp6RkVNdFpPMzQ5,aHR0cHM6Ly9hZHMudHJhdmVsYXVkaWVuY2UuY29tL2dvb2dsZV9waXhlbD9nb29nbGVfcHVzaD1BWGNvT21RMElTNWNPZ09remlEQzZ5cV9NNWJkTTF1cWlIQ3hfcUdFdThSZE8xUXhFNDFyOUhXVm4wdzVzdG1CZHJnNTlHak5KblRvaEM5cm15Yk5qNzBYWWZ5X0JNRG1oZWRnSzllMGd3TW16VmU5UDQtNkQxZjY3azR3Zkx3dk5uX0NDdHk1dFBjVDB1NDRlLXRZbFlWbG9UaXU=,aHR0cHM6Ly94LmJpZHN3aXRjaC5uZXQvc3luYz9zc3A9Z29vZ2xlJmdvb2dsZV9wdXNoPUFYY29PbVJ1ZGRfeEVGRFFQRUJhbTE4NVVyVnNBaDREMGRJLW1SMXFpaDN5RDVjeFF5Y0dRMHFtMi0wb3dwR2JabzdVUi1VeVJmVy1qc1ZVcFF6d080QXhTeTg0dFd3Ui1UaU8teUhaQ0RjbXdsbTRrcGxPV3pIVVVtWjVYZXZtMnpFT0xFSDE3dmpOLXkxejZ5dGlvd3FkLW51WA==,aHR0cHM6Ly9zLmFkLnNtYWF0by5uZXQvYy8_YWRFeEluaXQ9ZyZnb29nbGVfcHVzaD1BWGNvT21SMmZSbG9vWFk2eU9relRzbW8yUVJDdTRGRHJnQ3hFbFZTMG94UE5ybkNFanNiR0RHN3RzdlIwUVdnMHVnY3J4WFFxWnZYbW9uaFNKMjY1VkRTZjZCcTRpMU9sXzRPVW9QRTNJaFdCSm1BV1FxUGM1QW5tLVpHRkg5MkZsbjhJdXJtSlp5TmY3ZjgwU25tdnlDaWpLMlA=,aHR0cHM6Ly9iZWFjb24ubHlueC5jb2duaXRpdmxhYnMuY29tL2FkeC5naWY_Z29vZ2xlX3B1c2g9QVhjb09tVFAzdDlTWS0yczR3ZVpEbEQxUmNyVXRfTVpCMnVlczNjUWVldjBQUW9SZXRROW1JTXIzRUgwcFpiNEIxeUdSNGtldWpicjZQQ1dRWUhZUFVjdVlCb2R1UW02ZnpKX2FSb1E3WjhvSDZiWEpoSFBoTEluVHV1NVdfWk1kOG1wZThHcjVjUjdEaFJrSmJWT1RVZlFNem5pMHc=,aHR0cHM6Ly9zeW5jLm1hdGh0YWcuY29tL3N5bmMvaW1nP210X2V4aWQ9NCZwaXhlbF9tYXRjaD0mcmVkaXI9aHR0cHMlM0ElMkYlMkZjbS5nLmRvdWJsZWNsaWNrLm5ldCUyRnBpeGVsJTNGZ29vZ2xlX25pZCUzRG1lZGlhbWF0aCUyNmdvb2dsZV9obSUzRCU1Qk1NX1VVSURfQjY0V1MlNUQlMjZnb29nbGVfcHVzaCUzRCU1QkdPT0dMRV9QVVNIJTVEJmdvb2dsZV9wdXNoPUFYY29PbVQ0R0xpVXlYNVlORThfcjRySFJtTndlLW11ZHRKSG50ZGhzUmFMamFGX0tfV3JkZ3lHVmZZZk9xVjBlRDFPVmY0N053eDFHbEZ1eE9SUmQwYmhyU3NOajMwZTIwQkZYOENseWNSUUtLYUR2NHVPcnhZUjVPU2lZYVozSUhjS1lLX0pZcl9uMkdqWEgzU19WODVvTEFvcThB,aHR0cHM6Ly9ndHJhY2VuZXAuYWRtYXN0ZXIuY2MvanUvY3MvZ29vZ2xlP2dvb2dsZV9wdXNoPUFYY29PbVRmb1FkZjl4TXEweG5IVHF2RlltWjhxdGJfWFpUWUVnVVN1aVlzUkVCZ2MwTW5zUkNRM3JEczc3M0VBZFE3NTI1Vk95bEpTWHJpVWdlQndDYlpnczZoNFJmV2RoRkRUeVpUWGpLTktNNV9pNkp2dDlZNnNmR3otUmltc242bjJfazhhN1BhdTRRLUlwRXVhaUctUk1oMl93,aHR0cHM6Ly9jbS5nLmRvdWJsZWNsaWNrLm5ldC9waXhlbC9hdHRyP2Q9QUhORjEzTDJTcnFPTHNmS3hrVEdyTDFsLUh3LWxCN09NREhUZHp4R0JlLVVwU05BQjViQ2F1M3VnUWtnWmIzWkFySXVzQ1NEWXk4djl0WWE=&quot;  style=&quot;position:absolute&quot; aria-hidden=&quot;true&quot;></iframe><script data-jc=&quot;23&quot; src=&quot;https://tpc.googlesyndication.com/pagead/js/r20250602/r20110914/client/qs_click_protection_fy2021.js&quot; data-jc-version=&quot;r20250602&quot; data-jcp-init-data=&quot;[[[[null,500,99,2,9,null,null,null,1]]]]&quot;></script><img src=&quot;//www.google.com/ads/measurement/l?ebcid=ALh7CaSJFcmB-D8sGYuBY4ycGf739XdYtKEz9teiketH9dkHUUTjwW7pB21QUz12Oc12JPdPk1pgb9gLX8vXC9espGjySNRVAg&quot; style=&quot;display:none;&quot; alt=&quot;&quot;></img><script src=&quot;https://tpc.googlesyndication.com/safeframe/1-0-45/js/ext.js&quot;></script><div style=&quot;bottom:0;right:0;width:728px;height:90px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAAB/SURBVBjTbZELDoAgDEPLDXb/0yrtVoqRyMf4Vl8G8D+WxnvAijcUv5Z3rWsTRHvnSq7Ilmr0lhQuGlHj7P6389tkxo6u8JYvlMopjNCRQZsM3M/g4lVTVCp0J7Tjo2HQ2fYeNR2jc8plhbuQ+Xc3Oj07OKEz43YsfW5PvmP+AFm5Bg1D91zBAAAAAElFTkSuQmCC') !important;&quot;></div><script data-jc=&quot;103&quot; data-jc-version=&quot;r20250602&quot; data-jcp-base_url=&quot;https://googleads.g.doubleclick.net/pagead/conversion/?ai=CrwynDv4-aMbJKrjcmtUP_9jKgQ_xi56QXPKJ5eG5BcCNtwEQASAAYMm2iYfMo8AXggEXY2EtcHViLTk5NjE4MTQ4MjM5MzA5NjfIAQngAgCoAwHIAwKqBLkCT9AS-UkRWfXd1lGCyR39LGwK7yybxkrizr3WBJDnN28za5oGaZ1FY3w6E23FPrm7FsCjFRXOqLYaaP1ypQAZyL8nPO1y-J0AQrlW7KJSocuDKSWPp1V44EBfEmHZ_fTsomOC8QQUguUXnG6v77oCckaJIOL1gi723sEzs3U83MoCK2hxenAdO5O2-rsAJzn3G9YK76JMUrUWdwLC34V_JsqTsVhQQNPlmRZ-0CY2Zo_4UGn7QkN9GGqH6dcpNQLY7ynFTLRmPK0qIJT8U8cYK_WeM68ZFnLpdu0kWjpvTON8wEIQBWs8LachftnSkmfUip34i5iPuevW0uxqKMCO4kuVaZfnkJQBbRLpqxt6EoH7HCZAhBOyQwDrBF-P3E8vj47ftHCcj9tmuxzvcu0VjsC_X56VJ6w1gOAEAYAG_rXW8qGq9dLoAaAGIagHpr4bqAeW2BuoB6qbsQKoB_-esQKoB9-fsQKoB62-sQKoB7_TsQLYBwDSCCYIgGEQATICigI6DYBAgMCAgICAqIACoANIvf3BOljgtOyAs9WNA_oLAggBgAwBqg0CVVPiDRMI14TtgLPVjQMVOK6mBB1_rDLw6g0TCLr_7YCz1Y0DFTiupgQdf6wy8NAVAYAXAbIXDhgLKgo0MTYwMzQ1Nzg1&amp;amp;sigh=xcU3A9K9Vao&quot; data-jcp-cpu_label=&quot;heavy_ad_intervention_cpu&quot; data-jcp-net_label=&quot;heavy_ad_intervention_network&quot;>(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function n(a){h.setTimeout(()=>{throw a;},0)};let p=void 0;function u(a,b=!1){return b&amp;&amp;Symbol.for&amp;&amp;a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var v=u(),w=u(&quot;m_m&quot;,!0);const x=u(&quot;jas&quot;,!0);var y={};function z(a,b){return b===void 0?a.h!==A&amp;&amp;!!(2&amp;(a.g[x]|0)):!!(2&amp;b)&amp;&amp;a.h!==A}const A={};const B=BigInt(Number.MIN_SAFE_INTEGER),C=BigInt(Number.MAX_SAFE_INTEGER);function D(a){return a};function E(a,b,e,c){var d=c!==void 0;c=!!c;const l=[];var f=a.length;let g,k=**********,H=!1;const r=!!(b&amp;64),q=r?b&amp;128?0:-1:void 0;b&amp;1||(g=f&amp;&amp;a[f-1],g!=null&amp;&amp;typeof g===&quot;object&quot;&amp;&amp;g.constructor===Object?(f--,k=f):g=void 0,!r||b&amp;128||d||(H=!0,k=(F??D)(k-q,q,a,g)+q));b=void 0;for(d=0;d<f;d++){let m=a[d];if(m!=null&amp;&amp;(m=e(m,c))!=null)if(r&amp;&amp;d>=k){const t=d-q;(b??(b={}))[t]=m}else l[d]=m}if(g)for(let m in g){a=g[m];if(a==null||(a=e(a,c))==null)continue;f=+m;let t;r&amp;&amp;!Number.isNaN(f)&amp;&amp;(t=f+q)<k?l[t]=a:(b?? (b={}))[m]=a}b&amp;&amp;(H?l.push(b):l[k]=b);return l}function G(a){switch(typeof a){case &quot;number&quot;:return Number.isFinite(a)?a:&quot;&quot;+a;case &quot;bigint&quot;:return a>=B&amp;&amp;a<=C?Number(a):&quot;&quot;+a;case &quot;boolean&quot;:return a?1:0;case &quot;object&quot;:if(Array.isArray(a)){const b=a[x]|0;return a.length===0&amp;&amp;b&amp;1?void 0:E(a,b,G)}if(a!=null&amp;&amp;a[w]===y)return I(a);return}return a}let F;function I(a){a=a.g;return E(a,a[x]|0,G)};function J(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error(&quot;narr&quot;);b=a[x]|0;2048&amp;b&amp;&amp;!(2&amp;b)&amp;&amp;K();if(b&amp;256)throw Error(&quot;farr&quot;);if(b&amp;64)return b&amp;2048||(a[x]=b|2048),a;var e=a;b|=64;var c=e.length;if(c){var d=c-1;c=e[d];if(c!=null&amp;&amp;typeof c===&quot;object&quot;&amp;&amp;c.constructor===Object){const l=b&amp;128?0:-1;d-=l;if(d>=1024)throw Error(&quot;pvtlmt&quot;);for(const f in c){const g=+f;if(g<d)e[g+l]=c[f],delete c[f];else break}b=b&amp;-8380417|(d&amp;1023)<<13}}}a[x]=b|2112;return a} function K(){if(v!=null){var a=p??(p={});var b=a[v]||0;b>=5||(a[v]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity=&quot;incident&quot;,n(a))}};function L(a,b){if(typeof a!==&quot;object&quot;)return a;if(Array.isArray(a)){var e=a[x]|0;a.length===0&amp;&amp;e&amp;1?a=void 0:e&amp;2||(!b||4096&amp;e||16&amp;e?a=M(a,e,!1,b&amp;&amp;!(e&amp;16)):(a[x]|=34,e&amp;4&amp;&amp;Object.freeze(a)));return a}if(a!=null&amp;&amp;a[w]===y){e=a.g;const c=e[x]|0;z(a,c)||(c&amp;2?b=!0:c&amp;32&amp;&amp;!(c&amp;4096)?(e[x]=c|2,a.h=A,b=!0):b=!1,b?(a=new a.constructor(e),a.m=A):a=M(e,c));return a}}function M(a,b,e,c){c??(c=!!(34&amp;b));a=E(a,b,L,c);c=32;e&amp;&amp;(c|=2);b=b&amp;8380609|c;a[x]=b;return a};function N(a,b,e){if(e!=null&amp;&amp;typeof e!==&quot;string&quot;)throw Error();if(a.h===A){var c=a.g;c=M(c,c[x]|0);c[x]|=2048;a.g=c;a.h=void 0;a.m=void 0;c=!0}else c=!1;if(!c&amp;&amp;z(a,a.g[x]|0))throw Error();a=a.g;a:{var d=a[x]|0;c=b+-1;const l=a.length-1;if(l>=0&amp;&amp;c>=l){const f=a[l];if(f!=null&amp;&amp;typeof f===&quot;object&quot;&amp;&amp;f.constructor===Object){f[b]=e;break a}}c<=l?a[c]=e:e!==void 0&amp;&amp;(d=(d??a[x]|0)>>13&amp;1023||536870912,b>=d?e!=null&amp;&amp;(a[d+-1]={[b]:e}):a[c]=e)}};var O=class{constructor(a){this.g=J(a)}toJSON(){return I(this)}};O.prototype[w]=y;O.prototype.toString=function(){return this.g.toString()};var P=class extends O{};function Q(a=window){return a};var R=/#|$/;const S=function(a,b=null){return b&amp;&amp;b.getAttribute(&quot;data-jc&quot;)===String(a)?b:document.querySelector(`[${&quot;data-jc&quot;}=&quot;${a}&quot;]`)}(103,document.currentScript);if(S==null)throw Error(&quot;JSC not found 103&quot;);const T={},U=S.attributes;for(let a=U.length-1;a>=0;a--){const b=U[a].name;b.indexOf(&quot;data-jcp-&quot;)===0&amp;&amp;(T[b.substring(9)]=U[a].value)} (function(a,b,e){var c=window;a&amp;&amp;b&amp;&amp;e&amp;&amp;c.ReportingObserver&amp;&amp;c.fetch&amp;&amp;(new c.ReportingObserver((d,l)=>{d=d[0];if(d?.body?.id===&quot;HeavyAdIntervention&quot;){d=(d.body.message?.indexOf(&quot;network&quot;)||0)>0?e:b;var f=a.search(R);var g;b:{for(g=0;(g=a.indexOf(&quot;ad_signals&quot;,g))>=0&amp;&amp;g<f;){var k=a.charCodeAt(g-1);if(k==38||k==63)if(k=a.charCodeAt(g+10),!k||k==61||k==38||k==35)break b;g+=11}g=-1}k=g;if(k<0)f=null;else{g=a.indexOf(&quot;&amp;&quot;,k);if(g<0||g>f)g=f;f=decodeURIComponent(a.slice(k+11,g!==-1?g:0).replace(/\+/g,&quot; &quot;))}f? (navigator.sendBeacon(&quot;https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&amp;label=&quot;+d),d={i:f,label:d},f=new P,d!=null&amp;&amp;(d.i!=null&amp;&amp;N(f,1,d.i),d.s!=null&amp;&amp;N(f,3,d.s),d.label!=null&amp;&amp;N(f,6,d.label),d.l!=null&amp;&amp;N(f,7,d.l),d.j!=null&amp;&amp;N(f,8,d.j),d.o!=null&amp;&amp;N(f,11,d.o)),Q(h).fence?.reportEvent({eventType:&quot;interaction&quot;,eventData:JSON.stringify(I(f)),destination:[&quot;buyer&quot;]})):c.fetch(`${a}&amp;label=${d}`,{keepalive:!0,method:&quot;get&quot;,mode:&quot;no-cors&quot;});l.disconnect()}},{types:[&quot;intervention&quot;], buffered:!0})).observe()})(T.base_url,T.cpu_label,T.net_label);}).call(this);</script><script id=&quot;googleActiveViewDisplayScript&quot; src=&quot;https://pagead2.googlesyndication.com/pagead/managed/js/activeview/current/ufs_web_display.js&quot;></script><script type=&quot;text/javascript&quot;>osdlfm();</script></body></html>{&quot;uid&quot;:&quot;1&quot;,&quot;hostPeerName&quot;:&quot;https://www.smartbackgroundchecks.com&quot;,&quot;initialGeometry&quot;:&quot;{\&quot;windowCoords_t\&quot;:-16000,\&quot;windowCoords_r\&quot;:-15958,\&quot;windowCoords_b\&quot;:-15986,\&quot;windowCoords_l\&quot;:-16000,\&quot;frameCoords_t\&quot;:585,\&quot;frameCoords_r\&quot;:875,\&quot;frameCoords_b\&quot;:675,\&quot;frameCoords_l\&quot;:147,\&quot;styleZIndex\&quot;:\&quot;auto\&quot;,\&quot;allowedExpansion_t\&quot;:585,\&quot;allowedExpansion_r\&quot;:147,\&quot;allowedExpansion_b\&quot;:5,\&quot;allowedExpansion_l\&quot;:147,\&quot;xInView\&quot;:1,\&quot;yInView\&quot;:1}&quot;,&quot;permissions&quot;:&quot;{\&quot;expandByOverlay\&quot;:false,\&quot;expandByPush\&quot;:false,\&quot;readCookie\&quot;:false,\&quot;writeCookie\&quot;:false}&quot;,&quot;metadata&quot;:&quot;{\&quot;shared\&quot;:{\&quot;sf_ver\&quot;:\&quot;1-0-45\&quot;,\&quot;ck_on\&quot;:1,\&quot;flash_ver\&quot;:\&quot;0\&quot;}}&quot;,&quot;reportCreativeGeometry&quot;:false,&quot;isDifferentSourceWindow&quot;:false,&quot;goog_safeframe_hlt&quot;:{}}" scrolling="no" marginwidth="0" marginheight="0" width="728" height="90" data-is-safeframe="true" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" allow="private-state-token-redemption;attribution-reporting" aria-label="Advertisement" tabindex="0" data-google-container-id="1" style="border: 0px; vertical-align: bottom;"></iframe></div></div><a href="#">x</a></div><ins class="adsbygoogle adsbygoogle-noablate" data-adsbygoogle-status="done" style="display: none !important;" data-ad-status="unfilled"><div id="aswift_0_host" style="border: none; height: 0px; width: 0px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block;"><iframe id="aswift_0" name="aswift_0" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:undefinedpx;height:undefinedpx;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting; run-ad-auction" src="https://googleads.g.doubleclick.net/pagead/ads?gdpr=0&amp;client=ca-pub-****************&amp;output=html&amp;adk=1812271804&amp;adf=3025194257&amp;abgtt=11&amp;lmt=1748958730&amp;plaf=1%3A2%2C7%3A2&amp;plat=1%3A128%2C2%3A128%2C3%3A128%2C4%3A128%2C8%3A64%2C9%3A32776%2C16%3A8388608%2C17%3A32%2C24%3A32%2C25%3A32%2C30%3A1048576%2C32%3A32%2C41%3A32%2C42%3A32&amp;format=0x0&amp;url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;pra=5&amp;wgl=1&amp;aihb=0&amp;asro=0&amp;aifxl=29_18~30_19&amp;aiapm=0.07881135168149486&amp;aiapmi=0.16&amp;aiact=0.7202448128909672&amp;aicct=0.7&amp;ailct=0.509355364951381&amp;aimart=7&amp;uach=WyJXaW5kb3dzIiwiIiwiIiwiIiwiIixudWxsLDAsbnVsbCwiIixudWxsLDBd&amp;dt=1748958711788&amp;bpp=4&amp;bdt=278&amp;idt=170&amp;shv=r20250602&amp;mjsv=m202505290101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3Dc1a7caadd9217a7b%3AT%3D1748958701%3ART%3D1748958701%3AS%3DALNI_MaDAhC6MQr7utSK_CB4ReQeaLId1A&amp;gpic=UID%3D000010df644b10c6%3AT%3D1748958701%3ART%3D1748958701%3AS%3DALNI_MYllcjJgug_wsHwht7hBtA9wlaFGg&amp;eo_id_str=ID%3D78d2f42a76d730fb%3AT%3D1748958701%3ART%3D1748958701%3AS%3DAA-AfjaVKOtJN-wg9L28Iji9yDXV&amp;nras=1&amp;correlator=2405323634073&amp;frm=20&amp;pv=2&amp;u_tz=480&amp;u_his=2&amp;u_h=900&amp;u_w=1440&amp;u_ah=860&amp;u_aw=1440&amp;u_cd=24&amp;u_sd=2&amp;dmc=8&amp;adx=-12245933&amp;ady=-12245933&amp;biw=1022&amp;bih=680&amp;scr_x=0&amp;scr_y=0&amp;eid=31092200%2C95332923%2C95353386%2C95360813%2C95361622%2C95362170%2C31084487%2C31091243%2C95360801&amp;oid=2&amp;pvsid=7780424293932444&amp;tmod=1703081609&amp;uas=0&amp;nvt=1&amp;fsapi=1&amp;fc=1920&amp;brdim=10%2C10%2C10%2C10%2C1440%2C0%2C1050%2C840%2C1037%2C695&amp;vis=1&amp;rsz=%7C%7Cs%7C&amp;abl=NS&amp;fu=33792&amp;bc=31&amp;bz=1.01&amp;td=1&amp;tdf=2&amp;psd=W251bGwsbnVsbCxudWxsLDNd&amp;nt=1&amp;ifi=1&amp;uci=a!1&amp;fsb=1&amp;dtd=19053" data-google-container-id="a!1" tabindex="0" title="Advertisement" aria-label="Advertisement" data-load-complete="true"></iframe></div></ins><script type="text/javascript" id="gtm-jq-ajax-listen" charset="">(function(){function h(b){"undefined"!==typeof jQuery?(k=jQuery,n()):20>b&&setTimeout(h,500)}function n(){k(document).bind("ajaxComplete",function(b,a,f){var c=document.createElement("a");c.href=f.url;var g="/"===c.pathname[0]?c.pathname:"/"+c.pathname,d="?"===c.search[0]?c.search.slice(1):c.search;d=l(d,"\x26","\x3d",!0);var e=l(a.getAllResponseHeaders(),"\n",":");dataLayer.push({event:"ajaxComplete",attributes:{type:f.type||"",url:c.href||"",queryParameters:d,pathname:g||"",hostname:c.hostname||
"",protocol:c.protocol||"",fragment:c.hash||"",statusCode:a.status||"",statusText:a.statusText||"",headers:e,timestamp:b.timeStamp||"",contentType:f.contentType||"",response:a.responseJSON||a.responseXML||a.responseText||""}})})}function l(b,a,f,c){var g={};if(!b||!a||!f)return{};if(b=b.split(a))for(a=0;a<b.length;a++){var d=c?decodeURIComponent(b[a]):b[a],e=d.split(f);d=m(e[0]);e=m(e[1]);d&&e&&(g[d]=e)}return g}function m(b){if(b)return b.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var k;h()})();</script><script type="text/javascript" id="gtm-scroll-tracking" charset="">(function(c){function d(a){if(!(this instanceof d))return new d(a);a=a||{};var b=a.context||"body";"string"===typeof b&&(b=h.querySelector(b));if(!b)throw Error("Unable to find context "+b);this._context=b;this.minHeight=a.minHeight||0;this._marks={};this._tracked={};this._config={percentages:{each:{},every:{}},pixels:{each:{},every:{}},elements:{each:{},every:{}}};a=n(this._checkDepth.bind(this),500);b=this._update.bind(this);var g=n(b,500);c.addEventListener("scroll",a,!0);c.addEventListener("resize",
g);this._artifacts={timer:q(b),resize:g,scroll:a}}function r(a){return a.handlers.map(function(b){return b.bind(this,{data:{depth:a.depth,label:a.label}})})}function p(a){var b=Math.floor(a.numerator/a.n),g;for(g=1;g<=b;g++)a.callback(g*a.n)}function q(a){var b=m();return setInterval(function(){m()!==b&&(a(),b=m())},500)}function m(){var a=h.body,b=h.documentElement;return Math.max(a.scrollHeight,a.offsetHeight,b.clientHeight,b.scrollHeight,b.offsetHeight)}function t(a){a=a.getBoundingClientRect().top;
var b=void 0!==c.pageYOffset?c.pageYOffset:(h.documentElement||h.body.parentNode||h.body).scrollTop;return a+b}function u(){}function n(a,b){var g,e,d,l=null,c=0,f=function(){c=new Date;l=null;d=a.apply(g,e)};return function(){var k=new Date;c||(c=k);var h=b-(k-c);g=this;e=arguments;0>=h?(clearTimeout(l),l=null,c=k,d=a.apply(g,e)):l||(l=setTimeout(f,h));return d}}function v(){var a={},b;for(b in d)a[b]=u;c.ScrollTracker=a}if(c.navigator.userAgent.match(/MSIE [678]/gi))return v();var h=c.document;
d.prototype.destroy=function(){clearInterval(this._artifacts._timer);c.removeEventListener("resize",this._artifacts.resize);c.removeEventListener("scroll",this._artifacts.scroll,!0)};d.prototype.on=function(a,b){var g=this._config;["percentages","pixels","elements"].forEach(function(e){a[e]&&["each","every"].forEach(function(c){a[e][c]&&a[e][c].forEach(function(a){g[e][c][a]=g[e][c][a]||[];g[e][c][a].push(b)})})});this._update()};d.prototype._update=function(){this._calculateMarks();this._checkDepth()};
d.prototype._calculateMarks=function(){function a(a,b){return function(b,c){var g=b.getBoundingClientRect().top-h._context.getBoundingClientRect().top;d({label:a+"["+c+"]",depth:g,handlers:e.elements.every[a]})}}function b(a){return function(a){var b=Math.floor(a*c/100);d({label:String(a)+"%",depth:b,handlers:e.percentages.every[f]})}}function g(a){return function(b){d({label:String(b)+"px",depth:b,handlers:a})}}delete this._marks;this._fromTop=t(this._context);this._marks={};var e=this._config,c=
this._contextHeight(),d=this._addMark.bind(this),h=this,f;if(!(c<this.minHeight)){for(f in e.percentages.every)p({n:Number(f),numerator:100,callback:b(e.percentages.every[f])});for(f in e.pixels.every)p({n:Number(f),numerator:c,callback:g(e.pixels.every[f])});for(f in e.percentages.each){var k=Math.floor(c*Number(f)/100);d({label:f+"%",depth:k,handlers:e.percentages.each[f]})}for(f in e.pixels.each)k=Number(f),d({label:f+"px",depth:k,handlers:e.pixels.each[f]});for(f in e.elements.every)k=[].slice.call(this._context.querySelectorAll(f)),
k.length&&k.forEach(a(f,e.elements.every[f]));for(f in e.elements.each)if(k=this._context.querySelector(f))k=k.getBoundingClientRect().top-h._context.getBoundingClientRect().top,d({label:f,depth:k,handlers:e.elements.each[f]})}};d.prototype._checkDepth=function(){var a=this._marks,b=this._currentDepth(),c;for(c in a)b>=c&&!this._tracked[c]&&(a[c].forEach(function(a){a()}),this._tracked[c]=!0)};d.prototype.reset=function(){this._tracked={};delete this._marks;this.marks={}};d.prototype._contextHeight=
function(){return this._context!==h.body?this._context.scrollHeight-5:this._context.clientHeight-5};d.prototype._currentDepth=function(){var a=this._context;var b=a.offsetHeight;var d="CSS1Compat"===h.compatMode?h.documentElement:h.body;d=d.clientHeight;a=a.getBoundingClientRect();b=Math.max(0,0<a.top?Math.min(b,d-a.top):a.bottom<d?a.bottom:d);this._context.scrollTop?a=this._context.scrollTop+b:(this._context.scrollTop=1,this._context.scrollTop?(this._context.scrollTop=0,a=this._context.scrollTop+
b):a=c.pageYOffset||h.documentElement.scrollTop||h.body.scrollTop||0);return b?a+b:a>=this._fromTop?a:-1};d.prototype._addMark=function(a){var b=a.depth;this._marks[b]=(this._marks[b]||[]).concat(r(a))};c.ScrollTracker=d})(this);
(function(c){function d(){var d=c.ScrollTracker();d.on({percentages:{each:[10,90],every:[25]}},function(c){dataLayer.push({event:"scrollTracking",attributes:{distance:c.data.depth,label:c.data.label}})});delete c.ScrollTracker}"loading"!==document.readyState?d():document.addEventListener("DOMContentLoaded",d)})(window);</script><script type="text/javascript" id="gtm-youtube-tracking" charset="">(function(h,f,l){function n(){"loading"!==h.readyState?m():"addEventListener"in h?p(h,"DOMContentLoaded",m):p(f,"load",m)}function m(){var b=[].slice.call(h.getElementsByTagName("iframe")).concat([].slice.call(h.getElementsByTagName("embed"))),a;for(a=0;a<b.length;a++){var d=q(b[a]);if(d){d=b[a];var e=f.location,c=h.createElement("a");c.href=d.src;c.hostname="www.youtube.com";c.protocol=e.protocol;var g="/"===c.pathname.charAt(0)?c.pathname:"/"+c.pathname;-1<c.search.indexOf("enablejsapi")||(c.search=
(0<c.search.length?c.search+"\x26":"")+"enablejsapi\x3d1");if(!(-1<c.search.indexOf("origin"))&&-1===e.hostname.indexOf("localhost")){var w=e.port?":"+e.port:"";e=e.protocol+"%2F%2F"+e.hostname+w;c.search=c.search+"\x26origin\x3d"+e}"application/x-shockwave-flash"===d.type&&(e=h.createElement("iframe"),e.height=d.height,e.width=d.width,g=g.replace("/v/","/embed/"),d.parentNode.parentNode.replaceChild(e,d.parentNode),d=e);c.pathname=g;d.src!==c.href+c.hash&&(d.src=c.href+c.hash);r(d)}}"addEventListener"in
h&&h.addEventListener("load",x,!0)}function q(b){b=b.src||"";return-1<b.indexOf("youtube.com/embed/")||-1<b.indexOf("youtube.com/v/")?!0:!1}function r(b){var a=YT.get(b.id);a||(a=new YT.Player(b,{}));"undefined"===typeof b.pauseFlag&&(b.pauseFlag=!1,a.addEventListener("onStateChange",function(a){y(a,b)}))}function z(b){var a={};g.events["Watch to End"]&&(a["Watch to End"]=Math.min(b-3,Math.floor(.99*b)));if(g.percentageTracking){var d=[],e;g.percentageTracking.each&&(d=d.concat(g.percentageTracking.each));
if(g.percentageTracking.every){var c=parseInt(g.percentageTracking.every,10),f=100/c;for(e=1;e<f;e++)d.push(e*c)}for(e=0;e<d.length;e++)f=d[e],c=f+"%",f=b*f/100,a[c]=Math.floor(f)}return a}function y(b,a){var d=b.data,e=b.target,c=e.getVideoUrl();c=c.match(/[?&]v=([^&#]*)/)[1];var f=e.getPlayerState(),g=Math.floor(e.getDuration()),h=z(g);g={1:"Play",2:"Pause"};g=g[d];a.playTracker=a.playTracker||{};1!==f||a.timer?(clearInterval(a.timer),a.timer=!1):(clearInterval(a.timer),a.timer=setInterval(function(){var b=
e,d=h,c=a.videoId,g=b.getCurrentTime(),f;b[c]=b[c]||{};for(f in d)d[f]<=g&&!b[c][f]&&(b[c][f]=!0,t(c,f))},1E3));1===d&&(a.playTracker[c]=!0,a.videoId=c,a.pauseFlag=!1);if(!a.playTracker[a.videoId])return!1;if(2===d){if(a.pauseFlag)return!1;a.pauseFlag=!0}u[g]&&t(a.videoId,g)}function t(b,a){var d="https://www.youtube.com/watch?v\x3d"+b,e=f.GoogleAnalyticsObject;if("undefined"===typeof f[v]||g.forceSyntax)if("function"===typeof f[e]&&"function"===typeof f[e].getAll&&2!==g.forceSyntax)f[e]("send","event",
"Videos",a,d);else"undefined"!==typeof f._gaq&&1!==A&&f._gaq.push(["_trackEvent","Videos",a,d]);else f[v].push({event:"youTubeTrack",attributes:{videoUrl:d,videoAction:a}})}function p(b,a,d){if(b.addEventListener)b.addEventListener(a,d);else if(b.attachEvent)b.attachEvent("on"+a,function(a){a.target=a.target||a.srcElement;d.call(b,a)});else if("undefined"===typeof b["on"+a]||null===b["on"+a])b["on"+a]=function(a){a.target=a.target||a.srcElement;d.call(b,a)}}function x(b){b=b.target||b.srcElement;
var a=q(b);"IFRAME"===b.tagName&&a&&-1<b.src.indexOf("enablejsapi")&&-1<b.src.indexOf("origin")&&r(b)}if(!navigator.userAgent.match(/MSIE [67]\./gi)){var g=l||{},A=g.forceSyntax||0,v=g.dataLayerName||"dataLayer",u={Play:!0,Pause:!0,"Watch to End":!0};for(k in g.events)g.events.hasOwnProperty(k)&&(u[k]=g.events[k]);if(f.YT)n();else{var k=h.createElement("script");k.src="//www.youtube.com/iframe_api";l=h.getElementsByTagName("script")[0];l.parentNode.insertBefore(k,l);f.onYouTubeIframeAPIReady=function(b){return function(){b&&
b.apply(this,arguments);n()}}(f.onYouTubeIframeAPIReady)}}})(document,window,{events:{Play:!0,Pause:!0,"Watch to End":!0},percentageTracking:{every:25,each:[10,90]}});</script>      <script type="text/javascript" id="" charset="">(function(){var a=document.createElement("script");a.type="text/javascript";a.async=!0;a.referrerPolicy="unsafe-url";a.src="https://ws.zoominfo.com/pixel/633ef9125a797886caf7797d";var b=document.getElementsByTagName("script")[0];b.parentNode.insertBefore(a,b)})();</script>
      <noscript>
        <img src="https://ws.zoominfo.com/pixel/633ef9125a797886caf7797d" width="1" height="1" style="display: none;" alt="websights">
      </noscript><img src="https://ad-delivery.net/px.gif?ch=2" style="display: none !important; width: 1px !important; height: 1px !important;"><img src="https://ad.doubleclick.net/favicon.ico?ad=300x250&amp;ad_box_=1&amp;adnet=1&amp;showad=1&amp;size=250x250" style="display: none !important; width: 1px !important; height: 1px !important;"><img src="https://ad-delivery.net/px.gif?ch=1&amp;e=0.6439151321095268" style="display: none !important; width: 1px !important; height: 1px !important;"><iframe marginwidth="0" marginheight="0" scrolling="no" frameborder="0" id="1fc513945d24cb8" width="0" height="0" src="about:blank" name="__pb_locator__" style="display: none; height: 0px; width: 0px; border: 0px;"></iframe><iframe name="googlefcPresent" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><script type="text/javascript" id="" charset="">google_tag_manager["rm"]["98301455"](24);</script><script id="" text="" charset="" type="text/javascript" src="https://boot.pbstck.com/v1/user-sessions/621169b6-0529-464c-8712-1e799d75704d"></script><script id="" text="" charset="" type="text/javascript" src="https://boot.pbstck.com/v1/tag/621169b6-0529-464c-8712-1e799d75704d"></script><script src="https://a.ad.gt/api/v1/u/matches/405?_it=amazon"></script><img height="1" width="1" src="https://ids.ad.gt/api/v1/halo_match?id=AU1D-0100-001748958676-TSKA018J-B6Y9&amp;halo_id=060ixdej2g5989f999a999f9c9b97996666uokmwsqy646o666e666o6i6g626600" alt="" style="display: none;"><img height="1" width="1" src="https://ids4.ad.gt/api/v1/ip_match?id=AU1D-0100-001748958676-TSKA018J-B6Y9" alt="" style="display: none;"><script type="text/javascript" src="https://pixels.ad.gt/api/v1/getpixels?tagger_id=3995f7c90161f5bb056ab57f9b6e4344&amp;url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;code='none'" async=""></script><iframe src="https://s.amazon-adsystem.com/iu3?cm3ppd=1&amp;d=dtb-pub&amp;csif=t&amp;dl=n-minuteMedia_n-adMediaV1_rx_n-MediaNet_n-colossusMedia_cnv_n-opera3pb_n-smaato_n-sharethrough_n-onetag_n-simpli.fi_n-nativo_n-Rise_3lift" style="display: none;"></iframe><script src="https://a.ad.gt/api/v1/u/matches/788?_it=tag"></script><img height="1" width="1" src="https://ids.ad.gt/api/v1/halo_match?id=AU1D-0100-001748958676-TSKA018J-B6Y9&amp;halo_id=060ixdej2g5989f999a999f9c9b97996666uokmwsqy646o666e666o6i6g626600" alt="" style="display: none;"><img height="1" width="1" src="https://ids4.ad.gt/api/v1/ip_match?id=AU1D-0100-001748958676-TSKA018J-B6Y9" alt="" style="display: none;"><script type="text/javascript" src="https://pixels.ad.gt/api/v1/getpixels?tagger_id=3995f7c90161f5bb056ab57f9b6e4344&amp;url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;code='none'" async=""></script><script type="text/javascript" src="https://pixels.ad.gt/api/v1/getpixels?tagger_id=c48b966855ebcf2c57ae70f609041297&amp;url=https%3A%2F%2Fwww.smartbackgroundchecks.com%2Fphone%2F5619324217&amp;code='none'" async=""></script><iframe src="https://proton.ad.gt/join-ad-interest-groups.html" allow="join-ad-interest-group; attribution-reporting" sandbox="allow-scripts allow-same-origin" style="display: none;"></iframe><iframe src="https://proton.ad.gt/join-ad-interest-groups.html" allow="join-ad-interest-group; attribution-reporting" sandbox="allow-scripts allow-same-origin" style="display: none;"></iframe><iframe src="https://proton.ad.gt/join-ad-interest-groups.html" allow="join-ad-interest-group; attribution-reporting" sandbox="allow-scripts allow-same-origin" style="display: none;"></iframe><iframe name="__tcfapiLocator" src="about:blank" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><iframe name="googlefcInactive" src="about:blank" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe><iframe name="googlefcLoaded" src="about:blank" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;"></iframe></body><iframe sandbox="allow-scripts allow-same-origin" id="1337fdcf2aa84094" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://public.servenobid.com/sync.html">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="1348669d803cc6888" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://visitor.omnitagjs.com/visitor/isync?uid=19340f4f097d16f41f34fc0274981ca4">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="1356d420929eebef" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://sync.cootlogix.com/api/sync/iframe/?cid=&amp;gdpr=0&amp;gdpr_consent=&amp;us_privacy=">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="13620a9679705866" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://acdn.adnxs.com/dmp/async_usersync.html">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="1375d98a820e7c0c" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://ads.pubmatic.com/AdServer/js/user_sync.html?kdntuid=1&amp;p=161102">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="138c48c49737b709" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://eus.rubiconproject.com/usync.html">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="139f33fc6d456d678" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://onetag-sys.com/usync/?cb=1748958715414">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="14085302f65077648" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://js-sec.indexww.com/um/ixmatch.html">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="14138f1a29dfce01" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://u.openx.net/w/1.0/pd?ph=2d1251ae-7f3a-47cf-bd2a-2f288854a0ba">
    </iframe><iframe sandbox="allow-scripts allow-same-origin" id="1424c5429e32bf5e8" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://csync.smilewanted.com">
    </iframe><iframe id="google_esf" name="google_esf" src="https://googleads.g.doubleclick.net/pagead/html/r20250602/r20190131/zrt_lookup_fy2021.html" style="display: none;"></iframe><iframe name="goog_topics_frame" src="https://securepubads.g.doubleclick.net/static/topics/topics_frame.html" style="display: none;"></iframe></html>