swagger: "2.0"
schemes:
  - https
host: api.ltesocks.io
basePath: /v2
info:
  version: 0.1.0
  title: LTESocks API
  x-logo:
    url: "logo.png"
    altText: LTESocks Logo
tags:
  - name: user
    description: User related endpoints
  - name: port
    description: Port related endpoints
  - name: payment
    description: Payment related endpoints
  - name: general
    description: General endpoints
securityDefinitions:
  Token:
    type: apiKey
    in: header
    name: Authorization
paths:
  /user:
    get:
      summary: Get user
      produces:
        - application/json
      responses:
        "200":
          schema:
            $ref: "#/definitions/User"
      tags:
        - user
  /user/preferences:
    post:
      summary: Update preferences
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: User preferences object
          required: true
          schema:
            $ref: '#/definitions/UserPreferences'
      responses:
        "200":
          schema:
            $ref: "#/definitions/User"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - user
  /ports:
    get:
      summary: Get ports
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "#/definitions/Port"
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                example: 20
              total:
                type: integer
                example: 7
      tags:
        - port
    post:
      summary: Get ports
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Pagination parameters
          required: true
          schema:
            type: object
            properties:
              plan:
                type: string
                example: O2
                description: "Plan ID (exact) or name (can match partially)"
              countryCode:
                type: string
                example: GB
              tags:
                type: array
                maxLength: 30
                items:
                  type: string
                  maxLength: 50
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                maximum: 100
                example: 20
      responses:
        "200":
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "#/definitions/Port"
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                example: 20
              total:
                type: integer
                example: 7
      tags:
        - port
  "/ports/{id}":
    get:
      summary: Get port
      produces:
        - application/json
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
      tags:
        - port
    delete:
      summary: Delete port
      responses:
        "200":
          schema:
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/delete":
    post:
      summary: Delete port
      responses:
        "200":
          schema:
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/log":
    get:
      summary: Get port log
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "#/definitions/PortLog"
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                example: 20
              total:
                type: integer
                example: 7
      tags:
        - port
    post:
      summary: Get port log
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Pagination parameters
          required: true
          schema:
            type: object
            properties:
              from:
                type: string
                format: rfc3339
                example: 2022-04-02T00:40:44
              to:
                type: string
                format: rfc3339
                example: 2022-04-02T00:40:44
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                maximum: 100
                example: 20
      responses:
        "200":
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "#/definitions/PortLog"
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                example: 20
              total:
                type: integer
                example: 7
      tags:
        - port
  "/ports/{id}/vpn":
    get:
      summary: Get VPN config
      produces:
        - application/zip
      responses:
        "200":
      tags:
        - port
    post:
      summary: Get VPN config
      description: Responds with VPN config tied to the given credentials. Credentials must be in the port credentials list
      produces:
        - application/zip
      parameters:
        - in: body
          name: body
          description: Credentials
          required: true
          schema:
            type: object
            properties:
              login:
                type: string
                example: login
              password:
                type: string
                example: password
      responses:
        "200":
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  /ports/order:
    post:
      summary: Order port
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Plan parameters
          required: true
          schema:
            type: object
            properties:
              plan:
                type: string
                example: 5349b4ddd2781d08c09890f3
                description: Plan ID
              tarification:
                $ref: "#/definitions/PlanTarification"
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/plan":
    post:
      summary: Update plan
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Plan parameters
          required: true
          schema:
            type: object
            properties:
              plan:
                type: string
                example: 5349b4ddd2781d08c09890f3
                description: Plan ID
              tarification:
                $ref: "#/definitions/PlanTarification"
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/tags":
    post:
      summary: Update tags
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Tags
          required: true
          schema:
            type: object
            properties:
              tags:
                type: array
                maxLength: 30
                items:
                  type: string
                  maxLength: 50
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/autorenew":
    post:
      summary: Update auto renew
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Autorenew parameters
          required: true
          schema:
            type: object
            properties:
              autoRenew:
                type: boolean
                example: true
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/signature":
    post:
      summary: Update signature
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Signature parameters
          required: true
          schema:
            type: object
            properties:
              signature:
                type: string
                example: Linux
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/credentials":
    post:
      summary: Update credentials
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Credentials parameters
          required: true
          schema:
            $ref: "#/definitions/PortCredentials"
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/autoreset":
    post:
      summary: Update auto reset interval
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Auto reset interval parameters
          required: true
          schema:
            type: object
            properties:
              autoResetInterval:
                type: int
                example: 600
                description: Auto reset interval in seconds, 0 means disabled
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  "/ports/{id}/reset":
    post:
      summary: Reset port
      produces:
        - application/json
      responses:
        "200":
          schema:
            $ref: "#/definitions/Port"
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - port
  /payments:
    get:
      summary: Get payments history
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "#/definitions/PaymentHistoryRecord"
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                example: 20
              total:
                type: integer
                example: 7
      tags:
        - payment
    post:
      summary: Get payments history
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Pagination parameters
          required: true
          schema:
            type: object
            properties:
              from:
                type: string
                format: rfc3339
                example: 2022-04-02T00:40:44
              to:
                type: string
                format: rfc3339
                example: 2022-04-02T00:40:44
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                maximum: 100
                example: 20
      responses:
        "200":
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: "#/definitions/PaymentHistoryRecord"
              page:
                type: integer
                example: 1
              pageSize:
                type: integer
                example: 20
              total:
                type: integer
                example: 7
      tags:
        - payment
  /payments/currencies:
    get:
      summary: Get currencies
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              currencies:
                type: array
                items:
                  type: string
                  example: usd
      tags:
        - payment
  /payments/request:
    post:
      summary: Request payment
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: body
          name: body
          description: Payment request parameters
          required: true
          schema:
            type: object
            properties:
              amount:
                type: integer
                example: 10000
                description: Payment amount in cents (always USD)
              currency:
                type: string
                example: btc
                description: Currency in which payment will be paid, given amount will be converted automatically from USD
      responses:
        "200":
          schema:
            type: object
            properties:
              url:
                type: string
                example: "https://exmaple.com/payment"
                description: Payment page URL
        "400":
          schema:
            $ref: "#/definitions/Error"
      tags:
        - payment
  /plans:
    get:
      summary: Get plans
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              plans:
                type: array
                items:
                  $ref: "#/definitions/Plan"
      tags:
        - general
  /servers:
    get:
      summary: Get servers
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              vpnServers:
                type: array
                items:
                  $ref: "#/definitions/Server"
              proxyServers:
                type: array
                items:
                  $ref: "#/definitions/Server"
              proxyProtocols:
                type: array
                items:
                  type: string
                  enum:
                    - http
                    - https
                    - socks5
      tags:
        - general
  /signatures:
    get:
      summary: Get signatures
      produces:
        - application/json
      responses:
        "200":
          schema:
            type: object
            properties:
              signatures:
                type: array
                items:
                 type: string
                 example: Windows
      tags:
        - general
definitions:
  Error:
    type: object
    properties:
      error:
        type: string
        example: example error
        description: Error text representation
  GeoIP:
    type: object
    properties:
      continentCode:
        type: string
        example: NA
      continentName:
        type: string
        example: North America
      countryCode2:
        type: string
        example: US
      countryCode3:
        type: string
        example: United States
      stateProv:
        type: string
      district:
        type: string
      city:
        type: string
      zipcode:
        type: string
      latitude:
        type: string
        example: "38.89037"
      longitude:
        type: string
        example: "-77.03196"
      isp:
        type: string
        example: T-Mobile USA, Inc.
  Server:
    type: object
    properties:
      host:
        type: string
        example: vpn1.ltesocks.io
      countryCode:
        type: string
        example: NL
  Plan:
    type: object
    properties:
      id:
        type: string
        example: 5349b4ddd2781d08c09890f3
      name:
        type: string
        example: UK Three
      available:
        type: boolean
        example: true
      description:
        type: string
        example: Plan description
      countryCode:
        type: string
        example: UK
      vpnAccess:
        type: boolean
        example: true
      tarifications:
        type: array
        items:
          $ref: "#/definitions/PlanTarification"
  PlanTarification:
    type: object
    properties:
      time:
        type: integer
        example: 2592000
        description: Plan duration time in seconds
      traffic:
        type: integer
        example: 51200
        description: Plan traffic amount in megabytes
      price:
        type: integer
        example: 14500
        description: Plan price in cents
  User:
    type: object
    properties:
      login:
        type: string
        example: user
        description: User login
      email:
        type: string
        format: email
        example: <EMAIL>
        description: User email address
      balance:
        type: integer
        example: 10000
        description: User balance in cents (100$ = 10000)
      portsCount:
        type: integer
        example: 3
        description: Total ports number
      portsLimit:
        type: integer
        example: 10
        description: Maximum allowed ports number
      preferences:
        type: object
        $ref: "#/definitions/UserPreferences"
  UserPreferences:
    type: object
    properties:
      vpnServer:
        type: string
        format: host
        example: vpn1.ltesocks.io
        description: Default server hostname to connect via OpenVPN
      proxyServer:
        type: string
        format: host
        example: ap1.ltesocks.io"
        description: Default server hostname to connect via proxy
      proxyProtocol:
        type: string
        example: socks5
        description: Default proxy protocol
        enum:
          - http
          - https
          - socks5
  Port:
    type: object
    properties:
      port:
        type: string
        example: 10000
        description: Port number
      ip:
        type: string
        example: *******
        description: Port current IP address (0.0.0.0 if not available)
      geoip:
        type: object
        $ref: "#/definitions/GeoIP"
      status:
        type: string
        example: active
        description: Port status
        enum:
        - active
        - reset
        - suspended
        - disconnected
        - outoftraffic
      resetToken:
        type: string
        example: 6015510d206e4d7b023e5f978a56e3cf49d7e79bc26cf28c69b75314e098a61b
        description: Port reset token
      signature:
        type: string
        example: Windows
        description: Port signature
      vpnAccess:
        type: boolean
        example: true
        description: Indicates whether port has access to the VPN server
      autoResetInterval:
        type: integer
        exmaple: 300
        description: Port auto reset interval in seconds, 0 means disabled
      tags:
        type: array
        maxLength: 30
        description: Port tags
        items:
          type: string
          minLength: 1
          maxLength: 50
          pattern: '(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])'
      plan:
        type: object
        $ref: "#/definitions/PortPlan"
      credentials:
        type: object
        $ref: "#/definitions/PortCredentials"
  PortPlan:
    type: object
    properties:
      id:
        type: string
        example: 5349b4ddd2781d08c09890f3
        description: Plan ID
      name:
        type: string
        example: UK Three
        description: Plan title
      countryCode:
        type: string
        example: "UK"
        description: Plan country code
      enabled:
        type: boolean
        example: true
        description: Indicates whether plan is active
      autoRenew:
        type: boolean
        example: true
        description: Indicates whether plan auto renewal is active
      activatedAt:
        type: string
        format: rfc3339
        example: 2022-04-02T05:57:29.054Z
        description: Plan activation time
      expiresAt:
        type: string
        format: rfc3339
        example: 2022-05-02T05:57:29.054Z
        description: Plan expiration time
      trafficRemains:
        type: integer
        example: 51200
        description: Remaining traffic amount in megabytes
      tarification:
        type: object
        $ref: "#/definitions/PlanTarification"
  PortCredentials:
    type: object
    properties:
      ip:
        type: array
        maxLength: 20
        items:
          type: string
          example: *******
          description: IP address to allow access from
      password:
        type: array
        maxLength: 20
        items:
          type: object
          properties:
            login:
              type: string
              example: login
            password:
              type: string
              example: password
  PortLog:
    type: object
    properties:
      action:
        type: string
        example: update
      cause:
        type: string
        example: admin
      source:
        type: string
        example: adminDashboard
      extra:
        type: string
        example: extra info
      createdAt:
        type: string
        format: rfc3339
        example: 2022-04-03T01:13:50.182Z
  PaymentHistoryRecord:
    type: object
    properties:
      date:
        type: string
        format: rfc3339
        example: 2022-04-03T01:13:50.182Z
      status:
        type: string
        enum:
          - success
          - failed
      port:
        type: string
        example: 10000
        description: Port number
      amount:
        type: integer
        example: 10000
        description: Payment amount in cents
      balance:
        type: integer
        example: 25000
        description: Balance at the time of payment
      description:
        type: string
        example: Port 10000 subscription fee
        description: Payment description
