#!/usr/bin/env python3
"""
瞬时加载测试 - 使用最激进的优化策略
"""

import time
import logging
import os
import threading
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from bs4 import BeautifulSoup
import csv
import re

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('instant_load_test.log', mode='w', encoding='utf-8')
    ]
)

def find_chrome_path():
    """查找Chrome浏览器路径"""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"找到Chrome浏览器: {path}")
            return path
    
    logging.warning("未找到Chrome浏览器")
    return None

def instant_stop_loading(driver, delay=1):
    """1秒后立即停止页面加载"""
    def stop():
        time.sleep(delay)
        try:
            driver.stop_loading()
            logging.info(f"已在 {delay} 秒后强制停止页面加载")
        except:
            pass
    
    thread = threading.Thread(target=stop)
    thread.daemon = True
    thread.start()

def check_page_content_ready(driver, min_content_length=5000):
    """检查页面内容是否已经可用"""
    try:
        html = driver.html
        if len(html) >= min_content_length:
            logging.info(f"页面内容已就绪，长度: {len(html)} 字符")
            return True, html
        return False, html
    except:
        return False, ""

def instant_page_load(driver, url, max_wait=2):
    """瞬时页面加载，最多等待2秒"""
    try:
        logging.info(f"开始瞬时加载: {url}")
        start_time = time.time()
        
        # 启动0.3秒后强制停止加载
        instant_stop_loading(driver, 0.3)

        # 开始加载页面
        try:
            driver.get(url)
        except:
            pass  # 忽略所有加载错误

        # 每0.2秒检查一次页面内容是否就绪
        for i in range(int(max_wait * 5)):
            time.sleep(0.2)
            ready, html = check_page_content_ready(driver)
            if ready:
                load_time = time.time() - start_time
                logging.info(f"页面内容就绪，耗时: {load_time:.3f}秒")
                return True
        
        # 即使内容不够也继续
        load_time = time.time() - start_time
        logging.info(f"达到最大等待时间，耗时: {load_time:.3f}秒，继续处理...")
        return True
        
    except Exception as e:
        logging.error(f"页面加载失败: {e}")
        return False

def find_background_report_instant(driver):
    """瞬时查找背景报告链接"""
    try:
        start_time = time.time()
        logging.info("开始瞬时查找背景报告按钮...")
        
        # 立即获取页面源码
        page_source = driver.html
        parse_time = time.time() - start_time
        logging.info(f"获取页面源码耗时: {parse_time:.3f}秒，长度: {len(page_source)} 字符")
        
        # 快速检查页面内容
        if len(page_source) < 1000:
            logging.warning("页面源码太短，可能未加载完成，但继续尝试...")
        
        # 使用BeautifulSoup快速解析
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 方法1: 多种class组合查找
        button_classes = [
            'btn btn-primary btn-sm btn-block text-center',
            'btn btn-primary',
            'btn-primary',
            'btn'
        ]

        target_buttons = []
        for btn_class in button_classes:
            buttons = soup.find_all('a', class_=btn_class)
            target_buttons.extend(buttons)

        # 去重
        unique_buttons = []
        seen_hrefs = set()
        for button in target_buttons:
            href = button.get('href', '')
            if href and href not in seen_hrefs:
                unique_buttons.append(button)
                seen_hrefs.add(href)

        logging.info(f"找到 {len(unique_buttons)} 个候选按钮")

        # 优先查找包含特定文本的按钮
        for button in unique_buttons:
            button_text = button.get_text().strip()
            href = button.get('href', '')

            if any(text in button_text for text in ['Open Free Background Report', 'Background Report', 'View Report']):
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href

                total_time = time.time() - start_time
                logging.info(f"✅ 找到目标按钮，总耗时: {total_time:.3f}秒")
                return True, href

        # 如果没找到特定文本，查找包含/people/的链接
        for button in unique_buttons:
            href = button.get('href', '')
            if '/people/' in href:
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href

                total_time = time.time() - start_time
                logging.info(f"✅ 找到people链接，总耗时: {total_time:.3f}秒")
                return True, href
        
        # 方法2: 快速文本搜索
        if 'Open Free Background Report' in page_source:
            logging.info("在页面源码中发现目标文本，尝试提取链接...")
            
            # 使用正则表达式快速提取链接
            import re
            pattern = r'href="([^"]*people[^"]*)"[^>]*>.*?Open Free Background Report'
            matches = re.search(pattern, page_source, re.DOTALL | re.IGNORECASE)
            
            if matches:
                href = matches.group(1)
                if href.startswith('/'):
                    base_url = f"{driver.url.split('/')[0]}//{driver.url.split('/')[2]}"
                    href = base_url + href
                
                total_time = time.time() - start_time
                logging.info(f"✅ 通过正则表达式找到链接，总耗时: {total_time:.3f}秒")
                return True, href
        
        total_time = time.time() - start_time
        logging.warning(f"❌ 未找到目标按钮，总耗时: {total_time:.3f}秒")
        return False, None
        
    except Exception as e:
        total_time = time.time() - start_time
        logging.error(f"查找过程出错，耗时: {total_time:.3f}秒，错误: {e}")
        return False, None

def extract_person_info(soup):
    """提取个人基本信息"""
    try:
        person_info = {}

        # 提取姓名 - 从标题中获取
        title = soup.title.string if soup.title else ""
        if title:
            # 从标题中提取姓名，格式通常是 "Name - City, State Phone - Public Record"
            name_match = re.match(r'^([^-]+)', title.strip())
            if name_match:
                person_info['姓名'] = name_match.group(1).strip()

        # 提取年龄 - 从h2标题中查找 "60 years old"
        h2_elements = soup.find_all('h2', class_='h2Title')
        for h2 in h2_elements:
            h2_text = h2.get_text()
            age_match = re.search(r'(\d+)\s*years?\s*old', h2_text, re.IGNORECASE)
            if age_match:
                age_value = age_match.group(1)
                # 确保年龄是合理的数字（1-120之间）
                if age_value.isdigit() and 1 <= int(age_value) <= 120:
                    person_info['年龄'] = age_value
                    break

        # 如果h2中没找到，再从整个页面查找
        if '年龄' not in person_info:
            page_text = soup.get_text()
            # 查找所有年龄模式，选择最合理的
            age_matches = re.findall(r'(\d+)\s*years?\s*old', page_text, re.IGNORECASE)
            for age_value in age_matches:
                if age_value.isdigit() and 1 <= int(age_value) <= 120:
                    person_info['年龄'] = age_value
                    break

        # 提取手机号码 - 从标题中获取
        if title:
            phone_match = re.search(r'\((\d{3})\)\s*(\d{3})-(\d{4})', title)
            if phone_match:
                person_info['手机'] = f"({phone_match.group(1)}){phone_match.group(2)}-{phone_match.group(3)}"

        # 提取地址 - 从标题中获取
        if title:
            # 查找城市和州的信息
            location_match = re.search(r'-\s*([^-]+),\s*([A-Z]{2})', title)
            if location_match:
                person_info['地址'] = f"{location_match.group(1).strip()}, {location_match.group(2)}"

        logging.info(f"提取到个人信息: {person_info}")
        return person_info

    except Exception as e:
        logging.error(f"提取个人信息失败: {e}")
        return {}

def extract_relatives_info(soup, min_age=40):
    """提取亲属信息，只保留年龄>=min_age的亲属"""
    try:
        relatives = []

        # 查找亲属信息区域 - 使用string参数替代text
        relatives_section = soup.find(string=re.compile(r'Relatives Found', re.IGNORECASE))
        if not relatives_section:
            logging.warning("未找到亲属信息区域")
            return relatives

        # 找到包含亲属信息的父元素
        relatives_container = relatives_section.parent
        while relatives_container and relatives_container.name != 'div':
            relatives_container = relatives_container.parent

        if not relatives_container:
            logging.warning("未找到亲属信息容器")
            return relatives

        # 方法1: 查找所有包含年龄信息的div元素
        card_blocks = relatives_container.find_all('div', class_='card-block')

        for card in card_blocks:
            card_text = card.get_text()

            # 查找链接和年龄信息
            link = card.find('a', class_='link-underline')
            small_tag = card.find('small')

            if link and small_tag:
                name = link.get_text().strip()
                age_text = small_tag.get_text().strip()

                # 提取年龄数字
                age_match = re.search(r'(\d+)\s*years?\s*old', age_text, re.IGNORECASE)
                if age_match:
                    age = int(age_match.group(1))

                    # 只保留年龄>=min_age的亲属
                    if age >= min_age and name:
                        relative_info = {
                            '姓名': name,
                            '年龄': age,
                            '关系': '亲属',
                            '链接': link.get('href', '')
                        }
                        relatives.append(relative_info)
                        logging.info(f"找到符合条件的亲属: {name}, {age}岁")
                    else:
                        logging.info(f"跳过年龄不符的亲属: {name}, {age}岁 (< {min_age})")

        # 如果上面的方法没找到，尝试正则表达式方法
        if not relatives:
            text_content = relatives_container.get_text()
            relative_pattern = r'([A-Za-z\s]+)\s+(\d+)\s*years?\s*old'
            matches = re.findall(relative_pattern, text_content, re.IGNORECASE)

            for name, age in matches:
                age = int(age)
                name = name.strip()

                # 只保留年龄>=min_age的亲属
                if age >= min_age and name and len(name) > 2:  # 过滤掉太短的名字
                    relative_info = {
                        '姓名': name,
                        '年龄': age,
                        '关系': '亲属'
                    }
                    relatives.append(relative_info)
                    logging.info(f"找到符合条件的亲属: {name}, {age}岁")
                else:
                    logging.info(f"跳过年龄不符的亲属: {name}, {age}岁 (< {min_age})")

        logging.info(f"共找到 {len(relatives)} 个符合条件的亲属（年龄>={min_age}）")
        return relatives

    except Exception as e:
        logging.error(f"提取亲属信息失败: {e}")
        return []

def click_relative_link_and_extract(driver, relative_name, soup):
    """通过点击链接访问亲属页面并提取详细信息"""
    try:
        logging.info(f"开始查找并点击亲属链接: {relative_name}")

        # 在当前页面中查找该亲属的链接
        relative_links = soup.find_all('a', class_='link-underline')
        target_link = None

        for link in relative_links:
            link_text = link.get_text().strip()
            if relative_name.lower() in link_text.lower() or link_text.lower() in relative_name.lower():
                target_link = link
                break

        if not target_link:
            logging.warning(f"未找到亲属链接: {relative_name}")
            return {}

        # 获取链接的href
        href = target_link.get('href', '')
        if not href:
            logging.warning(f"亲属链接无效: {relative_name}")
            return {}

        # 构造完整URL
        if href.startswith('/'):
            full_url = 'https://www.smartbackgroundchecks.com' + href
        else:
            full_url = href

        logging.info(f"找到亲属链接: {relative_name} -> {full_url}")

        # 使用JavaScript点击链接（模拟真实用户行为）
        try:
            # 先滚动到链接位置
            driver.run_js(f"document.querySelector('a[href=\"{href}\"]').scrollIntoView();")
            time.sleep(0.2)

            # 点击链接
            driver.run_js(f"document.querySelector('a[href=\"{href}\"]').click();")
            logging.info(f"已点击亲属链接: {relative_name}")

            # 平衡速度和完整性的等待时间
            time.sleep(0.8)

            # 等待新页面内容加载完成 - 平衡速度和完整性
            max_wait = 6
            for i in range(max_wait):
                try:
                    current_url = driver.url
                    if full_url.split('/')[-1] in current_url:
                        # 页面已跳转，检查内容是否加载完成
                        page_source = driver.html
                        if len(page_source) > 5000:  # 适中的内容长度要求
                            break
                except:
                    pass
                time.sleep(0.5)  # 平衡的检查间隔

            # 获取新页面内容
            page_source = driver.html
            new_soup = BeautifulSoup(page_source, 'html.parser')

            # 提取亲属详细信息
            relative_details = extract_person_info(new_soup)
            logging.info(f"通过点击链接提取到详细信息: {relative_name} - {relative_details}")

            # 返回上一页
            driver.back()
            time.sleep(0.8)  # 平衡返回等待时间
            logging.info(f"已返回上一页")

            return relative_details

        except Exception as e:
            logging.error(f"点击链接失败: {relative_name}, 错误: {e}")
            return {}

    except Exception as e:
        logging.error(f"处理亲属链接失败 {relative_name}: {e}")
        return {}

def save_to_csv(person_info, relatives_info, filename='background_report_data.csv'):
    """保存信息到CSV文件"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['类型', '姓名', '年龄', '手机', '地址', '关系', '链接']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入个人信息
            person_row = {
                '类型': '本人',
                '姓名': person_info.get('姓名', ''),
                '年龄': person_info.get('年龄', ''),
                '手机': person_info.get('手机', ''),
                '地址': person_info.get('地址', ''),
                '关系': '本人',
                '链接': ''
            }
            writer.writerow(person_row)

            # 写入亲属信息
            logging.info(f"准备写入 {len(relatives_info)} 个亲属信息到CSV")
            for i, relative in enumerate(relatives_info):
                relative_row = {
                    '类型': '亲属',
                    '姓名': relative.get('姓名', ''),
                    '年龄': relative.get('年龄', ''),
                    '手机': relative.get('手机', ''),
                    '地址': relative.get('地址', ''),
                    '关系': relative.get('关系', '亲属'),
                    '链接': relative.get('链接', '')
                }
                writer.writerow(relative_row)
                logging.info(f"已写入第 {i+1} 个亲属: {relative.get('姓名', '')}, {relative.get('年龄', '')}岁, 手机: {relative.get('手机', '无')}, 地址: {relative.get('地址', '无')}")

        logging.info(f"数据已保存到 {filename}，共 {1 + len(relatives_info)} 行数据")
        return True

    except Exception as e:
        logging.error(f"保存CSV文件失败: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("开始瞬时页面加载测试")
    logging.info("=" * 60)
    
    # 查找浏览器
    chrome_path = find_chrome_path()
    if not chrome_path:
        logging.error("无法找到Chrome浏览器")
        return
    
    # 创建最激进的浏览器配置
    try:
        options = ChromiumOptions()
        options.set_paths(browser_path=chrome_path)
        
        # 极速文本加载参数配置 - 只加载文字内容
        instant_args = [
            "--no-first-run",
            "--disable-gpu",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-web-security",
            "--disable-extensions",
            "--disable-images",  # 禁用图片
            "--disable-javascript",  # 禁用JS
            "--disable-plugins",
            "--disable-background-timer-throttling",
            "--aggressive-cache-discard",
            "--disable-background-networking",
            "--disable-sync",
            "--disable-translate",
            "--disable-ipc-flooding-protection",
            "--disable-client-side-phishing-detection",
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-domain-reliability",
            "--disable-features=TranslateUI,BlinkGenPropertyTrees,VizDisplayCompositor",
            "--page-load-strategy=none",
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars",
            "--disable-notifications",
            "--disable-popup-blocking",
            # 新增资源过滤参数
            "--disable-background-mode",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=VizDisplayCompositor",
            "--no-default-browser-check",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-component-extensions-with-background-pages",
            "--disable-field-trial-config",
            "--disable-back-forward-cache"
        ]

        # 设置资源过滤偏好
        prefs = {
            "profile.default_content_setting_values": {
                "images": 2,  # 禁用图片
                "plugins": 2,  # 禁用插件
                "popups": 2,  # 禁用弹窗
                "geolocation": 2,  # 禁用地理位置
                "notifications": 2,  # 禁用通知
                "media_stream": 2,  # 禁用媒体流
            },
            "profile.managed_default_content_settings": {
                "images": 2
            }
        }
        options.set_pref("prefs", prefs)
        
        for arg in instant_args:
            options.set_argument(arg)
            
        logging.info("瞬时加载浏览器选项配置完成")
        
    except Exception as e:
        logging.error(f"配置浏览器选项失败: {e}")
        return
    
    # 启动浏览器并测试
    try:
        total_start = time.time()
        
        logging.info("正在启动浏览器...")
        browser_start = time.time()
        driver = ChromiumPage(addr_or_opts=options)
        browser_time = time.time() - browser_start
        logging.info(f"浏览器启动耗时: {browser_time:.3f}秒")
        
        # 设置极短的超时
        try:
            driver.set.timeouts(page_load=1)  # 1秒超时
            logging.info("设置页面加载超时为1秒")
        except Exception as e:
            logging.warning(f"设置超时失败: {e}")
        
        # 瞬时页面加载
        target_url = "https://www.smartbackgroundchecks.com/phone/5619324217"
        if instant_page_load(driver, target_url, max_wait=2):
            
            # 快速检查Cloudflare
            cf_start = time.time()
            page_source = driver.html
            has_cloudflare = any(keyword in page_source.lower() for keyword in ["cloudflare", "just a moment", "checking your browser"])
            cf_time = time.time() - cf_start
            logging.info(f"Cloudflare检测耗时: {cf_time:.3f}秒")
            
            if has_cloudflare:
                logging.info("检测到Cloudflare保护，开始绕过...")
                bypass_start = time.time()
                cf_bypasser = CloudflareBypasser(driver)
                cf_bypasser.bypass()
                bypass_time = time.time() - bypass_start
                logging.info(f"Cloudflare绕过耗时: {bypass_time:.3f}秒")
            
            # 瞬时查找背景报告
            logging.info("=" * 50)
            button_found, button_url = find_background_report_instant(driver)
            
            if button_found:
                logging.info(f"✅ 成功找到背景报告按钮!")
                logging.info(f"目标链接: {button_url}")
                
                # 瞬时访问新页面
                logging.info("开始瞬时访问背景报告页面...")
                if instant_page_load(driver, button_url, max_wait=2):
                    
                    # 获取新页面信息
                    time.sleep(0.3)  # 极短等待
                    new_page_source = driver.html
                    soup = BeautifulSoup(new_page_source, 'html.parser')
                    new_title = soup.title.string if soup.title else "无标题"

                    logging.info(f"新页面标题: {new_title}")
                    logging.info(f"新页面源码长度: {len(new_page_source)} 字符")

                    # 保存新页面源码
                    with open('instant_background_report.html', 'w', encoding='utf-8') as f:
                        f.write(new_page_source)
                    logging.info("背景报告页面源码已保存到 instant_background_report.html")

                    # 提取个人信息和亲属信息
                    logging.info("=" * 50)
                    logging.info("开始提取个人信息和亲属信息...")

                    # 提取个人基本信息
                    person_info = extract_person_info(soup)

                    # 提取亲属信息（只保留年龄>=40的）
                    relatives_info = extract_relatives_info(soup, min_age=40)

                    # 通过点击链接访问每个亲属的页面获取详细信息
                    logging.info("=" * 50)
                    logging.info("开始通过点击链接访问亲属页面获取详细信息（电话和地址）...")

                    detailed_relatives = []
                    for i, relative in enumerate(relatives_info):
                        relative_name = relative.get('姓名', '')

                        if relative_name:
                            logging.info(f"正在处理第 {i+1}/{len(relatives_info)} 个亲属: {relative_name}")

                            # 通过点击链接访问亲属页面获取详细信息
                            relative_details = click_relative_link_and_extract(driver, relative_name, soup)

                            # 合并基本信息和详细信息
                            combined_info = {
                                '姓名': relative.get('姓名', ''),
                                '年龄': relative.get('年龄', ''),
                                '关系': relative.get('关系', '亲属'),
                                '链接': relative.get('链接', ''),
                                '手机': relative_details.get('手机', ''),
                                '地址': relative_details.get('地址', '')
                            }
                            detailed_relatives.append(combined_info)

                            # 添加延迟模拟真实用户行为
                            time.sleep(2)
                        else:
                            # 如果没有姓名，保留原始信息
                            detailed_relatives.append(relative)

                    # 保存到CSV文件
                    if person_info or detailed_relatives:
                        csv_filename = 'background_report_data.csv'
                        if save_to_csv(person_info, detailed_relatives, csv_filename):
                            logging.info(f"✅ 数据提取完成，已保存到 {csv_filename}")
                            logging.info(f"个人信息: {person_info}")
                            logging.info(f"符合条件的亲属数量: {len(detailed_relatives)}")
                        else:
                            logging.error("❌ 保存CSV文件失败")
                    else:
                        logging.warning("⚠️ 未提取到任何信息")
                
            else:
                logging.error("❌ 未找到背景报告按钮")
        
        total_time = time.time() - total_start
        logging.info("=" * 60)
        logging.info(f"⚡ 总执行时间: {total_time:.3f}秒")
        
    except Exception as e:
        total_time = time.time() - total_start
        logging.error(f"测试过程中出错，总耗时: {total_time:.3f}秒，错误: {e}")
    finally:
        try:
            driver.quit()
            logging.info("浏览器已关闭")
        except:
            pass
    
    logging.info("瞬时加载测试完成")

if __name__ == '__main__':
    main()
