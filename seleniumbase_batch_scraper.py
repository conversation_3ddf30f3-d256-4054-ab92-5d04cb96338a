#!/usr/bin/env python3
"""
SeleniumBase批量电话号码爬取器
使用SeleniumBase UC Mode + CDP Mode 绕过机器人检测
基于enhanced_batch_scraper.py的逻辑重新实现
"""

import os
import sys
import time
import logging
import traceback
import re
import random
from datetime import datetime
from typing import List, Dict, Tuple
import csv
from pathlib import Path

from seleniumbase import SB
from bs4 import BeautifulSoup

# 注意：现在使用内置的SeleniumBase版本函数，不再依赖test_instant_load.py

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seleniumbase_batch_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class SeleniumBaseBatchScraper:
    """SeleniumBase批量爬取器 - UC Mode + CDP Mode 反机器人检测"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data"):
        """
        初始化SeleniumBase爬取器
        
        Args:
            phone_file: 电话号码文件路径
            output_dir: 输出目录
        """
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None,
            'current_phone': None
        }

        # 已处理的电话号码记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()

        # 统一的CSV文件
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_initialized = False

        # 智能延迟控制 - 减少延迟时间以提高效率
        self.last_request_time = 0
        self.min_delay = 1.0  # 最小延迟（秒）- 从2.0减少到1.0
        self.max_delay = 3.0  # 最大延迟（秒）- 从5.0减少到3.0
        self.adaptive_delay = 1.5  # 自适应延迟 - 从2.0减少到1.5
        self.success_count = 0  # 连续成功次数
        self.fail_count = 0  # 连续失败次数

        logging.info(f"SeleniumBase批量爬取器初始化完成")
        logging.info(f"电话号码文件: {phone_file}")
        logging.info(f"输出目录: {output_dir}")
        logging.info(f"已处理电话号码: {len(self.processed_phones)}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")

    def init_unified_csv(self):
        """初始化统一的CSV文件"""
        if not self.csv_initialized:
            try:
                if not self.unified_csv_file.exists():
                    with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                    logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                else:
                    logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
                self.csv_initialized = True
            except Exception as e:
                logging.error(f"初始化CSV文件失败: {e}")

    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """立即保存数据到CSV文件"""
        try:
            self.init_unified_csv()
            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    data_type,
                    info.get('姓名', ''),
                    info.get('年龄', ''),
                    info.get('手机', ''),
                    info.get('地址', ''),
                    info.get('关系', data_type),
                    info.get('链接', '')
                ])
            logging.info(f"✅ 已保存{data_type}: {info.get('姓名', 'N/A')}")
            return True
        except Exception as e:
            logging.error(f"保存{data_type}信息失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """从文件中加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                            logging.debug(f"去掉前缀1: {line.strip()} -> {phone}")
                        
                        # 验证电话号码格式（10位数字）
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def calculate_adaptive_delay(self, success: bool) -> float:
        """计算自适应延迟时间"""
        current_time = time.time()
        
        # 更新成功/失败计数
        if success:
            self.success_count += 1
            self.fail_count = 0
            # 连续成功时逐渐减少延迟
            if self.success_count >= 3:
                self.adaptive_delay = max(self.min_delay, self.adaptive_delay * 0.9)
        else:
            self.fail_count += 1
            self.success_count = 0
            # 失败时增加延迟
            self.adaptive_delay = min(self.max_delay * 2, self.adaptive_delay * 1.5)
        
        # 确保最小间隔
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_delay:
            additional_delay = self.min_delay - time_since_last
        else:
            additional_delay = 0
        
        # 计算总延迟
        base_delay = random.uniform(self.adaptive_delay * 0.8, self.adaptive_delay * 1.2)
        total_delay = base_delay + additional_delay
        
        # 记录请求时间
        self.last_request_time = current_time + total_delay
        
        return total_delay

    def create_sb_driver_wrapper(self, sb):
        """创建一个包装器，使SeleniumBase兼容DrissionPage的接口"""
        class SBDriverWrapper:
            def __init__(self, sb_instance):
                self.sb = sb_instance

            @property
            def html(self):
                """获取页面源码"""
                return self.sb.cdp.get_page_source()

            @property
            def url(self):
                """获取当前URL"""
                return self.sb.cdp.get_current_url()

            def back(self):
                """返回上一页"""
                self.sb.cdp.go_back()

            def run_js(self, script):
                """执行JavaScript"""
                return self.sb.cdp.execute_script(script)

        return SBDriverWrapper(sb)

    def find_background_report_button_sb(self, sb) -> Tuple[bool, str]:
        """使用SeleniumBase查找背景报告按钮 - 增强版本"""
        try:
            # 等待页面稳定加载
            sb.sleep(2)

            # 获取页面源码并使用BeautifulSoup解析
            page_source = sb.cdp.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            logging.info(f"页面源码长度: {len(page_source)} 字符")

            # 多种策略查找背景报告链接
            found_links = []

            # 策略1: 查找包含特定文本的链接
            links = soup.find_all('a', href=True)
            for link in links:
                link_text = link.get_text().strip().lower()
                href = link.get('href', '')

                # 扩展关键词匹配
                keywords = [
                    'background report', 'free background', 'open free',
                    'view report', 'get report', 'full report',
                    'background check', 'person report'
                ]

                if any(keyword in link_text for keyword in keywords):
                    if href.startswith('/'):
                        current_url = sb.cdp.get_current_url()
                        base_url = f"{current_url.split('/')[0]}//{current_url.split('/')[2]}"
                        href = base_url + href

                    # 验证链接有效性
                    if "smartbackgroundchecks.com" in href and "/people/" in href:
                        found_links.append((href, link_text, "text_match"))
                        logging.info(f"找到文本匹配链接: {href} (文本: {link_text})")

            # 策略2: 查找特定class或id的链接
            button_selectors = [
                'a[class*="btn"]', 'a[class*="button"]',
                'a[class*="report"]', 'a[class*="background"]'
            ]

            for selector in button_selectors:
                elements = soup.select(selector)
                for element in elements:
                    href = element.get('href', '')
                    if href and href.startswith('/'):
                        current_url = sb.cdp.get_current_url()
                        base_url = f"{current_url.split('/')[0]}//{current_url.split('/')[2]}"
                        href = base_url + href

                    if href and "smartbackgroundchecks.com" in href and "/people/" in href:
                        element_text = element.get_text().strip().lower()
                        found_links.append((href, element_text, "selector_match"))
                        logging.info(f"找到选择器匹配链接: {href} (选择器: {selector})")

            # 策略3: 查找URL模式匹配
            import re
            url_pattern = r'https://www\.smartbackgroundchecks\.com/people/[^"\s]+'
            url_matches = re.findall(url_pattern, page_source)
            for url in url_matches:
                found_links.append((url, "url_pattern", "pattern_match"))
                logging.info(f"找到URL模式匹配: {url}")

            # 选择最佳链接
            if found_links:
                # 优先选择文本匹配的链接
                text_matches = [link for link in found_links if link[2] == "text_match"]
                if text_matches:
                    best_link = text_matches[0][0]
                    logging.info(f"✅ 选择最佳链接 (文本匹配): {best_link}")
                    return True, best_link

                # 其次选择选择器匹配的链接
                selector_matches = [link for link in found_links if link[2] == "selector_match"]
                if selector_matches:
                    best_link = selector_matches[0][0]
                    logging.info(f"✅ 选择最佳链接 (选择器匹配): {best_link}")
                    return True, best_link

                # 最后选择URL模式匹配的链接
                pattern_matches = [link for link in found_links if link[2] == "pattern_match"]
                if pattern_matches:
                    best_link = pattern_matches[0][0]
                    logging.info(f"✅ 选择最佳链接 (模式匹配): {best_link}")
                    return True, best_link

            logging.warning("❌ 未找到任何有效的背景报告链接")

            # 调试信息：输出页面中的所有链接
            all_links = [link.get('href') for link in soup.find_all('a', href=True)]
            logging.debug(f"页面中的所有链接: {all_links[:10]}...")  # 只显示前10个

            return False, ""

        except Exception as e:
            logging.error(f"查找背景报告按钮失败: {e}")
            return False, ""

    def extract_person_info_sb(self, soup) -> Dict:
        """使用BeautifulSoup提取个人信息 - 与test_instant_load.py逻辑一致"""
        try:
            person_info = {}

            # 提取姓名 - 从标题中获取
            title = soup.title.string if soup.title else ""
            if title:
                # 从标题中提取姓名，格式通常是 "Name - City, State Phone - Public Record"
                name_match = re.match(r'^([^-]+)', title.strip())
                if name_match:
                    person_info['姓名'] = name_match.group(1).strip()

            # 提取年龄 - 从h2标题中查找 "60 years old"
            h2_elements = soup.find_all('h2', class_='h2Title')
            for h2 in h2_elements:
                h2_text = h2.get_text()
                age_match = re.search(r'(\d+)\s*years?\s*old', h2_text, re.IGNORECASE)
                if age_match:
                    age_value = age_match.group(1)
                    # 确保年龄是合理的数字（1-120之间）
                    if age_value.isdigit() and 1 <= int(age_value) <= 120:
                        person_info['年龄'] = age_value
                        break

            # 如果h2中没找到，再从整个页面查找
            if '年龄' not in person_info:
                page_text = soup.get_text()
                # 查找所有年龄模式，选择最合理的
                age_matches = re.findall(r'(\d+)\s*years?\s*old', page_text, re.IGNORECASE)
                for age_value in age_matches:
                    if age_value.isdigit() and 1 <= int(age_value) <= 120:
                        person_info['年龄'] = age_value
                        break

            # 提取手机号码 - 从标题中获取
            if title:
                phone_match = re.search(r'\((\d{3})\)\s*(\d{3})-(\d{4})', title)
                if phone_match:
                    person_info['手机'] = f"({phone_match.group(1)}){phone_match.group(2)}-{phone_match.group(3)}"

            # 提取地址 - 从标题中获取
            if title:
                # 查找城市和州的信息
                location_match = re.search(r'-\s*([^-]+),\s*([A-Z]{2})', title)
                if location_match:
                    person_info['地址'] = f"{location_match.group(1).strip()}, {location_match.group(2)}"

            logging.info(f"提取到个人信息: {person_info}")
            return person_info

        except Exception as e:
            logging.error(f"提取个人信息失败: {e}")
            return {}

    def extract_relatives_info_sb(self, soup, min_age: int = 40) -> List[Dict]:
        """使用BeautifulSoup提取亲属信息 - 增强版本"""
        try:
            relatives = []

            # 策略1: 查找亲属信息区域
            relatives_sections = []

            # 查找多种可能的亲属区域标识
            section_keywords = [
                'Relatives Found', 'Related People', 'Family Members',
                'Associates', 'Possible Relatives', 'Known Associates'
            ]

            for keyword in section_keywords:
                section = soup.find(string=re.compile(keyword, re.IGNORECASE))
                if section:
                    relatives_sections.append(section)
                    logging.info(f"找到亲属区域: {keyword}")

            # 如果没找到特定区域，尝试在整个页面查找
            if not relatives_sections:
                logging.info("未找到特定亲属区域，在整个页面查找")
                relatives_sections = [soup]

            for section_root in relatives_sections:
                # 找到包含亲属信息的容器
                if hasattr(section_root, 'parent'):
                    relatives_container = section_root.parent
                    while relatives_container and relatives_container.name not in ['div', 'section', 'article']:
                        relatives_container = relatives_container.parent
                else:
                    relatives_container = section_root

                if not relatives_container:
                    continue

                # 策略2: 查找card-block结构
                card_blocks = relatives_container.find_all('div', class_='card-block')
                for card in card_blocks:
                    link = card.find('a', class_='link-underline')
                    small_tag = card.find('small')

                    if link and small_tag:
                        name = link.get_text().strip()
                        age_text = small_tag.get_text().strip()

                        age_match = re.search(r'(\d+)\s*years?\s*old', age_text, re.IGNORECASE)
                        if age_match:
                            age = int(age_match.group(1))

                            if age >= min_age and name:
                                relative_info = {
                                    '姓名': name,
                                    '年龄': age,
                                    '关系': '亲属',
                                    '链接': link.get('href', '')
                                }
                                relatives.append(relative_info)
                                logging.info(f"✅ 找到符合条件的亲属: {name}, {age}岁")
                            else:
                                logging.debug(f"跳过年龄不符的亲属: {name}, {age}岁 (< {min_age})")

                # 策略3: 查找所有包含年龄信息的链接
                all_links = relatives_container.find_all('a', href=True)
                for link in all_links:
                    link_text = link.get_text().strip()
                    # 查找包含年龄信息的链接
                    age_match = re.search(r'(\d+)\s*years?\s*old', link_text, re.IGNORECASE)
                    if age_match:
                        age = int(age_match.group(1))
                        # 提取姓名（去掉年龄部分）
                        name = re.sub(r'\s*\d+\s*years?\s*old.*', '', link_text, flags=re.IGNORECASE).strip()

                        if age >= min_age and name and len(name) > 2:
                            # 检查是否已经添加过这个人
                            if not any(rel['姓名'] == name for rel in relatives):
                                relative_info = {
                                    '姓名': name,
                                    '年龄': age,
                                    '关系': '亲属',
                                    '链接': link.get('href', '')
                                }
                                relatives.append(relative_info)
                                logging.info(f"✅ 找到符合条件的亲属 (链接): {name}, {age}岁")

                # 策略4: 正则表达式全文搜索
                if not relatives:
                    text_content = relatives_container.get_text()
                    # 更复杂的正则表达式模式
                    patterns = [
                        r'([A-Z][a-z]+\s+[A-Z][a-z]+)\s*,?\s*(\d+)\s*years?\s*old',
                        r'([A-Z][a-z]+\s+[A-Z]\.\s+[A-Z][a-z]+)\s*,?\s*(\d+)\s*years?\s*old',
                        r'([A-Z][a-z]+\s+[A-Z][a-z]+\s+[A-Z][a-z]+)\s*,?\s*(\d+)\s*years?\s*old'
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, text_content, re.IGNORECASE)
                        for name, age_str in matches:
                            age = int(age_str)
                            name = name.strip()

                            if age >= min_age and name and len(name) > 5:
                                # 检查是否已经添加过这个人
                                if not any(rel['姓名'] == name for rel in relatives):
                                    relative_info = {
                                        '姓名': name,
                                        '年龄': age,
                                        '关系': '亲属',
                                        '链接': ''
                                    }
                                    relatives.append(relative_info)
                                    logging.info(f"✅ 找到符合条件的亲属 (正则): {name}, {age}岁")

            # 去重和排序
            unique_relatives = []
            seen_names = set()
            for rel in relatives:
                name = rel['姓名']
                if name not in seen_names:
                    unique_relatives.append(rel)
                    seen_names.add(name)

            # 按年龄排序（年龄大的在前）
            unique_relatives.sort(key=lambda x: x['年龄'], reverse=True)

            logging.info(f"📊 共找到 {len(unique_relatives)} 个符合条件的亲属（年龄>={min_age}）")
            return unique_relatives

        except Exception as e:
            logging.error(f"提取亲属信息失败: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return []

    def click_relative_link_and_extract_sb(self, sb, relative_name: str, relative_link: str) -> Dict:
        """点击亲属链接并提取详细信息 - SeleniumBase版本"""
        try:
            logging.info(f"开始访问亲属页面: {relative_name} -> {relative_link}")

            # 构造完整URL
            if relative_link.startswith('/'):
                current_url = sb.cdp.get_current_url()
                base_url = f"{current_url.split('/')[0]}//{current_url.split('/')[2]}"
                full_url = base_url + relative_link
            else:
                full_url = relative_link

            # 访问亲属页面
            sb.cdp.get(full_url)
            sb.sleep(1.5)  # 等待页面加载

            # 获取页面源码并解析
            page_source = sb.cdp.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取亲属详细信息
            relative_details = self.extract_person_info_sb(soup)
            logging.info(f"提取到亲属详细信息: {relative_name} - {relative_details}")

            return relative_details

        except Exception as e:
            logging.error(f"处理亲属链接失败 {relative_name}: {e}")
            return {}

    def scrape_phone_data_sb(self, sb, phone: str) -> Tuple[bool, Dict]:
        """
        使用SeleniumBase爬取单个电话号码的背景报告数据
        使用与enhanced_batch_scraper.py完全相同的数据提取逻辑

        Args:
            sb: SeleniumBase实例
            phone: 10位电话号码

        Returns:
            (成功标志, 数据字典)
        """
        start_time = time.time()
        logging.info("=" * 80)
        logging.info(f"🔍 开始处理电话号码: {phone}")

        try:
            # 自适应延迟 - 减少延迟时间
            delay = self.calculate_adaptive_delay(True)  # 先假设会成功
            logging.info(f"⏰ 智能延迟: {delay:.2f}秒 (自适应延迟: {self.adaptive_delay:.2f})")
            sb.sleep(delay)

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"目标URL: {target_url}")

            # 使用CDP Mode访问页面 - 最强反检测
            sb.activate_cdp_mode(target_url)

            # 模拟人类行为：随机等待
            human_delay = random.uniform(2.0, 4.0)
            sb.sleep(human_delay)

            # 模拟人类行为：随机鼠标移动
            try:
                sb.cdp.mouse_move(random.randint(100, 800), random.randint(100, 600))
                sb.sleep(random.uniform(0.5, 1.0))
            except Exception as e:
                logging.debug(f"鼠标移动模拟: {e}")

            # 检查并处理Cloudflare挑战
            if not self.handle_cloudflare_challenge_sb(sb):
                logging.warning(f"Cloudflare挑战处理失败: {phone}")
                # 尝试重新加载页面
                logging.info("🔄 尝试重新加载页面...")
                sb.cdp.refresh()
                sb.sleep(random.uniform(3.0, 5.0))
                if not self.handle_cloudflare_challenge_sb(sb):
                    self.calculate_adaptive_delay(False)
                    return False, {}

            # 检查页面是否正常加载
            try:
                page_title = sb.cdp.get_title()
                current_url = sb.cdp.get_current_url()
                logging.info(f"页面标题: {page_title}")
                logging.info(f"当前URL: {current_url}")

                # 检查是否仍在Cloudflare页面或被阻止
                if ("just a moment" in page_title.lower() or
                    "cloudflare" in current_url.lower() or
                    "error" in page_title.lower() or
                    "blocked" in page_title.lower()):
                    logging.warning(f"页面访问受限: {page_title}")
                    self.calculate_adaptive_delay(False)
                    return False, {}

            except Exception as e:
                logging.warning(f"获取页面信息失败: {e}")

            # 注意：现在直接使用SeleniumBase，不需要包装器

            # 使用SeleniumBase版本的函数查找背景报告按钮
            button_found, button_url = self.find_background_report_button_sb(sb)

            if not button_found:
                logging.warning(f"未找到背景报告按钮: {phone}")
                self.calculate_adaptive_delay(False)
                return False, {}

            logging.info(f"找到背景报告链接: {button_url}")

            # 访问背景报告页面
            sb.cdp.get(button_url)
            sb.sleep(1.5)

            # 再次处理Cloudflare挑战
            if not self.handle_cloudflare_challenge_sb(sb):
                logging.warning(f"背景报告页面Cloudflare挑战处理失败: {phone}")
                # 尝试错误恢复
                if self.enhanced_error_recovery_sb(sb, phone, "cloudflare"):
                    logging.info("✅ 错误恢复成功，继续处理")
                else:
                    self.calculate_adaptive_delay(False)
                    return False, {}

            # 等待页面内容稳定加载
            sb.sleep(1)

            # 获取页面源码并使用BeautifulSoup解析
            page_source = sb.cdp.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            # 使用SeleniumBase版本的函数提取个人信息
            person_info = self.extract_person_info_sb(soup)
            if not person_info.get('姓名'):
                logging.warning(f"未提取到个人信息: {phone}")
                self.calculate_adaptive_delay(False)
                return False, {}

            # 立即保存个人信息
            self.save_to_csv(phone, '本人', person_info)

            # 使用SeleniumBase版本的函数提取亲属信息
            relatives_info = self.extract_relatives_info_sb(soup, min_age=40)
            logging.info(f"找到 {len(relatives_info)} 个符合条件的亲属")

            # 处理亲属信息并实时保存
            detailed_relatives_count = 0
            for i, relative in enumerate(relatives_info):
                relative_name = relative.get('姓名', '')
                if relative_name:
                    # 智能重试机制
                    max_retries = 2
                    for retry in range(max_retries):
                        try:
                            logging.info(f"处理亲属 {i+1}/{len(relatives_info)}: {relative_name} (尝试 {retry+1}/{max_retries})")

                            # 如果是重试，短暂等待
                            if retry > 0:
                                sb.sleep(0.5)  # 减少重试等待时间：从0.8秒减少到0.5秒

                            # 使用SeleniumBase版本的函数点击亲属链接并提取详细信息
                            relative_link = relative.get('链接', '')
                            if relative_link:
                                relative_details = self.click_relative_link_and_extract_sb(sb, relative_name, relative_link)
                            else:
                                relative_details = {}

                            combined_info = {
                                '姓名': relative.get('姓名', ''),
                                '年龄': relative.get('年龄', ''),
                                '关系': relative.get('关系', '亲属'),
                                '链接': relative.get('链接', ''),
                                '手机': relative_details.get('手机', ''),
                                '地址': relative_details.get('地址', '')
                            }

                            # 立即保存每个亲属信息
                            if self.save_to_csv(phone, '亲属', combined_info):
                                detailed_relatives_count += 1

                            # 检查是否获取到有效数据
                            has_contact_info = combined_info.get('手机') or combined_info.get('地址')

                            if has_contact_info:
                                # 获取到联系信息，正常延迟
                                sb.sleep(random.uniform(0.2, 0.5))  # 减少延迟：从0.4-0.8秒减少到0.2-0.5秒
                            else:
                                # 没有联系信息，快速处理下一个
                                sb.sleep(random.uniform(0.1, 0.3))  # 减少延迟：从0.2-0.4秒减少到0.1-0.3秒

                            break  # 成功处理，跳出重试循环

                        except Exception as e:
                            error_msg = str(e).lower()
                            logging.error(f"处理亲属 {relative_name} 失败 (尝试 {retry+1}/{max_retries}): {e}")

                            # 智能错误判断：某些错误直接跳过，不重试
                            if any(keyword in error_msg for keyword in ['页面已被刷新', 'context lost', 'page refresh']):
                                logging.warning(f"检测到页面刷新错误，直接跳过亲属 {relative_name}")
                                break

                            if retry == max_retries - 1:
                                logging.warning(f"亲属 {relative_name} 处理失败，保存基本信息")
                                # 保存基本信息，即使没有详细联系方式
                                basic_info = {
                                    '姓名': relative.get('姓名', ''),
                                    '年龄': relative.get('年龄', ''),
                                    '关系': relative.get('关系', '亲属'),
                                    '链接': relative.get('链接', ''),
                                    '手机': '',
                                    '地址': ''
                                }
                                self.save_to_csv(phone, '亲属', basic_info)
                            else:
                                sb.sleep(0.2)  # 减少重试前等待时间：从0.3秒减少到0.2秒

            duration = time.time() - start_time
            logging.info(f"✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"成功保存亲属数量: {detailed_relatives_count}")

            # 更新成功状态
            self.calculate_adaptive_delay(True)

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"错误详情: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")

            # 更新失败状态
            self.calculate_adaptive_delay(False)

            return False, {}

    def handle_cloudflare_challenge_sb(self, sb, max_attempts: int = 5) -> bool:
        """
        处理Cloudflare挑战 - 最强版本

        Args:
            sb: SeleniumBase实例
            max_attempts: 最大尝试次数

        Returns:
            是否成功绕过
        """
        for attempt in range(max_attempts):
            try:
                # 等待页面稳定
                sb.sleep(random.uniform(1.0, 2.0))

                # 检查页面标题和URL
                page_title = sb.cdp.get_title()
                current_url = sb.cdp.get_current_url()
                page_source = sb.cdp.get_page_source()

                # 检查多种Cloudflare标识
                cloudflare_indicators = [
                    "just a moment",
                    "checking your browser",
                    "cloudflare",
                    "please wait",
                    "ray id",
                    "cf-browser-verification"
                ]

                is_cloudflare = any(indicator in page_title.lower() for indicator in cloudflare_indicators) or \
                               any(indicator in current_url.lower() for indicator in cloudflare_indicators) or \
                               any(indicator in page_source.lower() for indicator in cloudflare_indicators[:3])

                # 如果不是Cloudflare页面，直接返回成功
                if not is_cloudflare:
                    return True

                logging.info(f"🛡️ 检测到Cloudflare挑战，尝试绕过 (第{attempt+1}/{max_attempts}次)")
                logging.info(f"页面标题: {page_title}")

                # 方法1: 使用SeleniumBase的自动CAPTCHA处理
                try:
                    sb.uc_gui_click_captcha()
                    sb.sleep(random.uniform(2.0, 4.0))
                    logging.info("✅ 自动CAPTCHA处理完成")
                except Exception as e:
                    logging.debug(f"自动CAPTCHA处理: {e}")

                # 方法2: 模拟人类行为
                try:
                    # 随机鼠标移动
                    for _ in range(random.randint(2, 4)):
                        x = random.randint(100, 800)
                        y = random.randint(100, 600)
                        sb.cdp.mouse_move(x, y)
                        sb.sleep(random.uniform(0.3, 0.8))

                    # 模拟滚动
                    sb.cdp.scroll_down(random.randint(100, 300))
                    sb.sleep(random.uniform(0.5, 1.0))
                    sb.cdp.scroll_up(random.randint(50, 150))

                except Exception as e:
                    logging.debug(f"人类行为模拟: {e}")

                # 等待Cloudflare处理
                wait_time = random.uniform(5.0, 8.0)
                logging.info(f"⏳ 等待Cloudflare处理: {wait_time:.1f}秒")
                sb.sleep(wait_time)

                # 检查是否成功绕过
                new_title = sb.cdp.get_title()
                new_url = sb.cdp.get_current_url()
                new_source = sb.cdp.get_page_source()

                new_is_cloudflare = any(indicator in new_title.lower() for indicator in cloudflare_indicators) or \
                                   any(indicator in new_url.lower() for indicator in cloudflare_indicators) or \
                                   any(indicator in new_source.lower() for indicator in cloudflare_indicators[:3])

                if not new_is_cloudflare:
                    logging.info("✅ Cloudflare挑战绕过成功")
                    return True

                # 如果还在挑战页面，尝试不同策略
                if attempt < max_attempts - 1:
                    if attempt % 2 == 0:
                        logging.info("🔄 策略1: 刷新页面...")
                        sb.cdp.refresh()
                        sb.sleep(random.uniform(3.0, 5.0))
                    else:
                        logging.info("🔄 策略2: 重新访问...")
                        current_url_clean = current_url.split('?')[0]  # 移除查询参数
                        sb.cdp.get(current_url_clean)
                        sb.sleep(random.uniform(3.0, 5.0))

            except Exception as e:
                logging.error(f"Cloudflare挑战处理异常 (第{attempt+1}次): {e}")
                if attempt < max_attempts - 1:
                    sb.sleep(random.uniform(2.0, 4.0))

        logging.warning("❌ Cloudflare挑战绕过失败")
        return False

    def enhanced_error_recovery_sb(self, sb, phone: str, error_type: str) -> bool:
        """
        增强的错误恢复机制

        Args:
            sb: SeleniumBase实例
            phone: 当前处理的电话号码
            error_type: 错误类型

        Returns:
            是否恢复成功
        """
        try:
            logging.info(f"🔧 尝试错误恢复: {error_type}")

            if error_type == "cloudflare":
                # Cloudflare错误恢复
                return self.handle_cloudflare_challenge_sb(sb, max_attempts=2)

            elif error_type == "page_load":
                # 页面加载错误恢复
                sb.sleep(2)
                sb.cdp.refresh()
                sb.sleep(3)
                return True

            elif error_type == "network":
                # 网络错误恢复
                sb.sleep(5)
                target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
                sb.cdp.get(target_url)
                sb.sleep(3)
                return True

            else:
                # 通用错误恢复
                sb.sleep(1)
                return True

        except Exception as e:
            logging.error(f"错误恢复失败: {e}")
            return False

    def run_seleniumbase_batch_scraping(self, max_phones: int = None,
                                      base_delay: float = 1.5) -> Dict:
        """
        运行SeleniumBase批量爬取

        Args:
            max_phones: 最大处理电话号码数量，None表示处理所有
            base_delay: 基础延迟时间（秒）

        Returns:
            统计结果字典
        """
        self.stats['start_time'] = datetime.now()
        self.min_delay = base_delay
        self.max_delay = base_delay * 2.5
        self.adaptive_delay = base_delay

        logging.info("🚀 开始SeleniumBase批量爬取任务")
        logging.info(f"基础延迟: {base_delay}秒")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 使用SeleniumBase UC Mode + CDP Mode，最强反检测配置
        with SB(uc=True, test=True, incognito=True, locale="en", ad_block=True,
                disable_csp=True, disable_ws=True, block_images=True,
                chromium_arg="--disable-blink-features=AutomationControlled",
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36") as sb:
            try:
                # 预热浏览器 - 访问主页建立会话
                logging.info("🔥 预热浏览器会话...")
                try:
                    sb.activate_cdp_mode("https://www.smartbackgroundchecks.com")
                    sb.sleep(random.uniform(2.0, 4.0))

                    # 模拟用户浏览行为
                    sb.cdp.mouse_move(random.randint(200, 600), random.randint(200, 400))
                    sb.sleep(random.uniform(1.0, 2.0))
                    sb.cdp.scroll_down(random.randint(200, 400))
                    sb.sleep(random.uniform(1.0, 2.0))

                    logging.info("✅ 浏览器会话预热完成")
                except Exception as e:
                    logging.warning(f"浏览器预热失败: {e}")

                # 处理每个电话号码
                for i, phone in enumerate(unprocessed_phones, 1):
                    self.stats['current_phone'] = phone
                    self.stats['processed_phones'] = i

                    logging.info(f"\n📞 处理进度: {i}/{len(unprocessed_phones)} ({i/len(unprocessed_phones)*100:.1f}%)")

                    # 智能重试机制
                    max_retries = 2
                    success = False

                    for retry in range(max_retries):
                        try:
                            if retry > 0:
                                logging.info(f"🔄 重试处理电话号码 {phone} (第{retry+1}次)")
                                # 重试前等待更长时间
                                retry_delay = random.uniform(5.0, 10.0)
                                sb.sleep(retry_delay)

                                # 清理浏览器状态
                                try:
                                    sb.cdp.clear_all_cookies()
                                    sb.cdp.clear_local_storage()
                                except Exception as e:
                                    logging.debug(f"清理浏览器状态: {e}")

                            success, _ = self.scrape_phone_data_sb(sb, phone)

                            if success:
                                self.stats['successful_scrapes'] += 1
                                self.save_processed_phone(phone)
                                logging.info(f"✅ 成功处理: {phone}")
                                break
                            else:
                                if retry == max_retries - 1:
                                    self.stats['failed_scrapes'] += 1
                                    logging.error(f"❌ 处理失败: {phone} (已重试{max_retries}次)")

                        except Exception as e:
                            if retry == max_retries - 1:
                                self.stats['failed_scrapes'] += 1
                                logging.error(f"❌ 处理电话号码 {phone} 时发生异常: {e}")
                                logging.error(f"异常堆栈: {traceback.format_exc()}")
                            else:
                                logging.warning(f"⚠️ 处理电话号码 {phone} 异常，将重试: {e}")

                    # 打印当前统计
                    self.print_stats()

            except KeyboardInterrupt:
                logging.info("收到中断信号，正在优雅退出...")
            except Exception as e:
                logging.error(f"批量处理过程中发生严重错误: {e}")
                logging.error(f"错误堆栈: {traceback.format_exc()}")

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 SeleniumBase批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        if self.stats['processed_phones'] > 0:
            success_rate = self.stats['successful_scrapes']/self.stats['processed_phones']*100
            avg_time = duration / self.stats['processed_phones']
            logging.info(f"成功率: {success_rate:.1f}%")
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
        logging.info(f"最终自适应延迟: {self.adaptive_delay:.2f}秒")
        logging.info("=" * 80)

        return self.stats

    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"自适应延迟: {self.adaptive_delay:.2f}s, 耗时: {elapsed:.0f}秒")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='SeleniumBase批量电话号码背景报告爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=float, default=1.5, help='基础延迟时间（秒）')

    args = parser.parse_args()

    try:
        # 创建SeleniumBase爬取器
        scraper = SeleniumBaseBatchScraper(args.phone_file, args.output_dir)

        # 运行SeleniumBase批量爬取
        stats = scraper.run_seleniumbase_batch_scraping(args.max_phones, args.delay)

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
