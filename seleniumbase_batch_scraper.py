#!/usr/bin/env python3
"""
SeleniumBase批量电话号码爬取器
使用SeleniumBase UC Mode + 智能延迟策略
自带绕过机器人检测的API，提供更稳定的爬取体验
"""

import os
import sys
import time
import logging
import traceback
import re
import random
from datetime import datetime
from typing import List, Dict, Tuple
import csv
from pathlib import Path

from seleniumbase import SB
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seleniumbase_batch_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class SeleniumBaseBatchScraper:
    """SeleniumBase批量爬取器 - UC Mode智能反检测策略"""
    
    def __init__(self, phone_file: str = "KK1000.txt", output_dir: str = "scraped_data"):
        """
        初始化SeleniumBase爬取器
        
        Args:
            phone_file: 电话号码文件路径
            output_dir: 输出目录
        """
        self.phone_file = phone_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_phones': 0,
            'processed_phones': 0,
            'successful_scrapes': 0,
            'failed_scrapes': 0,
            'start_time': None,
            'current_phone': None
        }

        # 已处理的电话号码记录
        self.processed_file = self.output_dir / "processed_phones.txt"
        self.processed_phones = self.load_processed_phones()

        # 统一的CSV文件
        self.unified_csv_file = self.output_dir / "all_phone_data.csv"
        self.csv_initialized = False

        # 智能延迟控制
        self.last_request_time = 0
        self.min_delay = 2.0  # 最小延迟（秒）
        self.max_delay = 5.0  # 最大延迟（秒）
        self.adaptive_delay = 2.0  # 自适应延迟
        self.success_count = 0  # 连续成功次数
        self.fail_count = 0  # 连续失败次数

        logging.info(f"SeleniumBase批量爬取器初始化完成")
        logging.info(f"电话号码文件: {phone_file}")
        logging.info(f"输出目录: {output_dir}")
        logging.info(f"已处理电话号码: {len(self.processed_phones)}")
    
    def load_processed_phones(self) -> set:
        """加载已处理的电话号码"""
        processed = set()
        if self.processed_file.exists():
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        phone = line.strip()
                        if phone:
                            processed.add(phone)
                logging.info(f"加载了 {len(processed)} 个已处理的电话号码")
            except Exception as e:
                logging.error(f"加载已处理电话号码失败: {e}")
        return processed
    
    def save_processed_phone(self, phone: str):
        """保存已处理的电话号码"""
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{phone}\n")
            self.processed_phones.add(phone)
        except Exception as e:
            logging.error(f"保存已处理电话号码失败: {e}")

    def init_unified_csv(self):
        """初始化统一的CSV文件"""
        if not self.csv_initialized:
            try:
                if not self.unified_csv_file.exists():
                    with open(self.unified_csv_file, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(['查询电话', '类型', '姓名', '年龄', '手机', '地址', '关系', '链接'])
                    logging.info(f"创建统一CSV文件: {self.unified_csv_file}")
                else:
                    logging.info(f"使用现有CSV文件: {self.unified_csv_file}")
                self.csv_initialized = True
            except Exception as e:
                logging.error(f"初始化CSV文件失败: {e}")

    def save_to_csv(self, phone: str, data_type: str, info: Dict):
        """立即保存数据到CSV文件"""
        try:
            self.init_unified_csv()
            with open(self.unified_csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    phone,
                    data_type,
                    info.get('姓名', ''),
                    info.get('年龄', ''),
                    info.get('手机', ''),
                    info.get('地址', ''),
                    info.get('关系', data_type),
                    info.get('链接', '')
                ])
            logging.info(f"✅ 已保存{data_type}: {info.get('姓名', 'N/A')}")
            return True
        except Exception as e:
            logging.error(f"保存{data_type}信息失败: {e}")
            return False
    
    def load_phone_numbers(self) -> List[str]:
        """从文件中加载电话号码"""
        phones = []
        
        if not os.path.exists(self.phone_file):
            logging.error(f"电话号码文件不存在: {self.phone_file}")
            return phones
        
        try:
            with open(self.phone_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        phone = line.strip()
                        if not phone:
                            continue
                        
                        # 去掉前缀1
                        if phone.startswith('1') and len(phone) == 11:
                            phone = phone[1:]
                            logging.debug(f"去掉前缀1: {line.strip()} -> {phone}")
                        
                        # 验证电话号码格式（10位数字）
                        if re.match(r'^\d{10}$', phone):
                            phones.append(phone)
                        else:
                            logging.warning(f"第{line_num}行电话号码格式无效: {line.strip()}")
                            
                    except Exception as e:
                        logging.error(f"处理第{line_num}行时出错: {e}")
                        continue
            
            logging.info(f"成功加载 {len(phones)} 个有效电话号码")
            
        except Exception as e:
            logging.error(f"读取电话号码文件失败: {e}")
        
        return phones
    
    def calculate_adaptive_delay(self, success: bool) -> float:
        """计算自适应延迟时间"""
        current_time = time.time()
        
        # 更新成功/失败计数
        if success:
            self.success_count += 1
            self.fail_count = 0
            # 连续成功时逐渐减少延迟
            if self.success_count >= 3:
                self.adaptive_delay = max(self.min_delay, self.adaptive_delay * 0.9)
        else:
            self.fail_count += 1
            self.success_count = 0
            # 失败时增加延迟
            self.adaptive_delay = min(self.max_delay * 2, self.adaptive_delay * 1.5)
        
        # 确保最小间隔
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_delay:
            additional_delay = self.min_delay - time_since_last
        else:
            additional_delay = 0
        
        # 计算总延迟
        base_delay = random.uniform(self.adaptive_delay * 0.8, self.adaptive_delay * 1.2)
        total_delay = base_delay + additional_delay
        
        # 记录请求时间
        self.last_request_time = current_time + total_delay
        
        return total_delay

    def extract_person_info(self, soup) -> Dict:
        """提取个人信息"""
        person_info = {}
        try:
            # 查找姓名
            name_selectors = [
                'h1.name', 'h1[class*="name"]', '.person-name', '.full-name',
                'h1', 'h2.name', '.profile-name'
            ]
            
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem and name_elem.get_text().strip():
                    person_info['姓名'] = name_elem.get_text().strip()
                    break
            
            # 查找年龄
            age_selectors = [
                '.age', '[class*="age"]', '.person-age'
            ]
            
            for selector in age_selectors:
                age_elem = soup.select_one(selector)
                if age_elem:
                    age_text = age_elem.get_text().strip()
                    age_match = re.search(r'(\d+)', age_text)
                    if age_match:
                        person_info['年龄'] = age_match.group(1)
                        break
            
            # 查找手机号码
            phone_selectors = [
                '.phone', '[class*="phone"]', '.contact-phone'
            ]
            
            for selector in phone_selectors:
                phone_elem = soup.select_one(selector)
                if phone_elem:
                    phone_text = phone_elem.get_text().strip()
                    phone_match = re.search(r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})', phone_text)
                    if phone_match:
                        person_info['手机'] = phone_match.group(1)
                        break
            
            # 查找地址
            address_selectors = [
                '.address', '[class*="address"]', '.location'
            ]
            
            for selector in address_selectors:
                addr_elem = soup.select_one(selector)
                if addr_elem and addr_elem.get_text().strip():
                    person_info['地址'] = addr_elem.get_text().strip()
                    break
                    
        except Exception as e:
            logging.error(f"提取个人信息失败: {e}")
        
        return person_info

    def extract_relatives_info(self, soup, min_age: int = 40) -> List[Dict]:
        """提取亲属信息"""
        relatives = []
        try:
            # 查找亲属相关的容器
            relative_selectors = [
                '.relatives', '.family-members', '.related-people',
                '[class*="relative"]', '[class*="family"]'
            ]
            
            for selector in relative_selectors:
                container = soup.select_one(selector)
                if container:
                    # 在容器中查找个人条目
                    person_items = container.select('.person, .member, .relative-item, li, div')
                    
                    for item in person_items:
                        relative_info = {}
                        item_text = item.get_text()
                        
                        # 提取姓名
                        name_elem = item.select_one('.name, .person-name, a, strong, b')
                        if name_elem:
                            relative_info['姓名'] = name_elem.get_text().strip()
                        
                        # 提取年龄
                        age_match = re.search(r'(\d+)', item_text)
                        if age_match:
                            age = int(age_match.group(1))
                            if age >= min_age:
                                relative_info['年龄'] = str(age)
                            else:
                                continue  # 跳过年龄不符合的
                        
                        # 提取链接
                        link_elem = item.select_one('a[href]')
                        if link_elem:
                            relative_info['链接'] = link_elem.get('href')
                        
                        if relative_info.get('姓名'):
                            relatives.append(relative_info)
                    
                    break  # 找到一个有效容器就停止
                    
        except Exception as e:
            logging.error(f"提取亲属信息失败: {e}")
        
        return relatives

    def find_background_report_button(self, sb) -> Tuple[bool, str]:
        """查找背景报告按钮"""
        try:
            # 使用SeleniumBase的方法查找按钮
            button_selectors = [
                'a.btn.btn-primary.btn-sm.btn-block.text-center',
                'a[class*="btn"][class*="primary"]',
                'a:contains("Open Free Background Report")',
                'a:contains("Free Background Report")',
                'a[href*="/people/"]'
            ]

            for selector in button_selectors:
                try:
                    if sb.is_element_visible(selector, timeout=2):
                        href = sb.get_attribute(selector, "href")
                        if href:
                            # 转换为绝对URL
                            if href.startswith('/'):
                                base_url = sb.get_current_url().split('/')[0] + '//' + sb.get_current_url().split('/')[2]
                                href = base_url + href
                            logging.info(f"找到背景报告按钮: {href}")
                            return True, href
                except Exception as e:
                    logging.debug(f"检查选择器 {selector} 失败: {e}")
                    continue

            # 如果没有找到，尝试从页面源码中解析
            page_source = sb.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            # 查找包含"Free Background Report"文本的链接
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                link_text = link.get_text().strip()
                href = link.get('href', '')

                if any(keyword in link_text for keyword in ['Open Free Background Report', 'Free Background Report']):
                    if href.startswith('/'):
                        base_url = sb.get_current_url().split('/')[0] + '//' + sb.get_current_url().split('/')[2]
                        href = base_url + href
                    logging.info(f"通过页面源码找到背景报告链接: {href}")
                    return True, href

            logging.warning("未找到背景报告按钮")
            return False, ""

        except Exception as e:
            logging.error(f"查找背景报告按钮失败: {e}")
            return False, ""

    def click_relative_and_extract_details(self, sb, relative_name: str) -> Dict:
        """点击亲属链接并提取详细信息"""
        details = {'手机': '', '地址': ''}

        try:
            # 查找包含亲属姓名的链接
            link_selectors = [
                f'a:contains("{relative_name}")',
                f'a[title*="{relative_name}"]',
                f'a[href*="{relative_name.replace(" ", "-").lower()}"]'
            ]

            for selector in link_selectors:
                try:
                    if sb.is_element_visible(selector, timeout=1):
                        # 使用SeleniumBase的UC模式点击
                        sb.uc_click(selector, timeout=3)
                        sb.sleep(1)  # 等待页面加载

                        # 提取详细信息
                        page_source = sb.get_page_source()
                        soup = BeautifulSoup(page_source, 'html.parser')

                        # 查找手机号码
                        phone_patterns = [
                            r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
                            r'\((\d{3})\)\s*(\d{3})[-.\s]?(\d{4})'
                        ]

                        for pattern in phone_patterns:
                            phone_match = re.search(pattern, page_source)
                            if phone_match:
                                details['手机'] = phone_match.group(0)
                                break

                        # 查找地址
                        address_selectors = [
                            '.address', '[class*="address"]', '.location',
                            '.contact-info', '[class*="contact"]'
                        ]

                        for addr_selector in address_selectors:
                            addr_elem = soup.select_one(addr_selector)
                            if addr_elem and addr_elem.get_text().strip():
                                details['地址'] = addr_elem.get_text().strip()
                                break

                        # 返回上一页
                        sb.go_back()
                        sb.sleep(0.5)

                        break

                except Exception as e:
                    logging.debug(f"尝试选择器 {selector} 失败: {e}")
                    continue

        except Exception as e:
            logging.error(f"提取亲属 {relative_name} 详细信息失败: {e}")

        return details

    def scrape_phone_data(self, sb, phone: str) -> Tuple[bool, Dict]:
        """
        使用SeleniumBase爬取单个电话号码的背景报告数据

        Args:
            sb: SeleniumBase实例
            phone: 10位电话号码

        Returns:
            (成功标志, 数据字典)
        """
        start_time = time.time()
        logging.info("=" * 80)
        logging.info(f"🔍 开始处理电话号码: {phone}")

        try:
            # 自适应延迟（基于上次结果）
            delay = self.calculate_adaptive_delay(True)  # 先假设会成功
            logging.info(f"⏰ 智能延迟: {delay:.2f}秒 (自适应延迟: {self.adaptive_delay:.2f})")
            sb.sleep(delay)

            # 构造URL
            target_url = f"https://www.smartbackgroundchecks.com/phone/{phone}"
            logging.info(f"目标URL: {target_url}")

            # 使用SeleniumBase UC Mode打开页面
            sb.uc_open_with_reconnect(target_url, reconnect_time=3)

            # 使用SeleniumBase内置的CAPTCHA处理
            try:
                sb.uc_gui_handle_captcha()  # 自动处理CAPTCHA
            except Exception as e:
                logging.debug(f"CAPTCHA处理: {e}")

            # 等待页面加载完成
            sb.wait_for_ready_state_complete(timeout=5)

            # 查找背景报告按钮
            button_found, button_url = self.find_background_report_button(sb)

            if not button_found:
                logging.warning(f"未找到背景报告按钮: {phone}")
                self.calculate_adaptive_delay(False)  # 更新失败状态
                return False, {}

            logging.info(f"找到背景报告链接: {button_url}")

            # 访问背景报告页面
            sb.uc_open_with_reconnect(button_url, reconnect_time=2)

            # 再次处理可能的CAPTCHA
            try:
                sb.uc_gui_handle_captcha()
            except Exception as e:
                logging.debug(f"背景报告页面CAPTCHA处理: {e}")

            # 等待页面内容加载
            sb.wait_for_ready_state_complete(timeout=5)
            sb.sleep(1)  # 额外等待确保内容完全加载

            # 获取页面源码并解析
            page_source = sb.get_page_source()
            soup = BeautifulSoup(page_source, 'html.parser')

            # 提取个人信息
            person_info = self.extract_person_info(soup)
            if not person_info.get('姓名'):
                logging.warning(f"未提取到个人信息: {phone}")
                self.calculate_adaptive_delay(False)  # 更新失败状态
                return False, {}

            # 立即保存个人信息
            self.save_to_csv(phone, '本人', person_info)

            # 提取亲属信息
            relatives_info = self.extract_relatives_info(soup, min_age=40)
            logging.info(f"找到 {len(relatives_info)} 个符合条件的亲属")

            # 处理亲属信息并实时保存
            detailed_relatives_count = 0
            for i, relative in enumerate(relatives_info):
                relative_name = relative.get('姓名', '')
                if relative_name:
                    try:
                        logging.info(f"处理亲属 {i+1}/{len(relatives_info)}: {relative_name}")

                        # 获取亲属详细信息
                        relative_details = self.click_relative_and_extract_details(sb, relative_name)

                        combined_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': relative_details.get('手机', ''),
                            '地址': relative_details.get('地址', '')
                        }

                        # 立即保存每个亲属信息
                        if self.save_to_csv(phone, '亲属', combined_info):
                            detailed_relatives_count += 1

                        # 智能延迟
                        has_contact_info = combined_info.get('手机') or combined_info.get('地址')
                        if has_contact_info:
                            sb.sleep(random.uniform(0.5, 1.0))
                        else:
                            sb.sleep(random.uniform(0.2, 0.5))

                    except Exception as e:
                        logging.error(f"处理亲属 {relative_name} 失败: {e}")
                        # 保存基本信息
                        basic_info = {
                            '姓名': relative.get('姓名', ''),
                            '年龄': relative.get('年龄', ''),
                            '关系': relative.get('关系', '亲属'),
                            '链接': relative.get('链接', ''),
                            '手机': '',
                            '地址': ''
                        }
                        self.save_to_csv(phone, '亲属', basic_info)

            duration = time.time() - start_time
            logging.info(f"✅ 电话号码 {phone} 处理成功，耗时: {duration:.2f}秒")
            logging.info(f"个人信息: {person_info.get('姓名', 'N/A')}")
            logging.info(f"成功保存亲属数量: {detailed_relatives_count}")

            # 更新成功状态
            self.calculate_adaptive_delay(True)

            return True, {
                'phone': phone,
                'person_info': person_info,
                'relatives_count': detailed_relatives_count,
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"❌ 处理电话号码 {phone} 失败，耗时: {duration:.2f}秒")
            logging.error(f"错误详情: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")

            # 更新失败状态
            self.calculate_adaptive_delay(False)

            return False, {}

    def run_seleniumbase_batch_scraping(self, max_phones: int = None,
                                      base_delay: float = 2.0,
                                      use_incognito: bool = True) -> Dict:
        """
        运行SeleniumBase批量爬取

        Args:
            max_phones: 最大处理电话号码数量，None表示处理所有
            base_delay: 基础延迟时间（秒）
            use_incognito: 是否使用隐身模式

        Returns:
            统计结果字典
        """
        self.stats['start_time'] = datetime.now()
        self.min_delay = base_delay
        self.max_delay = base_delay * 2.5
        self.adaptive_delay = base_delay

        logging.info("🚀 开始SeleniumBase批量爬取任务")
        logging.info(f"基础延迟: {base_delay}秒")
        logging.info(f"隐身模式: {'启用' if use_incognito else '禁用'}")

        # 加载电话号码
        phones = self.load_phone_numbers()
        if not phones:
            logging.error("没有可处理的电话号码")
            return self.stats

        # 过滤已处理的电话号码
        unprocessed_phones = [p for p in phones if p not in self.processed_phones]
        logging.info(f"总电话号码: {len(phones)}, 未处理: {len(unprocessed_phones)}")

        if not unprocessed_phones:
            logging.info("所有电话号码都已处理完成")
            return self.stats

        # 限制处理数量
        if max_phones:
            unprocessed_phones = unprocessed_phones[:max_phones]
            logging.info(f"限制处理数量: {max_phones}")

        self.stats['total_phones'] = len(unprocessed_phones)

        # 使用SeleniumBase UC Mode
        try:
            with SB(uc=True, incognito=use_incognito, test=True,
                   ad_block=True, block_images=True) as sb:

                logging.info("SeleniumBase UC Mode 初始化成功")

                # 处理每个电话号码
                for i, phone in enumerate(unprocessed_phones, 1):
                    self.stats['current_phone'] = phone
                    self.stats['processed_phones'] = i

                    logging.info(f"\n📞 处理进度: {i}/{len(unprocessed_phones)} ({i/len(unprocessed_phones)*100:.1f}%)")

                    try:
                        success, result_data = self.scrape_phone_data(sb, phone)

                        if success:
                            self.stats['successful_scrapes'] += 1
                            self.save_processed_phone(phone)
                            logging.info(f"✅ 成功处理: {phone}")
                        else:
                            self.stats['failed_scrapes'] += 1
                            logging.error(f"❌ 处理失败: {phone}")

                    except Exception as e:
                        self.stats['failed_scrapes'] += 1
                        logging.error(f"❌ 处理电话号码 {phone} 时发生异常: {e}")
                        logging.error(f"异常堆栈: {traceback.format_exc()}")

                    # 打印当前统计
                    self.print_stats()

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在优雅退出...")
        except Exception as e:
            logging.error(f"SeleniumBase批量处理过程中发生严重错误: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")

        # 最终统计
        end_time = datetime.now()
        duration = (end_time - self.stats['start_time']).total_seconds()

        logging.info("=" * 80)
        logging.info("🎯 SeleniumBase批量爬取任务完成")
        logging.info(f"总耗时: {duration:.2f}秒 ({duration/60:.1f}分钟)")
        logging.info(f"处理电话号码: {self.stats['processed_phones']}")
        logging.info(f"成功: {self.stats['successful_scrapes']}")
        logging.info(f"失败: {self.stats['failed_scrapes']}")
        if self.stats['processed_phones'] > 0:
            success_rate = self.stats['successful_scrapes']/self.stats['processed_phones']*100
            avg_time = duration / self.stats['processed_phones']
            logging.info(f"成功率: {success_rate:.1f}%")
            logging.info(f"平均处理时间: {avg_time:.2f}秒/个")
        logging.info(f"最终自适应延迟: {self.adaptive_delay:.2f}秒")
        logging.info("=" * 80)

        return self.stats

    def print_stats(self):
        """打印当前统计信息"""
        if self.stats['start_time']:
            elapsed = (datetime.now() - self.stats['start_time']).total_seconds()
            logging.info(f"📊 当前统计 - 已处理: {self.stats['processed_phones']}/{self.stats['total_phones']}, "
                        f"成功: {self.stats['successful_scrapes']}, 失败: {self.stats['failed_scrapes']}, "
                        f"自适应延迟: {self.adaptive_delay:.2f}s, 耗时: {elapsed:.0f}秒")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='SeleniumBase批量电话号码背景报告爬取器')
    parser.add_argument('--phone-file', default='KK1000.txt', help='电话号码文件路径')
    parser.add_argument('--output-dir', default='scraped_data', help='输出目录')
    parser.add_argument('--max-phones', type=int, help='最大处理电话号码数量')
    parser.add_argument('--delay', type=float, default=2.0, help='基础延迟时间（秒）')
    parser.add_argument('--no-incognito', action='store_true', help='禁用隐身模式')

    args = parser.parse_args()

    try:
        # 创建SeleniumBase爬取器
        scraper = SeleniumBaseBatchScraper(args.phone_file, args.output_dir)

        # 运行SeleniumBase批量爬取
        stats = scraper.run_seleniumbase_batch_scraping(
            max_phones=args.max_phones,
            base_delay=args.delay,
            use_incognito=not args.no_incognito
        )

        # 退出码
        if stats['successful_scrapes'] > 0:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
